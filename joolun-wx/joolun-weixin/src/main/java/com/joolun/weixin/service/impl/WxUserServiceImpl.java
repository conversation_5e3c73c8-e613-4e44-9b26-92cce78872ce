package com.joolun.weixin.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.joolun.common.exception.CustomException;
import com.joolun.common.utils.validation.CheckUtils;
import com.joolun.weixin.config.WxMaConfiguration;
import com.joolun.weixin.constant.WxMaConstants;
import com.joolun.framework.utils.ThirdSession;
import com.joolun.weixin.entity.WxOpenDataDTO;
import com.joolun.weixin.entity.WxUserLevel;
import com.joolun.weixin.entity.dto.WxUserDTO;
import com.joolun.weixin.handler.SubscribeHandler;
import com.joolun.weixin.mapper.WxUserMapper;
import com.joolun.weixin.service.IWxUserLevelService;
import com.joolun.weixin.service.WxUserService;
import com.joolun.weixin.constant.ConfigConstant;
import com.joolun.weixin.entity.WxUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpUserService;
import me.chanjar.weixin.mp.api.WxMpUserTagService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 微信用户
 *
 * <AUTHOR>
 * @date 2019-03-25 15:39:39
 */
@Slf4j
@Service
@AllArgsConstructor
public class WxUserServiceImpl extends ServiceImpl<WxUserMapper, WxUser> implements WxUserService {

	private final WxMpService wxService;
	private final RedisTemplate redisTemplate;

	private final IWxUserLevelService wxUserLevelService;

	private final WxUserMapper wxUserMapper;
	private final CheckUtils checkUtils;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateRemark(WxUser entity) throws WxErrorException {
		String id = entity.getId();
		String remark = entity.getRemark();
		String openId = entity.getOpenId();
		entity = new WxUser();
		entity.setId(id);
		entity.setRemark(remark);
		super.updateById(entity);
		WxMpUserService wxMpUserService = wxService.getUserService();
		wxMpUserService.userUpdateRemark(openId,remark);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void tagging(String taggingType,Long tagId,String[] openIds) throws WxErrorException {
		WxMpUserTagService wxMpUserTagService = wxService.getUserTagService();
		WxUser wxUser;
		if("tagging".equals(taggingType)){
			for(String openId : openIds){
				wxUser = baseMapper.selectOne(Wrappers.<WxUser>lambdaQuery()
						.eq(WxUser::getOpenId,openId));
				Long[] tagidList = wxUser.getTagidList();
				List<Long> list = Arrays.asList(tagidList);
				list = new ArrayList<>(list);
				if(!list.contains(tagId)){
					list.add(tagId);
					tagidList = list.toArray(new Long[list.size()]);
					wxUser.setTagidList(tagidList);
					this.updateById(wxUser);
				}
			}
			wxMpUserTagService.batchTagging(tagId,openIds);
		}
		if("unTagging".equals(taggingType)){
			for(String openId : openIds){
				wxUser = baseMapper.selectOne(Wrappers.<WxUser>lambdaQuery()
						.eq(WxUser::getOpenId,openId));
				Long[] tagidList = wxUser.getTagidList();
				List<Long> list = Arrays.asList(tagidList);
				list = new ArrayList<>(list);
				if(list.contains(tagId)){
					list.remove(tagId);
					tagidList = list.toArray(new Long[list.size()]);
					wxUser.setTagidList(tagidList);
					this.updateById(wxUser);
				}
			}
			wxMpUserTagService.batchUntagging(tagId,openIds);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void synchroWxUser() throws WxErrorException {
		//先将已关注的用户取关
		WxUser wxUser = new WxUser();
		wxUser.setSubscribe(ConfigConstant.SUBSCRIBE_TYPE_NO);
		this.baseMapper.update(wxUser, Wrappers.<WxUser>lambdaQuery()
				.eq(WxUser::getSubscribe, ConfigConstant.SUBSCRIBE_TYPE_YES));
		WxMpUserService wxMpUserService = wxService.getUserService();
		this.recursionGet(wxMpUserService,null);
	}

	/**
	 * 递归获取
	 * @param nextOpenid
	 */
	void recursionGet(WxMpUserService wxMpUserService,String nextOpenid) throws WxErrorException {
		WxMpUserList userList = wxMpUserService.userList(nextOpenid);
		List<WxUser> listWxUser = new ArrayList<>();
		List<WxMpUser> listWxMpUser = getWxMpUserList(wxMpUserService,userList.getOpenids());
		listWxMpUser.forEach(wxMpUser -> {
			WxUser wxUser = baseMapper.selectOne(Wrappers.<WxUser>lambdaQuery()
					.eq(WxUser::getOpenId,wxMpUser.getOpenId()));
			if(wxUser == null){//用户未存在
				wxUser = new WxUser();
				wxUser.setSubscribeNum(1);
			}
			SubscribeHandler.setWxUserValue(wxUser,wxMpUser);
			listWxUser.add(wxUser);
		});
		this.saveOrUpdateBatch(listWxUser);
		if(userList.getCount() >= 10000){
			this.recursionGet(wxMpUserService,userList.getNextOpenid());
		}
	}

	/**
	 * 分批次获取微信粉丝信息 每批100条
	 * @param wxMpUserService
	 * @param openidsList
	 * @return
	 * @throws WxErrorException
	 * <AUTHOR>
	private List<WxMpUser> getWxMpUserList(WxMpUserService wxMpUserService, List<String> openidsList) throws WxErrorException {
		// 粉丝openid数量
		int count = openidsList.size();
		if (count <= 0) {
			return new ArrayList<>();
		}
		List<WxMpUser> list = Lists.newArrayList();
		List<WxMpUser> followersInfoList;
		int a = count % 100 > 0 ? count / 100 + 1 : count / 100;
		for (int i = 0; i < a; i++) {
			if (i + 1 < a) {
//				log.debug("i:{},from:{},to:{}", i, i * 100, (i + 1) * 100);
				followersInfoList = wxMpUserService.userInfoList(openidsList.subList(i * 100, ((i + 1) * 100)));
				if (null != followersInfoList && !followersInfoList.isEmpty()) {
					list.addAll(followersInfoList);
				}
			}
			else {
//				log.debug("i:{},from:{},to:{}", i, i * 100, count - i * 100);
				followersInfoList = wxMpUserService.userInfoList(openidsList.subList(i * 100, count));
				if (null != followersInfoList && !followersInfoList.isEmpty()) {
					list.addAll(followersInfoList);
				}
			}
		}
//		log.debug("本批次获取微信粉丝数：",list.size());
		return list;
	}

	@Override
	public WxUser getByOpenId(String openId){
		return this.baseMapper.selectOne(Wrappers.<WxUser>lambdaQuery()
				.eq(WxUser::getOpenId,openId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public WxUser loginMa(String appId, String jsCode) throws WxErrorException {
		WxMaJscode2SessionResult jscode2session = WxMaConfiguration.getMaService(appId).jsCode2SessionInfo(jsCode);
		WxUser wxUser = this.getByOpenId(jscode2session.getOpenid());
		if(wxUser==null) {
			//新增微信用户
			wxUser = new WxUser();
			wxUser.setAppType(ConfigConstant.WX_APP_TYPE_1);
			wxUser.setOpenId(jscode2session.getOpenid());
			wxUser.setSessionKey(jscode2session.getSessionKey());
			wxUser.setUnionId(jscode2session.getUnionid());
			try{
				Integer maxInviteCode = wxUserMapper.getMaxInviteCode();
//			maxInviteCode=maxInviteCode++;
				wxUser.setInviteCode(maxInviteCode.toString());
				this.save(wxUser);
			}catch (DuplicateKeyException e){
				if(e.getMessage().contains("uk_appid_openid")){
					wxUser = this.getByOpenId(wxUser.getOpenId());
				}
			}
		}else {
			//更新SessionKey
			wxUser.setAppType(ConfigConstant.WX_APP_TYPE_1);
			wxUser.setOpenId(jscode2session.getOpenid());
			wxUser.setSessionKey(jscode2session.getSessionKey());
			wxUser.setUnionId(jscode2session.getUnionid());
			this.updateById(wxUser);
		}

		String thirdSessionKey = UUID.randomUUID().toString();
		ThirdSession thirdSession = new ThirdSession();
		thirdSession.setAppId(appId);
		thirdSession.setSessionKey(wxUser.getSessionKey());
		thirdSession.setWxUserId(wxUser.getId());
		thirdSession.setOpenId(wxUser.getOpenId());
		//将3rd_session和用户信息存入redis，并设置过期时间
		String key = WxMaConstants.THIRD_SESSION_BEGIN + ":" + thirdSessionKey;
		redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(thirdSession) , WxMaConstants.TIME_OUT_SESSION, TimeUnit.HOURS);
		wxUser.setSessionKey(thirdSessionKey);
		return wxUser;
	}

  @Override
  @Transactional(rollbackFor = Exception.class)
  public WxUser saveOrUptateWxUser(WxOpenDataDTO wxOpenDataDTO) {
    try {
      log.info(
          "开始处理用户数据解密，appId: {}, userId: {}", wxOpenDataDTO.getAppId(), wxOpenDataDTO.getUserId());

      WxMaUserService wxMaUserService =
          WxMaConfiguration.getMaService(wxOpenDataDTO.getAppId()).getUserService();
      log.debug("wxMaUserService:{}", JSON.toJSONString(wxMaUserService));

      // 检查参数有效性
      if (wxOpenDataDTO.getSessionKey() == null || wxOpenDataDTO.getSessionKey().trim().isEmpty()) {
        log.error("sessionKey为空，无法解密用户数据");
        throw new CustomException("sessionKey为空，无法解密用户数据");
      }

      if (wxOpenDataDTO.getEncryptedData() == null
          || wxOpenDataDTO.getEncryptedData().trim().isEmpty()) {
        log.error("encryptedData为空，无法解密用户数据");
        throw new CustomException("encryptedData为空，无法解密用户数据");
      }

      if (wxOpenDataDTO.getIv() == null || wxOpenDataDTO.getIv().trim().isEmpty()) {
        log.error("iv为空，无法解密用户数据");
        throw new CustomException("iv为空，无法解密用户数据");
      }

      WxMaUserInfo wxMaUserInfo =
          wxMaUserService.getUserInfo(
              wxOpenDataDTO.getSessionKey(),
              wxOpenDataDTO.getEncryptedData(),
              wxOpenDataDTO.getIv());
      log.info("解密结果 - wxMaUserInfo是否为null: {}", wxMaUserInfo == null);

      if (wxMaUserInfo == null) {
        log.error("用户信息解密失败，可能原因：1.sessionKey已过期 2.encryptedData与sessionKey不匹配 3.iv参数错误");
        throw new CustomException("用户信息解密失败，请重新授权");
      }

      log.info(
          "解密成功 - 用户昵称: {}, 性别: {}, 城市: {}",
          wxMaUserInfo.getNickName(),
          wxMaUserInfo.getGender(),
          wxMaUserInfo.getCity());
      log.debug("完整用户信息:{}", JSON.toJSONString(wxMaUserInfo));

      WxUser wxUser = new WxUser();
      wxUser.setId(wxOpenDataDTO.getUserId());
      wxUser.setSex(wxMaUserInfo.getGender());
      wxUser.setCity(wxMaUserInfo.getCity());
      wxUser.setNickName(wxMaUserInfo.getNickName());
      wxUser.setHeadimgUrl(wxMaUserInfo.getAvatarUrl());

      // 上级邀请码不为空时
      if (StringUtils.isNotBlank(wxOpenDataDTO.getUpperInviteCode())) {
        WxUser upperUser =
            this.baseMapper.selectOne(
                Wrappers.<WxUser>lambdaQuery()
                    .eq(WxUser::getInviteCode, wxOpenDataDTO.getUpperInviteCode())); // 根据邀请码查询用户
        if (upperUser != null) {
          wxUser.setUpperUser(upperUser.getId());
          List<WxUser> wxUsers =
              this.baseMapper.selectList(
                  Wrappers.<WxUser>lambdaQuery().eq(WxUser::getUpperUser, upperUser.getId()));
          List<WxUserLevel> wxUserLevels =
              wxUserLevelService.selectWxUserLevelList(new WxUserLevel());
          for (WxUserLevel wxUserLevel : wxUserLevels) {
            if (wxUsers.size() == wxUserLevel.getInvitationCount()) {
              upperUser.setLevelId(wxUserLevel.getId());
            }
            ;
          }
        } else {
          log.warn("未找到邀请码对应的用户: {}", wxOpenDataDTO.getUpperInviteCode());
        }
      }
      baseMapper.updateById(wxUser);
      wxUser = baseMapper.selectById(wxUser.getId());
      log.info("用户数据更新成功，用户ID: {}", wxUser.getId());
      return wxUser;
    } catch (Exception e) {
      log.error("处理用户数据时发生异常: {}", e.getMessage(), e);
      throw new CustomException("处理用户数据失败: " + e.getMessage(), e);
    }
  }

	@Override
	public WxUser getUpperUserByOpenId(String openId) {
		return this.baseMapper.selectOne(Wrappers.<WxUser>lambdaQuery()
				.eq(WxUser::getUpperUser,openId));
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public WxUser saveOrUpdateWxUser2(WxUser wxUser) {
//		//上级邀请码不为空时
//		if(StringUtils.isNotBlank(wxUser.getUpperUser())){
//			WxUser upperUser = this.baseMapper.selectOne(Wrappers.<WxUser>lambdaQuery().eq(WxUser::getInviteCode, wxUser.getUpperInviteCode()));//根据邀请码查询用户
//			wxUser.setUpperUser(upperUser.getId());
//			List<WxUser> wxUsers = this.baseMapper.selectList(Wrappers.<WxUser>lambdaQuery().eq(WxUser::getUpperUser, upperUser.getId()));
//			List<WxUserLevel> wxUserLevels = wxUserLevelService.selectWxUserLevelList(new WxUserLevel());
//			for (WxUserLevel wxUserLevel : wxUserLevels) {
//				if(wxUsers.size()==wxUserLevel.getInvitationCount()){
//					upperUser.setLevelId(wxUserLevel.getId());
//				};
//			}
//			baseMapper.updateWxUserLevel(upperUser);
//		}
		baseMapper.updateById(wxUser);
		wxUser = baseMapper.selectById(wxUser.getId());
		return wxUser;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public WxUser updateWxUser(WxUserDTO wxUserDTO) {
		checkUtils.throwIfNull(wxUserDTO.getId(), "用户ID不能为空");
		WxUser wxUser =  baseMapper.selectById(wxUserDTO.getId());
		checkUtils.throwIfNull(wxUser, "用户不存在");
		if(ObjectUtil.isNotNull(wxUserDTO.getPhone())){
			wxUser.setPhone(wxUserDTO.getPhone());
		}
		if(ObjectUtil.isNotNull(wxUserDTO.getNickName())){
			wxUser.setNickName(wxUserDTO.getNickName());
		}
		if(ObjectUtil.isNotNull(wxUserDTO.getHeadimgUrl())){
			wxUser.setHeadimgUrl(wxUserDTO.getHeadimgUrl());
		}
		baseMapper.updateById(wxUser);
		return wxUser;
	}

	@Override
	public int updateWxUserLv(WxUser wxUser) {
		return wxUserMapper.updateWxUserLevel(wxUser);
	}

	@Override
	public IPage<WxUser> page1(IPage<WxUser> page,String upperUserId) {
		return wxUserMapper.selectPage1(page,upperUserId);
	}

	@Override
	public IPage<WxUser> selectFollowMePage(IPage<WxUser> page, String userId) {
		return wxUserMapper.selectFollowMePage(page,userId);
	}

	@Override
	public IPage<WxUser> selectMyFollowPage(IPage<WxUser> page, String userId) {
		return wxUserMapper.selectMyFollowPage(page,userId);
	}

	@Override
	public int updateSignature(String userId, String signature) {
		return wxUserMapper.updateSignature(userId,signature);
	}

	@Override
	public IPage<WxUser> getInvitedUsers(IPage<WxUser> page, String userId) {
		return this.page(page, Wrappers.<WxUser>lambdaQuery()
				.eq(WxUser::getUpperUser, userId)
				.orderByDesc(WxUser::getCreateTime));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean cancelInvitation(String userId, String invitedUserId) {
		// 验证邀请关系是否存在
		WxUser invitedUser = this.getById(invitedUserId);
		if (invitedUser == null || !userId.equals(invitedUser.getUpperUser())) {
			return false;
		}
		
		// 取消邀请关系，将被邀请用户的upperUser字段置空
		WxUser updateUser = new WxUser();
		updateUser.setId(invitedUserId);
		updateUser.setUpperUser(null);
		return this.updateById(updateUser);
	}
}
