/*
MIT License

Copyright (c) 2020 www.joolun.com

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/
package com.joolun.weixin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.weixin.entity.WxOpenDataDTO;
import com.joolun.weixin.entity.WxUser;
import com.joolun.weixin.entity.dto.WxUserDTO;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * 微信用户
 *
 * <AUTHOR>
 * @date 2019-03-25 15:39:39
 */
public interface WxUserService extends IService<WxUser> {

	/**
	 * 同步微信用户
	 */
	void synchroWxUser() throws WxErrorException;

	/**
	 * 修改用户备注
	 * @param entity
	 * @return
	 */
	boolean updateRemark(WxUser entity) throws WxErrorException;

	/**
	 * 认识标签
	 * @param taggingType
	 * @param tagId
	 * @param openIds
	 * @throws WxErrorException
	 */
	void tagging(String taggingType, Long tagId, String[] openIds) throws WxErrorException;

	/**
	 * 根据openId获取用户
	 * @param openId
	 * @return
	 */
	WxUser getByOpenId(String openId);

	/**
	 * 小程序登录
	 * @param appId
	 * @param jsCode
	 * @return
	 */
	WxUser loginMa(String appId, String jsCode) throws WxErrorException;

	/**
	 * 新增、更新微信用户
	 * @param wxOpenDataDTO
	 * @return
	 */
	WxUser saveOrUptateWxUser(WxOpenDataDTO wxOpenDataDTO);


	/**
	 * 根据openId获取用户
	 * @param openId
	 * @return
	 */
	WxUser getUpperUserByOpenId(String openId);


	WxUser saveOrUpdateWxUser2(WxUser wxUser);

	WxUser updateWxUser(WxUserDTO wxUserDTO);

	int updateWxUserLv(WxUser wxUser);

	IPage<WxUser> page1(IPage<WxUser> page, String upperUserId);


	IPage<WxUser> selectFollowMePage(IPage<WxUser> page, String userId);
	IPage<WxUser> selectMyFollowPage(IPage<WxUser> page, String userId);

	int updateSignature(String userId,String signature);

	/**
	 * 获取被邀请用户列表
	 * @param userId 邀请者用户ID
	 * @param page 分页参数
	 * @return 被邀请用户分页列表
	 */
	IPage<WxUser> getInvitedUsers(IPage<WxUser> page, String userId);

	/**
	 * 取消邀请关系
	 * @param userId 邀请者用户ID
	 * @param invitedUserId 被邀请用户ID
	 * @return 操作结果
	 */
	boolean cancelInvitation(String userId, String invitedUserId);
}
