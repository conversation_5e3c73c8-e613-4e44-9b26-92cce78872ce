package com.joolun.weixin.config;

import com.joolun.weixin.interceptor.ThirdSessionInterceptor;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/** web配置 */
@Configuration
@AllArgsConstructor
public class WebConfig implements WebMvcConfigurer {
  private final ThirdSessionInterceptor thirdSessionInterceptor; // 直接注入拦截器Bean

  /**
   * 拦截器
   *
   * @param registry
   */
  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    /** 进入ThirdSession拦截器 */
    registry
        .addInterceptor(thirdSessionInterceptor)
        .addPathPatterns("/weixin/api/**") // 拦截/api/**接口
        .excludePathPatterns(
            "/weixin/api/ma/wxuser/login",
            "/weixin/api/ma/orderinfo/notify-order",
            "/weixin/api/ma/orderinfo/notify-logisticsr",
            "/weixin/api/ma/orderinfo/notify-refunds",
            "/weixin/api/ma/tyOrderInfo/notify-logisticsr",
            "/weixin/api/ma/tyOrderInfo/notify-order",
            "/weixin/api/ma/tyOrderInfo/refund-order",
            "/weixin/api/ma/userLvOrder/notify-order",
            "/weixin/api/ma/userLvOrder/refund-order",
            "/weixin/api/ma/routes/**",
            "/weixin/api/ma/sysdict/**",
            "/weixin/api/ma/notice/**",
            "/weixin/api/ma/city/**",
            "/weixin/api/ma/goodsspu/page",
            //						"/weixin/api/ma/goodsspu/**",
            "/weixin/api/ma/goodscategory/**",
            //						"/weixin/api/ma/userCollect/check",
            "/weixin/api/ma/goodsevaluate/**",
            "/weixin/api/ma/file/upload",        // 只排除普通上传接口
            "/weixin/api/ma/file/remove",        // 只排除删除接口
            "/weixin/api/ma/articleInfo/page",
            "/weixin/api/ma/sysconfig/**",
            "/weixin/api/ma/labelinfo/**",
            "/mall/travel/register"); // 放行接口
  }
}
