package com.joolun.weixin.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.weixin.config.CommonConstants;
import com.joolun.weixin.constant.ConfigConstant;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.weixin.constant.WxMaConstants;
import com.joolun.framework.utils.ThirdSession;
import com.joolun.framework.utils.ThirdSessionHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.beans.factory.annotation.Value;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * ThirdSession拦截器，校验每个请求的ThirdSession
 *
 * <AUTHOR>
@Slf4j
@Component
public class ThirdSessionInterceptor extends HandlerInterceptorAdapter {

  @Value("${spring.profiles.active}")
  private String active;

  private final RedisTemplate redisTemplate;

  public ThirdSessionInterceptor(RedisTemplate redisTemplate) {
    this.redisTemplate = redisTemplate;
  }

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws Exception {
    // 简单直接的开发环境判断
    log.info("ThirdSessionInterceptor 是否为开发环境: {}", active);

    // 开发环境下提供默认用户会话
    if ("dev".equals(active)) {
      log.info("开发环境模式，使用默认用户会话");
      ThirdSession defaultThirdSession = createDefaultThirdSession();
      ThirdSessionHolder.setThirdSession(defaultThirdSession);
      return Boolean.TRUE;
    }

    // 获取header中的thirdSession
    String thirdSessionHeader = request.getHeader(ConfigConstant.HEADER_THIRDSESSION);
    if (StrUtil.isNotBlank(thirdSessionHeader)) {
      // 获取缓存中的ThirdSession
      String key = WxMaConstants.THIRD_SESSION_BEGIN + ":" + thirdSessionHeader;
      Object thirdSessionObj = redisTemplate.opsForValue().get(key);
      if (thirdSessionObj == null) { // session过期
        AjaxResult r =
            AjaxResult.error(MyReturnCode.ERR_60001.getCode(), MyReturnCode.ERR_60001.getMsg());
        this.writerPrint(response, r);
        return Boolean.FALSE;
      } else {
        String thirdSessionStr = String.valueOf(thirdSessionObj);
        ThirdSession thirdSession = JSONUtil.toBean(thirdSessionStr, ThirdSession.class);
        ThirdSessionHolder.setThirdSession(thirdSession); // 设置thirdSession
      }
    } else {
      AjaxResult r =
          AjaxResult.error(MyReturnCode.ERR_60002.getCode(), MyReturnCode.ERR_60002.getMsg());
      this.writerPrint(response, r);
      return Boolean.FALSE;
    }
    return Boolean.TRUE;
  }

  /** 创建开发环境默认用户会话 */
  private ThirdSession createDefaultThirdSession() {
    ThirdSession thirdSession = new ThirdSession();
    thirdSession.setWxUserId("1777203546643542018"); // 默认用户ID
    thirdSession.setOpenId("oXxoq5OP03GpYxFGBFwhU8oOKzuM"); // 默认openId
    thirdSession.setAppId("wx_dev_default"); // 开发环境默认appId
    thirdSession.setSessionKey("dev_session_key"); // 开发环境默认sessionKey
    return thirdSession;
  }

  private void writerPrint(HttpServletResponse response, AjaxResult r) throws IOException {
    // 返回超时错误码，触发小程序重新登录
    response.setCharacterEncoding(CommonConstants.UTF8);
    response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    PrintWriter writer = response.getWriter();
    writer.print(JSONUtil.parseObj(r));
    if (writer != null) {
      writer.close();
    }
  }
}
