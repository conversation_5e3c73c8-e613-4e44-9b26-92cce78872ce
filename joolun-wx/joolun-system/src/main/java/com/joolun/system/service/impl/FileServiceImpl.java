package com.joolun.system.service.impl;

import cn.hutool.core.date.DateTime;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.ObjectListing;
import com.joolun.common.config.OssConfig;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.system.service.IFileService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.UUID;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * <AUTHOR> @Description //TODO @Date 2023/11/8-23:43 @Version 1.0.0
 */
@Service
public class FileServiceImpl implements IFileService {

  private static final Logger log = LoggerFactory.getLogger(FileServiceImpl.class);

  @Override
  public String upload(InputStream inputStream, String type, String filename, String travelId) {
    OSS ossClient =
        new OSSClientBuilder().build(OssConfig.ENDPOINT, OssConfig.KEY_ID, OssConfig.KEY_SECRET);
    log.info("OSSClient实例创建成功");
    try {
      // 检查存储桶是否存在
      if (!ossClient.doesBucketExist(OssConfig.BUCKET_NAME)) {
        ossClient.createBucket(OssConfig.BUCKET_NAME);
        ossClient.setBucketAcl(OssConfig.BUCKET_NAME, CannedAccessControlList.PublicRead);
      }

      // 构建路径
      String envPath = OssConfig.ENV; // 从配置读取 prod/test
      String datePath = new DateTime().toString("yyyy-MM-dd");
      String fileExtension = filename.substring(filename.lastIndexOf("."));
      String newFilename = UUID.randomUUID() + fileExtension;

      String key;
      if (StringUtils.isNotBlank(travelId)) {
        // 后台管理端：包含travelId的路径 prod/trave123/image/2023-11-09/uuid.jpg
        key = String.format("%s/%s/%s/%s/%s", envPath, travelId, type, datePath, newFilename);
      } else {
        // 小程序端：不包含travelId的路径 prod/avatar/2023-11-09/uuid.jpg
        key = String.format("%s/%s/%s/%s", envPath, type, datePath, newFilename);
      }

      // 上传文件
      PutObjectRequest putObjectRequest =
          new PutObjectRequest(OssConfig.BUCKET_NAME, key, inputStream);
      ossClient.putObject(putObjectRequest);

      // 生成访问 URL
      String endpoint = OssConfig.ENDPOINT.substring(OssConfig.ENDPOINT.lastIndexOf("//") + 1);
      return "https://" + OssConfig.BUCKET_NAME + "." + endpoint + "/" + key;
    } catch (OSSException | ClientException e) {
      log.error("文件上传失败: {}", ExceptionUtils.getStackTrace(e));
      throw new RuntimeException("文件上传失败");
    } finally {
      if (ossClient != null) {
        ossClient.shutdown();
      }
    }
  }

  // 删除方法需要同步调整 URL 解析逻辑
  @Override
  public void remove(String url) {
    OSS ossClient =
        new OSSClientBuilder().build(OssConfig.ENDPOINT, OssConfig.KEY_ID, OssConfig.KEY_SECRET);
    try {
      String endpoint = OssConfig.ENDPOINT.substring(OssConfig.ENDPOINT.lastIndexOf("//") + 2);
      String host = "https://" + OssConfig.BUCKET_NAME + "." + endpoint + "/";
      String objectName = url.substring(host.length());
      ossClient.deleteObject(OssConfig.BUCKET_NAME, objectName);
    } catch (OSSException | ClientException e) {
      log.error("文件删除失败: {}", ExceptionUtils.getStackTrace(e));
      throw new RuntimeException("文件删除失败");
    } finally {
      if (ossClient != null) {
        ossClient.shutdown();
      }
    }
  }

  @Override
  public List<Map<String, String>> listFiles() {
    OSS ossClient = new OSSClientBuilder().build(OssConfig.ENDPOINT, OssConfig.KEY_ID, OssConfig.KEY_SECRET);
    List<Map<String, String>> fileList = new ArrayList<>();
    
    try {
      String envPath = OssConfig.ENV;
//      String prefix = envPath + "/" + SecurityUtils.getLoginUser().getUser().getTravelId() + "/";
      String prefix = envPath;

      ListObjectsRequest listObjectsRequest = new ListObjectsRequest(OssConfig.BUCKET_NAME);
      listObjectsRequest.setPrefix(prefix);
      
      ObjectListing listing = ossClient.listObjects(listObjectsRequest);
      String endpoint = OssConfig.ENDPOINT.substring(OssConfig.ENDPOINT.lastIndexOf("//") + 1);
      String baseUrl = "https://" + OssConfig.BUCKET_NAME + "." + endpoint + "/";
      
      for (OSSObjectSummary objectSummary : listing.getObjectSummaries()) {
        Map<String, String> fileInfo = new HashMap<>();
        String fileName = objectSummary.getKey().substring(objectSummary.getKey().lastIndexOf("/") + 1);
        fileInfo.put("fileName", fileName);
        fileInfo.put("fileUrl", baseUrl + objectSummary.getKey());
        fileList.add(fileInfo);
      }
      
      return fileList;
    } catch (OSSException | ClientException e) {
      log.error("获取文件列表失败: {}", ExceptionUtils.getStackTrace(e));
      throw new RuntimeException("获取文件列表失败", e);
    } finally {
      if (ossClient != null) {
        ossClient.shutdown();
      }
    }
  }
}
