package com.joolun.system.service;


import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @Description //TODO @Date 2023/11/8-23:42 @Version 1.0.0
 */
public interface IFileService {

  //    String upload(InputStream inputStream, String module, String filename);

  /**
   * 文件上传
   *
   * @param inputStream 文件输入流
   * @param type 文件类型/模块
   * @param filename 文件名
   * @param travelId 旅行社ID，可为空（小程序端用户上传时为空）
   * @return 文件访问URL
   */
  String upload(InputStream inputStream, String type, String filename, String travelId);

  void remove(String url);

  List<Map<String, String>> listFiles();
}
