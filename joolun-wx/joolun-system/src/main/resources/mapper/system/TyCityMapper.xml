<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joolun.system.mapper.TyCityMapper">
    
    <resultMap type="TyCity" id="TyCityResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="pid"    column="pid"    />
        <result property="type"    column="type"    />
        <result property="isHot"    column="is_hot"    />
    </resultMap>

    <sql id="selectTyCityVo">
        select id, code, name, pid, type, is_hot from ty_city
    </sql>

    <select id="selectTyCityList" parameterType="TyCity" resultMap="TyCityResult">
        <include refid="selectTyCityVo"/>
        <where>
            <if test="code != null "> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="isHot != null  and isHot != ''"> and is_hot = #{isHot}</if>
        </where>
    </select>
    
    <select id="selectTyCityById" parameterType="Long" resultMap="TyCityResult">
        <include refid="selectTyCityVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTyCity" parameterType="TyCity" useGeneratedKeys="true" keyProperty="id">
        insert into ty_city
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="pid != null">pid,</if>
            <if test="type != null">type,</if>
            <if test="isHot != null">is_hot,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="pid != null">#{pid},</if>
            <if test="type != null">#{type},</if>
            <if test="isHot != null">#{isHot},</if>
         </trim>
    </insert>

    <update id="updateTyCity" parameterType="TyCity">
        update ty_city
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="type != null">type = #{type},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTyCityById" parameterType="Long">
        delete from ty_city where id = #{id}
    </delete>

    <delete id="deleteTyCityByIds" parameterType="java.lang.String">
        delete from ty_city where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDest"  resultMap="TyCityResult">
        SELECT
            a.*
        FROM
            ty_city a
                RIGHT JOIN goods_spu b ON a.code = b.dest_city
                right join goods_spu_detail d on b.id=d.goods_id
        where b.dest_city != ''
        UNION
        SELECT
            *
        FROM
            ty_city
        WHERE
                id IN (
                SELECT
                    a.pid
                FROM
                    ty_city a
                WHERE
                        a.code IN ( SELECT a.dest_city FROM goods_spu a right join goods_spu_detail d on a.id=d.goods_id
                                    GROUP BY dest_city ))
    </select>


    <select id="selectHostDest"  resultMap="TyCityResult">
        select *
        from ty_city
        where code in (select a.dest_city from goods_spu a right join goods_spu_detail d on a.id=d.goods_id
                       where  d.date >= date_format(NOW(), '%Y-%m-%d' )
                       group by dest_city order by count(1) desc)
    </select>


    <select id="selectTyCityListNotZero" parameterType="TyCity" resultMap="TyCityResult">
        <include refid="selectTyCityVo"/>
        where pid != 0
    </select>

    <!-- 批量更新城市热门状态 -->
    <update id="updateCityHotStatus">
        update ty_city
        set is_hot = #{isHot}
        <where>
            <if test="query != null and query.cityIds != null and query.cityIds.size() > 0">
                and id in
                <foreach item="id" collection="query.cityIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query == null or query.cityIds == null">
                and 1 = 1
            </if>
        </where>
    </update>

    <!-- 查询热门城市列表 -->
    <select id="selectHotCities" resultMap="TyCityResult">
        <include refid="selectTyCityVo"/>
        where is_hot = '1' and type=2
        order by id
    </select>

</mapper>