-- ================================================================
-- 订单SKU结构迁移脚本
-- 版本: V1.0.0
-- 描述: 将现有订单数据从GoodsSpuDetail结构迁移到新的SKU结构
-- 作者: 系统开发
-- 日期: 2024-12-23
-- ================================================================

-- 备份原有数据（可选）
-- CREATE TABLE order_item_backup AS SELECT * FROM order_item;
-- CREATE TABLE goods_spu_detail_backup AS SELECT * FROM goods_spu_detail;

-- 开始迁移事务
START TRANSACTION;

-- ===================================================================
-- 第一步：为order_item表添加SKU相关字段
-- ===================================================================
ALTER TABLE order_item 
ADD COLUMN sku_id VARCHAR(32) DEFAULT NULL COMMENT 'SKU ID (新增字段)' AFTER spu_id,
ADD COLUMN sku_name VARCHAR(255) DEFAULT NULL COMMENT 'SKU套餐名称 (新增字段)' AFTER sku_id;

-- 为SKU字段添加索引
CREATE INDEX idx_order_item_sku_id ON order_item(sku_id);

-- ===================================================================
-- 第二步：为现有商品创建默认的统一规格SKU
-- 说明：为所有已有的商品SPU创建一个默认的"统一规格"SKU
-- ===================================================================

-- 为已有商品创建默认SKU记录
INSERT INTO goods_sku (
    id,
    goods_id,
    sku_code,
    sku_name,
    has_specifications,
    delete_flag,
    create_by,
    create_time,
    update_by,
    update_time
)
SELECT 
    CONCAT('sku_default_', gs.id) AS id,  -- 生成默认SKU ID
    gs.id AS goods_id,
    CONCAT('DEFAULT_', gs.spu_code) AS sku_code,
    CONCAT(gs.name, '_统一规格') AS sku_name,
    0 AS has_specifications,  -- 统一规格
    0 AS delete_flag,
    'MIGRATION' AS create_by,
    NOW() AS create_time,
    'MIGRATION' AS update_by,
    NOW() AS update_time
FROM goods_spu gs
WHERE NOT EXISTS (
    SELECT 1 FROM goods_sku sku WHERE sku.goods_id = gs.id
);

-- ===================================================================
-- 第三步：将GoodsSpuDetail数据迁移到GoodsSkuCalendarPrice
-- 说明：将旧的商品详情按日期价格库存迁移到新的SKU日历价格结构
-- ===================================================================

INSERT INTO goods_sku_calendar_price (
    id,
    sku_id,
    goods_id,
    calendar_date,
    adult_price,
    child_price,
    stock,
    commission,
    status,
    sale_num,
    delete_flag,
    create_by,
    create_time,
    update_by,
    update_time
)
SELECT 
    CONCAT('cal_price_', gsd.id) AS id,  -- 生成新的日历价格ID
    CONCAT('sku_default_', gsd.goods_id) AS sku_id,  -- 关联到默认SKU
    gsd.goods_id,
    STR_TO_DATE(gsd.date, '%Y-%m-%d') AS calendar_date,
    gsd.adult_price,
    gsd.child_price,
    gsd.stock,
    gsd.commission,
    1 AS status,  -- 默认启用状态
    0 AS sale_num,  -- 初始销量为0
    0 AS delete_flag,
    'MIGRATION' AS create_by,
    NOW() AS create_time,
    'MIGRATION' AS update_by,
    NOW() AS update_time
FROM goods_spu_detail gsd
WHERE gsd.del_flag = '0'  -- 只迁移未删除的数据
AND gsd.date IS NOT NULL
AND gsd.date != '';

-- ===================================================================
-- 第四步：更新订单项关联SKU
-- 说明：为现有订单项关联对应的SKU和SKU名称
-- ===================================================================

-- 方式1：基于detail_id直接关联（如果detail_id就是goods_spu_detail的ID）
UPDATE order_item oi
INNER JOIN goods_spu_detail gsd ON oi.detail_id = gsd.id
SET 
    oi.sku_id = CONCAT('sku_default_', gsd.goods_id),
    oi.sku_name = CONCAT((SELECT name FROM goods_spu WHERE id = gsd.goods_id), '_统一规格')
WHERE oi.sku_id IS NULL
AND gsd.del_flag = '0';

-- 方式2：基于spu_id和出发日期关联（备用方案）
UPDATE order_item oi
INNER JOIN goods_spu_detail gsd ON oi.spu_id = gsd.goods_id 
    AND DATE(oi.departure_date) = STR_TO_DATE(gsd.date, '%Y-%m-%d')
SET 
    oi.sku_id = CONCAT('sku_default_', gsd.goods_id),
    oi.sku_name = CONCAT((SELECT name FROM goods_spu WHERE id = gsd.goods_id), '_统一规格')
WHERE oi.sku_id IS NULL
AND oi.departure_date IS NOT NULL
AND gsd.del_flag = '0';

-- 方式3：为仍未关联SKU的订单项创建通用关联（兜底方案）
UPDATE order_item oi
INNER JOIN goods_spu gs ON oi.spu_id = gs.id
SET 
    oi.sku_id = CONCAT('sku_default_', gs.id),
    oi.sku_name = CONCAT(gs.name, '_统一规格')
WHERE oi.sku_id IS NULL
AND gs.del_flag = '0';

-- ===================================================================
-- 第五步：数据一致性检查和修复
-- ===================================================================

-- 检查是否有未关联SKU的订单项
SELECT 
    COUNT(*) as unlinked_orders,
    '未关联SKU的订单项数量' as description
FROM order_item 
WHERE sku_id IS NULL AND del_flag = '0';

-- 检查SKU关联的有效性
SELECT 
    COUNT(*) as invalid_sku_links,
    '无效SKU关联数量' as description
FROM order_item oi
LEFT JOIN goods_sku gs ON oi.sku_id = gs.id
WHERE oi.sku_id IS NOT NULL 
AND oi.del_flag = '0'
AND gs.id IS NULL;

-- 为没有关联到有效SKU的订单项记录日志
INSERT INTO migration_log (
    table_name,
    record_id,
    issue_type,
    description,
    create_time
)
SELECT 
    'order_item' as table_name,
    oi.id as record_id,
    'MISSING_SKU' as issue_type,
    CONCAT('订单项ID:', oi.id, ', SPU ID:', oi.spu_id, ', 未能关联到有效SKU') as description,
    NOW() as create_time
FROM order_item oi
LEFT JOIN goods_sku gs ON oi.sku_id = gs.id
WHERE oi.del_flag = '0'
AND (oi.sku_id IS NULL OR gs.id IS NULL);

-- ===================================================================
-- 第六步：创建迁移日志表（如果不存在）
-- ===================================================================
CREATE TABLE IF NOT EXISTS migration_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(64) NOT NULL COMMENT '受影响的表名',
    record_id VARCHAR(32) NOT NULL COMMENT '记录ID', 
    issue_type VARCHAR(32) NOT NULL COMMENT '问题类型',
    description TEXT COMMENT '详细描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT='数据迁移日志表';

-- ===================================================================
-- 第七步：验证迁移结果
-- ===================================================================

-- 统计迁移结果
SELECT 
    '订单项总数' as metric,
    COUNT(*) as count
FROM order_item WHERE del_flag = '0'
UNION ALL
SELECT 
    '已关联SKU的订单项' as metric,
    COUNT(*) as count  
FROM order_item WHERE del_flag = '0' AND sku_id IS NOT NULL
UNION ALL
SELECT 
    '新增SKU数量' as metric,
    COUNT(*) as count
FROM goods_sku WHERE create_by = 'MIGRATION'
UNION ALL
SELECT 
    '新增日历价格记录数量' as metric,
    COUNT(*) as count
FROM goods_sku_calendar_price WHERE create_by = 'MIGRATION';

-- ===================================================================
-- 完成迁移
-- ===================================================================

-- 如果验证通过，提交事务
COMMIT;

-- 输出完成信息
SELECT 
    '数据迁移完成' as status,
    NOW() as completion_time,
    '请运行验证查询检查结果' as next_step; 