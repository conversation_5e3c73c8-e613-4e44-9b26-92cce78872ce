-- ================================================================
-- 版本: V1.0.1
-- 描述: 添加商品费用详情表，支持成人儿童费用说明
-- 作者: 系统开发
-- 日期: 2024-01-15
-- ================================================================

-- 创建商品费用详情表（列式存储，固定费用项目）
CREATE TABLE IF NOT EXISTS `goods_cost_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `goods_id` varchar(32) NOT NULL COMMENT '商品SPU ID，关联goods_spu.id',
  `passenger_type` varchar(10) NOT NULL COMMENT '乘客类型：adult-成人, child-儿童',
  `transport_desc` varchar(500) DEFAULT NULL COMMENT '交通费用说明',
  `stay_desc` varchar(500) DEFAULT NULL COMMENT '住宿费用说明',
  `meals_desc` varchar(500) DEFAULT NULL COMMENT '用餐费用说明',
  `tickets_desc` varchar(500) DEFAULT NULL COMMENT '门票费用说明',
  `guide_desc` varchar(500) DEFAULT NULL COMMENT '导服费用说明',
  -- 预留未来可能的费用项目扩展
  -- `insurance_desc` varchar(500) DEFAULT NULL COMMENT '保险费用说明',
  -- `shopping_desc` varchar(500) DEFAULT NULL COMMENT '购物费用说明',
  `delete_flag` tinyint(1) DEFAULT 0 COMMENT '删除标记：0-未删除, 1-已删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者', 
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_passenger_type` (`passenger_type`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品费用详情表';

-- 创建唯一索引，防止同一商品的同一乘客类型重复记录
CREATE UNIQUE INDEX `uk_goods_passenger` ON `goods_cost_detail` (`goods_id`, `passenger_type`, `delete_flag`);

-- 验证表创建成功
SELECT 'goods_cost_detail表创建成功' AS result; 