-- =====================================================
-- 数据库变更脚本
-- =====================================================
-- 版本: v1.1.0
-- 文件: 250610_add_field.sql
-- 作者: shimingxiang
-- 执行状态：已执行
-- =====================================================

-- =====================================================
-- 1. 增加字段
-- =====================================================

alter table goods_spu
    add spot_name varchar(255) null comment '景区名称';

-- 为ty_city表添加is_hot字段
ALTER TABLE ty_city ADD COLUMN is_hot VARCHAR(1) DEFAULT '0' COMMENT '是否热门城市 0-否 1-是';

-- 创建索引提高查询性能
CREATE INDEX idx_ty_city_is_hot ON ty_city(is_hot);

