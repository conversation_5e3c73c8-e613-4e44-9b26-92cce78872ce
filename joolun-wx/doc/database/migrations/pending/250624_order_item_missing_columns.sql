-- ============================================================
-- 订单项表(order_item)缺失字段补充SQL脚本
-- 创建时间: 2024-12-23  
-- 说明: 基于最新数据库表结构对比OrderItem.java实体类，只需补充3个缺失字段
-- ============================================================

-- 注意：执行前请备份数据库
-- CREATE TABLE order_item_backup AS SELECT * FROM order_item;

-- 对比分析结果：数据库已有字段较完整，只需补充以下3个字段

-- 1. SKU相关字段（SKU迁移核心字段）
ALTER TABLE order_item 
ADD COLUMN sku_id VARCHAR(32) DEFAULT NULL COMMENT 'SKU ID (新增字段 - SKU迁移)' AFTER spu_id;

ALTER TABLE order_item 
ADD COLUMN sku_name VARCHAR(255) DEFAULT NULL COMMENT 'SKU套餐名称 (新增字段 - SKU迁移)' AFTER sku_id;

-- 2. 价格日历ID字段
ALTER TABLE order_item 
ADD COLUMN gsc_price_id VARCHAR(32) DEFAULT NULL COMMENT '价格日历ID' AFTER detail_id;

-- 创建必要的索引
CREATE INDEX idx_order_item_sku_id ON order_item(sku_id);
CREATE INDEX idx_order_item_gsc_price_id ON order_item(gsc_price_id);

-- 验证字段添加结果
DESCRIBE order_item;

-- 统计新增字段信息
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'order_item'
    AND COLUMN_NAME IN ('sku_id', 'sku_name', 'gsc_price_id')
ORDER BY ORDINAL_POSITION;

-- 验证表结构完整性
SELECT COUNT(*) as total_columns 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'order_item';

COMMIT; 