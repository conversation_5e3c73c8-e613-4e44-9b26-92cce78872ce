-- ================================================================
-- 订单SKU迁移验证脚本  
-- 版本: V1.0.0
-- 描述: 验证SKU迁移的结果和数据完整性
-- 作者: 系统开发
-- 日期: 2024-12-23
-- ================================================================

-- ===================================================================
-- 第一部分：基础数据统计验证
-- ===================================================================

SELECT '=== 基础数据统计 ===' as section;

-- 1. 商品SPU总数
SELECT 
    'SPU总数' as item,
    COUNT(*) as count,
    '所有商品SPU的数量' as description
FROM goods_spu 
WHERE del_flag = '0';

-- 2. 订单项总数  
SELECT 
    '订单项总数' as item,
    COUNT(*) as count,
    '所有有效订单项的数量' as description
FROM order_item 
WHERE del_flag = '0';

-- 3. 新创建的SKU数量
SELECT 
    '新增SKU数量' as item,
    COUNT(*) as count,
    '迁移过程中新创建的SKU数量' as description
FROM goods_sku 
WHERE create_by = 'MIGRATION';

-- 4. 新增的日历价格记录数量
SELECT 
    '新增日历价格记录数量' as item,
    COUNT(*) as count,
    '从GoodsSpuDetail迁移的日历价格记录数量' as description
FROM goods_sku_calendar_price 
WHERE create_by = 'MIGRATION';

-- ===================================================================
-- 第二部分：SKU关联验证
-- ===================================================================

SELECT '=== SKU关联验证 ===' as section;

-- 5. 已关联SKU的订单项数量
SELECT 
    '已关联SKU订单项' as item,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM order_item WHERE del_flag = '0'), 2), '%') as percentage
FROM order_item 
WHERE del_flag = '0' AND sku_id IS NOT NULL;

-- 6. 未关联SKU的订单项（问题数据）
SELECT 
    '未关联SKU订单项' as item,
    COUNT(*) as count,
    '需要手动处理的问题订单项' as description
FROM order_item 
WHERE del_flag = '0' AND sku_id IS NULL;

-- 7. 无效SKU关联（指向不存在的SKU）
SELECT 
    '无效SKU关联' as item,
    COUNT(*) as count,
    'SKU ID指向但SKU不存在的订单项' as description
FROM order_item oi
LEFT JOIN goods_sku gs ON oi.sku_id = gs.id
WHERE oi.del_flag = '0' 
AND oi.sku_id IS NOT NULL 
AND gs.id IS NULL;

-- ===================================================================
-- 第三部分：数据完整性验证  
-- ===================================================================

SELECT '=== 数据完整性验证 ===' as section;

-- 8. 每个SPU的SKU数量分布
SELECT 
    '每SPU的SKU数量分布' as item,
    has_specifications,
    COUNT(*) as sku_count,
    CASE 
        WHEN has_specifications = 0 THEN '统一规格'
        WHEN has_specifications = 1 THEN '多规格'
        ELSE '未知'
    END as specification_type
FROM goods_sku gs
INNER JOIN goods_spu gsp ON gs.goods_id = gsp.id
WHERE gs.delete_flag = 0 AND gsp.del_flag = '0'
GROUP BY has_specifications;

-- 9. 日历价格覆盖率检查
SELECT 
    '有日历价格的SKU数量' as item,
    COUNT(DISTINCT gscp.sku_id) as count,
    '有价格数据的SKU数量' as description
FROM goods_sku_calendar_price gscp
INNER JOIN goods_sku gs ON gscp.sku_id = gs.id
WHERE gscp.delete_flag = 0 AND gs.delete_flag = 0;

-- 10. 价格日期范围检查
SELECT 
    '价格日期范围' as item,
    MIN(calendar_date) as min_date,
    MAX(calendar_date) as max_date,
    DATEDIFF(MAX(calendar_date), MIN(calendar_date)) as date_span_days
FROM goods_sku_calendar_price 
WHERE delete_flag = 0;

-- ===================================================================
-- 第四部分：问题数据详情
-- ===================================================================

SELECT '=== 问题数据详情 ===' as section;

-- 11. 未关联SKU的订单项详情（前10条）
SELECT 
    '未关联SKU的订单项详情' as item,
    oi.id as order_item_id,
    oi.order_id,
    oi.spu_id,
    oi.spu_name,
    oi.detail_id,
    oi.departure_date
FROM order_item oi
WHERE oi.del_flag = '0' AND oi.sku_id IS NULL
LIMIT 10;

-- 12. 重复的SKU关联检查  
SELECT 
    'SKU关联重复度检查' as item,
    sku_id,
    COUNT(*) as usage_count,
    '同一SKU被多少订单项使用' as description
FROM order_item 
WHERE del_flag = '0' AND sku_id IS NOT NULL
GROUP BY sku_id
HAVING COUNT(*) > 1
ORDER BY usage_count DESC
LIMIT 10;

-- ===================================================================
-- 第五部分：业务逻辑验证
-- ===================================================================

SELECT '=== 业务逻辑验证 ===' as section;

-- 13. 订单金额与SKU价格一致性检查（抽样）
SELECT 
    '价格一致性检查' as item,
    COUNT(*) as total_checked,
    SUM(CASE WHEN ABS(oi.sales_price - gscp.adult_price) < 0.01 THEN 1 ELSE 0 END) as price_match_count,
    CONCAT(
        ROUND(
            SUM(CASE WHEN ABS(oi.sales_price - gscp.adult_price) < 0.01 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 
            2
        ), 
        '%'
    ) as match_percentage
FROM order_item oi
INNER JOIN goods_sku_calendar_price gscp ON oi.sku_id = gscp.sku_id 
    AND DATE(oi.departure_date) = DATE(gscp.calendar_date)
WHERE oi.del_flag = '0' 
AND oi.sku_id IS NOT NULL 
AND oi.departure_date IS NOT NULL
AND gscp.delete_flag = 0
LIMIT 1000;  -- 抽样1000条检查

-- 14. 库存数据合理性检查
SELECT 
    '库存数据检查' as item,
    COUNT(*) as total_calendar_prices,
    SUM(CASE WHEN stock > 0 THEN 1 ELSE 0 END) as positive_stock_count,
    SUM(CASE WHEN stock < 0 THEN 1 ELSE 0 END) as negative_stock_count,
    AVG(stock) as avg_stock
FROM goods_sku_calendar_price 
WHERE delete_flag = 0;

-- ===================================================================
-- 第六部分：迁移问题汇总
-- ===================================================================

SELECT '=== 迁移问题汇总 ===' as section;

-- 15. 迁移日志中的问题汇总
SELECT 
    issue_type,
    COUNT(*) as issue_count,
    '问题类型及数量' as description
FROM migration_log 
GROUP BY issue_type
ORDER BY issue_count DESC;

-- 16. 需要手动处理的数据
SELECT 
    '需要手动处理' as category,
    table_name,
    issue_type, 
    COUNT(*) as count
FROM migration_log
GROUP BY table_name, issue_type
ORDER BY count DESC;

-- ===================================================================
-- 第七部分：迁移质量评估
-- ===================================================================

SELECT '=== 迁移质量评估 ===' as section;

-- 17. 迁移完成度评估
SELECT 
    '迁移完成度评估' as evaluation,
    CASE 
        WHEN sku_coverage >= 95 THEN '优秀'
        WHEN sku_coverage >= 90 THEN '良好'  
        WHEN sku_coverage >= 80 THEN '一般'
        ELSE '需要改进'
    END as quality_rating,
    CONCAT(sku_coverage, '%') as sku_coverage_percentage,
    total_orders,
    linked_orders
FROM (
    SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN sku_id IS NOT NULL THEN 1 ELSE 0 END) as linked_orders,
        ROUND(SUM(CASE WHEN sku_id IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as sku_coverage
    FROM order_item 
    WHERE del_flag = '0'
) as coverage_stats;

-- ===================================================================
-- 验证结论
-- ===================================================================

SELECT 
    '数据迁移验证完成' as status,
    NOW() as verification_time,
    '请检查以上各项指标是否符合预期' as recommendation; 