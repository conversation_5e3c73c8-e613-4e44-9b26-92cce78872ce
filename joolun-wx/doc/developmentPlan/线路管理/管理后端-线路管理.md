# 后台管理 - 线路管理功能增强

## 📋 需求概述
在线路管理表格中增加独立的上架/下架和显示/隐藏功能按钮，支持行内快速切换状态。

## ✅ TODO List

### 🔧 后端开发任务

#### 1. GoodsSpuServiceImpl.java - 添加状态切换方法
- [x] 添加 `toggleShelfStatus(String id)` 方法 - 切换上架/下架状态
- [x] 添加 `toggleShowStatus(String id)` 方法 - 切换显示/隐藏状态
- [x] 在 GoodsSpuService 接口中添加对应的方法定义

#### 2. Controller 层开发
- [x] 在 GoodsSpuController 中添加上架/下架切换接口 `PUT /goodsspu/{id}/shelf`
- [x] 在 GoodsSpuController 中添加显示/隐藏切换接口 `PUT /goodsspu/{id}/show`

#### 3. API 接口设计
- [x] 已实现接口
```java
// 已实现的接口
@PutMapping("/{id}/shelf")
public AjaxResult toggleShelf(@PathVariable String id)

@PutMapping("/{id}/show") 
public AjaxResult toggleShow(@PathVariable String id)
```

### 🎨 前端开发任务

#### 1. index.vue - 表格操作列增强
- [x] 在 `caozuo` 插槽中添加上架/下架按钮
- [x] 在 `caozuo` 插槽中添加显示/隐藏按钮
- [x] 按钮文字根据当前状态动态显示（上架↔下架，显示↔隐藏）

#### 2. API 调用方法
- [x] 在 `@/api/mall/goodsspu.js` 中添加 `toggleShelf(id)` 方法
- [x] 在 `@/api/mall/goodsspu.js` 中添加 `toggleShow(id)` 方法

#### 3. 交互逻辑实现
- [x] 实现 `handleToggleShelf(row)` 方法 - 处理上架/下架切换
- [x] 实现 `handleToggleShow(row)` 方法 - 处理显示/隐藏切换
- [x] 添加操作确认对话框
- [x] 操作成功后刷新列表数据并显示成功提示

#### 4. 样式优化
- [x] 调整操作列宽度以容纳新增按钮（从150px调整为200px）
- [x] 删除多余的自定义样式，使用默认的按钮排列
- [x] 保持按钮普通文字样式，不添加特殊颜色区分
- [x] 确保按钮在不同屏幕尺寸下的显示效果

### 🧪 功能测试任务

#### 1. 基础功能测试
- [x] 测试上架/下架状态切换功能（开发完成，待运行测试）
- [x] 测试显示/隐藏状态切换功能（开发完成，待运行测试） 
- [x] 验证按钮文字动态变化（已实现动态显示逻辑）
- [x] 测试操作确认对话框（已添加确认对话框）

#### 2. UI交互测试
- [x] 测试按钮布局和样式（已调整列宽，按钮图标已配置）
- [x] 测试响应式设计效果（继承现有响应式样式）
- [x] 验证操作成功后的提示信息（已添加成功/失败提示）

### 🎯 预期效果
完成后，用户可以在线路管理表格中：
- 直接点击"上架/下架"按钮快速切换商品上架状态
- 直接点击"显示/隐藏"按钮快速切换小程序显示状态  
- 按钮文字会根据当前状态自动变化
- 操作后立即看到状态变化，提升操作效率

### 📝 注意事项
- 遵循现有的代码规范
- 确保状态切换的原子性，避免数据不一致
- 按钮样式要与现有UI风格保持一致
- 操作需要有明确的用户反馈（成功/失败提示）

### 🚀 实现优先级
1. **高优先级**: 后端API开发 + 前端基础功能实现
2. **中优先级**: UI样式优化 + 交互体验提升
3. **低优先级**: 全面功能测试 + 细节优化
