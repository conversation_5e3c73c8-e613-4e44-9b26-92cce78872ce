# 商品管理系统 - 产品需求文档 (PRD)

## 📋 产品概述

### 🎯 产品定位
面向旅行社的线路商品信息管理系统，帮助旅行社高效管理线路商品、规格、价格和库存，提升运营效率。

### 👥 目标用户
- **主要用户**：旅行社管理人员（商品运营、价格管理）
- **次要用户**：后端管理人员（系统维护）
- **最终用户**：C端用户（通过小程序浏览预订）

### 💡 核心价值主张
- **简化商品管理**：统一平台管理所有线路商品信息
- **灵活规格设置**：支持统一规格和多规格两种模式
- **便捷价格管理**：可视化日历价格设置，支持批量操作
- **实时库存控制**：精确的库存管理和状态控制

---

## 🛠️ 功能需求

### 1️⃣ **商品基础信息管理**

#### 功能描述
管理线路商品的基础信息，包括商品创建、编辑、状态管理等。

#### 核心功能
- **商品CRUD**：创建、查看、编辑、删除商品
- **状态管理**：上架/下架、显示/隐藏状态切换
- **分类管理**：按商品类型进行分类整理
- **搜索筛选**：支持按名称、状态、分类等条件筛选

#### 业务规则
- 商品名称在同一旅行社内不允许重复
- 删除商品前需确认无未完成订单
- 下架商品在小程序端不可见，但已有订单不受影响

### 2️⃣ **商品规格管理**

#### 功能描述
支持两种规格模式，满足不同商品的规格管理需求。

#### 规格模式选择

##### 🔸 **统一规格模式**
- **适用场景**：标准化线路商品，无需区分规格
- **特点**：一个商品对应一个SKU，简化管理
- **示例**：固定行程的旅游线路

##### 🔹 **多规格模式**
- **适用场景**：需要区分不同选项的商品
- **特点**：一个商品对应多个SKU，灵活组合
- **示例**：
  - 出行方式：飞机、火车、自驾
  - 出发地点：北京、上海、广州
  - 住宿标准：经济型、舒适型、豪华型

#### 规格管理功能
- **规格属性定义**：设置规格分类（如出行方式、地点等）
- **规格值管理**：为每个规格属性添加具体选项
- **SKU自动生成**：根据规格组合自动生成商品变体
- **SKU命名**：支持自定义SKU名称，便于识别管理

#### 业务规则
- 同一商品下SKU名称不允许重复
- 规格类型切换时，系统自动处理原有数据
- 删除规格值时，智能判断是否影响现有订单

### 3️⃣ **价格日历管理**

#### 功能描述
提供可视化的日历价格设置界面，支持灵活的价格和库存管理。

#### 核心功能
- **日历价格设置**：在日历界面直观设置每日价格
- **批量操作**：支持选择日期范围进行批量价格设置
- **价格类型**：
  - 成人价格（必填）
  - 儿童价格（可选）
  - 佣金比例设置
- **库存管理**：设置每日可售库存数量
- **状态控制**：可售/停售状态设置

#### 操作方式
- **单日设置**：点击日历日期，设置当日价格库存
- **批量设置**：选择日期范围，统一设置价格库存
- **复制功能**：复制某日设置到其他日期
- **模板应用**：使用价格模板快速设置

#### 业务规则
- 成人价格为必填项，不能为空或负数
- 库存数量不能为负数
- 停售状态下商品在小程序端置灰显示
- 价格变更需记录操作日志

### 4️⃣ **小程序端展示**

#### 功能描述
为C端用户提供商品浏览和选择功能。

#### 展示内容
- **商品信息**：商品名称、图片、基础介绍
- **规格选择**：
  - 统一规格：直接显示套餐名称
  - 多规格：提供规格选择器
- **价格日历**：显示未来30天的价格信息
- **库存状态**：实时显示可售状态

#### 交互逻辑
- 用户选择规格后，价格日历自动更新
- 无库存日期置灰显示，不可选择
- 停售日期显示特殊标识
- 价格区间显示（最低价-最高价）

---

## 👥 用户故事

### 🔸 **旅行社管理员**

#### 商品管理
- **作为**旅行社管理员，**我希望**能够快速创建新的线路商品，**以便**及时上架新产品
- **作为**旅行社管理员，**我希望**能够批量修改商品状态，**以便**高效管理大量商品
- **作为**旅行社管理员，**我希望**能够复制现有商品，**以便**快速创建相似产品

#### 规格管理
- **作为**旅行社管理员，**我希望**能够灵活设置商品规格，**以便**满足不同客户需求
- **作为**旅行社管理员，**我希望**系统能够智能生成SKU名称，**以便**快速识别不同规格
- **作为**旅行社管理员，**我希望**能够安全删除不需要的规格，**以便**保持商品信息整洁

#### 价格管理
- **作为**旅行社管理员，**我希望**能够在日历上直观设置价格，**以便**快速完成价格配置
- **作为**旅行社管理员，**我希望**能够批量设置节假日价格，**以便**提高工作效率
- **作为**旅行社管理员，**我希望**能够查看价格变更历史，**以便**跟踪价格调整记录

### 🔹 **C端用户**

#### 商品浏览
- **作为**用户，**我希望**能够清楚看到商品的不同规格选项，**以便**选择适合的套餐
- **作为**用户，**我希望**能够查看详细的价格日历，**以便**选择合适的出行日期
- **作为**用户，**我希望**能够看到实时的库存状态，**以便**确认是否可以预订

---

## 🔄 业务流程

### 📝 **商品创建流程**
1. **填写基础信息**
   - 商品名称、描述、分类
   - 上传商品图片
   - 设置基本属性

2. **选择规格类型**
   - 统一规格：直接进入价格设置
   - 多规格：配置规格属性和值

3. **设置价格库存**
   - 选择日期范围
   - 设置价格和库存
   - 确认状态设置

4. **商品上架**
   - 检查信息完整性
   - 设置上架和显示状态
   - 商品正式生效

### 🔄 **规格变更流程**
1. **规格类型切换**
   - 确认变更影响范围
   - 系统自动迁移数据
   - 重新设置规格信息

2. **规格值调整**
   - 新增规格值：扩展商品选择
   - 删除规格值：系统智能判断影响
   - 修改规格值：同步更新相关信息

### 💰 **价格管理流程**
1. **价格设置准备**
   - 选择目标SKU
   - 确定价格调整范围
   - 准备价格策略

2. **执行价格设置**
   - 选择日期范围
   - 输入价格信息
   - 设置库存和状态

3. **价格确认生效**
   - 检查设置结果
   - 确认价格生效
   - 同步到小程序端

---

## ✅ 验收标准

### 🔍 **功能验收**

#### 商品管理
- [ ] 商品创建功能正常，信息保存准确
- [ ] 商品编辑功能完整，修改生效及时
- [ ] 状态切换功能稳定，前端显示正确
- [ ] 商品删除功能安全，相关数据正确处理

#### 规格管理
- [ ] 统一规格和多规格模式切换正常
- [ ] 规格值新增删除功能稳定
- [ ] SKU自动生成逻辑正确
- [ ] SKU名称重名校验有效

#### 价格管理
- [ ] 日历价格设置界面直观易用
- [ ] 批量价格设置功能正常
- [ ] 价格数据保存准确无误
- [ ] 库存状态更新及时

#### 小程序端展示
- [ ] 商品信息展示完整准确
- [ ] 规格选择交互流畅
- [ ] 价格日历显示正确
- [ ] 库存状态实时更新

### 📊 **性能验收**
- [ ] 商品列表加载时间 < 2秒
- [ ] 价格设置保存响应时间 < 1秒
- [ ] 小程序端商品详情加载时间 < 3秒
- [ ] 支持同时在线用户数 > 100

### 🛡️ **安全验收**
- [ ] 数据权限隔离正确（按旅行社）
- [ ] 操作日志记录完整
- [ ] 敏感操作需要确认
- [ ] 数据备份恢复机制有效

---

## 📋 需求优先级

### 🔴 **P0 - 核心功能（必须实现）**
- 商品基础CRUD功能
- 统一规格模式支持
- 基础价格设置功能
- 小程序端商品展示

### 🟡 **P1 - 重要功能（优先实现）**
- 多规格模式支持
- 批量价格设置
- 状态管理功能
- 价格日历展示

### 🟢 **P2 - 增强功能（后续迭代）**
- 价格模板功能
- 数据导入导出
- 高级筛选功能
- 操作日志查看

---

## 📚 相关文档

- [商品管理-技术设计文档TDD.md](./商品管理-技术设计文档TDD.md)
- [小程序端-线路详情中价格日历接口开发.md](./小程序端-线路详情中价格日历接口开发.md)
- [管理后端-线路管理.md](./管理后端-线路管理.md)

---

*📅 文档创建时间：2025-01-19*  
*✍️ 文档维护：产品团队*  
*🔄 最后更新：基于业务需求整理的标准PRD文档* 



```mermaid
flowchart TD
    A[开始] --> B[新增线路]
    B --> C[编辑线路详情（出发地、目的地）]
    C --> D[设置价格日历]
    D --> E{规格选择}
    E --> |统一规格| F[设置指定日期价格、库存、佣金]
    E --> |多规格| G[选择规格名（可以将出发地设置为一种规格）]
    F --> N[结束]
    G --> H[选择规格值]
    H --> I[生成组合套餐]
    I --> J[设置指定日期价格、库存、佣金]
    J --> N[结束]
```