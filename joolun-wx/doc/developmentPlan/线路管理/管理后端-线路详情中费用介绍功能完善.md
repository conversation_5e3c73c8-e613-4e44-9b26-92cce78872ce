# 后台管理 - 线路详情中费用介绍功能完善

## 📋 需求概述
用户要求增加了费用介绍的模块，区分成人与儿童，之前是没有这块儿功能的，是应该在GoodsSpu表中增加还是单独建表进行管理？或者有没有其他更好的方案给我推荐一下。

## 📊 方案对比分析

### 方案一：在GoodsSpu表中增加字段
**优点：**
✅ 查询效率最高，一次SQL获取所有数据  
✅ 实现最简单，前端UI已完备  
✅ 与商品信息强关联，业务逻辑清晰  
✅ 开发工作量最小，上线最快  

**缺点：**
❌ **费用项目扩展困难** - 每次新增费用项目都需要DDL  
❌ 字段数量会持续增长（当前10个→未来可能20+个）  
❌ 表结构变更影响线上业务  
❌ 不符合数据库范式设计原则  

### 方案二：单独建费用详情表（推荐）
**优点：**
✅ **扩展性最强** - 新增费用项目无需改表结构  
✅ 数据结构规范，符合数据库设计原则  
✅ 支持动态费用项目配置  
✅ 300字内容用VARCHAR(500)即可，存储高效  

**缺点：**
❌ 需要关联查询，SQL稍复杂  
❌ 前端需要适配新数据结构  
❌ 开发工作量相对较大  

### 方案三：JSON字段存储
**优点：**
✅ 扩展性好，JSON结构可动态调整  
✅ 查询效率较高  
✅ 实现相对简单  

**缺点：**
❌ JSON查询和维护复杂  
❌ 数据类型检查困难  
❌ 不利于数据统计分析  

## 🎯 确定方案

**选择方案二：单独建费用详情表**

**决策依据：**
1. **费用项目可能增加** - 这是关键扩展性需求
2. 300字内容长度适合关系型数据库
3. 一次性设计好架构，避免后续频繁改表

## 📋 实施TodoList

### 📚 数据库设计
- [x] 1.1 设计`goods_cost_detail`表结构 ✅
  - 支持成人/儿童两种乘客类型
  - 当前费用项目：交通、住宿、用餐、门票、导服
  - 预留扩展空间，支持动态费用项目
- [x] 1.2 编写建表SQL脚本 ✅

### 🏗️ 后端开发
- [x] 2.1 创建`GoodsCostDetail`实体类 ✅
- [x] 2.2 创建`GoodsCostDetailMapper`接口 ✅
- [x] 2.3 创建`GoodsCostDetailService`服务类 ✅
- [x] 2.4 修改`GoodsSpuService`，集成费用详情查询 ✅
- [x] 2.5 修改`GoodsSpuController`，添加费用详情接口 ✅
- [x] 2.6 修改`GoodsSpuDTO`，包含费用详情数据 ✅

### 🎨 前端适配
- [ ] 3.1 修改`GoodsSpuInfo`组件数据结构
- [ ] 3.2 适配新的费用详情数据格式
- [ ] 3.3 确保提交和回显功能正常
- [ ] 3.4 处理兼容性（现有数据的展示）

### 🧪 测试验证
- [ ] 4.1 单元测试：费用详情CRUD操作
- [ ] 4.2 集成测试：商品保存包含费用详情
- [ ] 4.3 前端测试：费用详情组件功能
- [ ] 4.4 兼容性测试：现有商品数据展示

### 📖 文档更新
- [ ] 5.1 更新数据库文档
- [ ] 5.2 更新API接口文档
- [ ] 5.3 编写部署说明文档

## 📝 补充需求确认

**已确认：**
- ✅ 扩展性考虑：暂不考虑增加更多乘客类型
- ✅ 费用项目：可能会有其他费用项目（关键需求）
- ✅ 内容长度：300字以内
- ✅ 业务逻辑：暂不考虑富文本格式

**状态：** 📋 方案已确定，等待开始实施