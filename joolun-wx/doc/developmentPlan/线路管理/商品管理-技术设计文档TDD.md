# 商品管理系统 - 技术设计文档 (TDD)

## 📋 技术概述

### 🏗️ 系统架构
- **后端框架**：Java 1.8 + SpringBoot + MybatisPlus
- **前端管理**：Vue 2.x + Element-UI
- **数据库**：MySQL 5.7
- **小程序端**：uni-app
- **缓存**：Redis（可选）
- **日志**：Slf4j + Logback

### 🎯 架构设计原则
- **分层架构**：Controller -> Service -> Mapper 三层架构
- **单一职责**：每个模块职责明确，便于维护
- **依赖注入**：使用Spring IOC管理对象依赖
- **事务管理**：关键业务操作使用声明式事务

---

## 💾 数据模型设计

### 📊 核心实体关系图
```
GoodsSpu (商品SPU)
├── id (主键)
├── goodsName (商品名称)
├── goodsDescription (商品描述)
├── specificationType (规格类型: 0-统一规格, 1-多规格)
├── shelfStatus (上架状态: 0-下架, 1-上架)
├── showStatus (显示状态: 0-隐藏, 1-显示)
├── travelId (旅行社ID - 多租户隔离)
└── deleteFlag (删除标识)

GoodsSku (商品SKU)
├── id (主键)
├── goodsId (关联商品ID)
├── skuName (SKU名称/套餐名)
├── skuCode (SKU编码-自动生成)
├── hasSpecifications (是否有规格: 0-统一规格, 1-多规格)
└── deleteFlag (删除标识)

GoodsSkuSpecificationValue (SKU规格值关联)
├── skuId (SKU ID)
├── goodsId (商品ID)
├── specValueId (规格值ID)
├── attrId (规格属性ID)
└── travelId (旅行社ID)

GoodsSkuCalendarPrice (SKU日历价格)
├── id (主键)
├── skuId (SKU ID)
├── goodsId (商品ID)
├── calendarDate (日历日期)
├── adultPrice (成人价格)
├── childPrice (儿童价格)
├── commission (佣金比例)
├── stock (库存数量)
├── status (状态: 1-可售, 2-停售)
└── deleteFlag (删除标识)
```

### 🔗 实体关系
- **GoodsSpu 1:N GoodsSku**：一个商品可以有多个SKU
- **GoodsSku 1:N GoodsSkuSpecificationValue**：一个SKU可以关联多个规格值
- **GoodsSku 1:N GoodsSkuCalendarPrice**：一个SKU可以有多个日期的价格

---

## 🌐 接口设计

### 📱 管理端接口

#### 商品管理接口
```java
@RestController
@RequestMapping("/mall/goodsspu")
public class GoodsSpuController {
    
    // 商品CRUD
    @PostMapping
    public AjaxResult add(@RequestBody GoodsSpu goodsSpu);
    
    @PutMapping
    public AjaxResult edit(@RequestBody GoodsSpu goodsSpu);
    
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids);
    
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id);
    
    // 状态管理
    @PutMapping("/{id}/shelf")
    public AjaxResult toggleShelf(@PathVariable String id);
    
    @PutMapping("/{id}/show")
    public AjaxResult toggleShow(@PathVariable String id);
}
```

#### 价格管理接口
```java
@RestController
@RequestMapping("/mall/price")
public class GoodsSkuCalendarPriceController {
    
    // 批量保存价格
    @PostMapping("/batchSave")
    public AjaxResult batchSave(@RequestBody GoodsSkuCalendarPriceDTO dto);
    
    // 获取价格日历信息
    @GetMapping("/calendar/{goodsId}")
    public AjaxResult getCalendarPriceInfo(
        @PathVariable String goodsId,
        @RequestParam String specType);
}
```

### 📱 小程序端接口

#### SKU查询接口
```java
@RestController
@RequestMapping("/weixin/api/ma/goodssku")
public class GoodsSkuApi {
    
    // 获取商品SKU列表
    @GetMapping("/goods/{goodsId}/skus")
    public AjaxResult getGoodsSkuList(@PathVariable String goodsId);
    
    // 获取SKU价格日历
    @GetMapping("/sku/{skuId}/calendar-prices")
    public AjaxResult getSkuCalendarPrices(
        @PathVariable String skuId,
        @RequestParam(defaultValue = "30") @Min(1) @Max(90) Integer days);
}
```

### 🔄 数据传输对象 (DTO)

#### 管理端DTO
```java
public class GoodsSkuCalendarPriceDTO {
    private String goodsId;                           // 商品ID
    private String skuId;                            // SKU ID  
    private String skuName;                          // SKU名称
    private List<GoodsSkuCalendarPrice> priceList;   // 价格列表
    private SpecChanges specChanges;                 // 规格变更信息
    
    public static class SpecChanges {
        private List<SpecValueChange> specValueChanges;
    }
    
    public static class SpecValueChange {
        private String categoryId;                    // 规格分类ID
        private String categoryName;                  // 规格分类名称
        private List<SpecValue> addedValues;          // 新增规格值
        private List<SpecValue> removedValues;        // 删除规格值
    }
    
    public static class SpecValue {
        private String id;                           // 规格值ID
        private String name;                         // 规格值名称
        private String attrId;                       // 规格属性ID
        private String categoryName;                 // 分类名称
    }
}
```

#### 小程序端DTO
```java
public class GoodsSkuAppDTO {
    private String skuId;                            // SKU ID
    private String skuName;                          // SKU套餐名称
    private BigDecimal minPrice;                     // 最低价格
    private BigDecimal maxPrice;                     // 最高价格
    private String specificationType;                // 规格类型
    private List<GoodsAttrValueAppDTO> specInfo;     // 规格信息
}

public class SkuCalendarPriceAppDTO {
    private String date;                             // 日期 yyyy-MM-dd
    private BigDecimal adultPrice;                   // 成人价格
    private BigDecimal childPrice;                   // 儿童价格
    private Long stock;                              // 库存
    private Integer status;                          // 状态 1-可售 2-停售 0-无数据
    private BigDecimal commission;                   // 佣金比例
}
```

---

## 🔄 数据流转逻辑

### 📊 价格管理数据流转
```mermaid
graph TD
    A[管理员设置价格] --> B{规格类型判断}
    B -->|统一规格| C[batchSaveForSpecType0]
    B -->|多规格| D[batchSaveForSpecType1]
    C --> E[校验SPU]
    D --> E
    E --> F[处理规格值变更]
    F --> G[处理SKU创建/更新]
    G --> H[保存价格数据]
    H --> I[返回保存结果]
    
    F --> F1[删除无效规格值的SKU]
    F --> F2[智能判断是否保留历史SKU]
    
    G --> G1[根据规格值查找现有SKU]
    G --> G2[创建新SKU if not exists]
    G --> G3[更新SKU名称]
    
    H --> H1[查询现有价格记录]
    H --> H2[批量更新/插入价格数据]
```

### 🔄 规格变更处理流程
```mermaid
graph TD
    A[规格值变更请求] --> B[收集本次使用的规格值]
    B --> C[过滤真正删除的规格值]
    C --> D{是否有真正删除的规格值}
    D -->|是| E[查找包含这些规格值的SKU]
    D -->|否| F[跳过删除操作]
    E --> G[删除相关SKU及其数据]
    G --> H[删除SKU规格值关联]
    H --> I[删除SKU日历价格数据]
    I --> J[删除SKU记录]
    
    B --> B1[从priceList中提取specValueIds]
    C --> C1[removedValues - usedSpecValueIds]
```

### 🏗️ SKU创建处理流程
```mermaid
graph TD
    A[处理价格项SKU] --> B{SKU是否存在}
    B -->|存在| C[使用现有SKU]
    B -->|不存在| D[根据规格值查找SKU]
    D --> E{找到匹配SKU?}
    E -->|是| F[使用找到的SKU]
    E -->|否| G[创建新SKU]
    G --> H[生成SKU名称]
    H --> I[保存SKU记录]
    I --> J[创建规格值关联]
    C --> K[更新SKU名称 if needed]
    F --> K
    J --> K
    K --> L[完成SKU处理]
```

---

## ⚙️ 核心算法实现

### 🧮 智能删除规格值算法
```java
/**
 * 智能删除规格值算法
 * 核心思想：只删除真正不再使用的规格值相关SKU
 */
private void handleRemovedSpecValues(GoodsSkuCalendarPriceDTO dto) {
    // 1. 收集本次请求中所有使用的规格值
    Set<String> usedSpecValueIds = new HashSet<>();
    if (!CollectionUtils.isEmpty(dto.getPriceList())) {
        dto.getPriceList().forEach(priceItem -> {
            if (!CollectionUtils.isEmpty(priceItem.getSpecValueIds())) {
                usedSpecValueIds.addAll(priceItem.getSpecValueIds());
            }
        });
    }
    
    // 2. 过滤出真正需要删除的规格值
    List<String> actuallyRemovedValueIds = removedValueIds.stream()
        .filter(valueId -> !usedSpecValueIds.contains(valueId))
        .collect(Collectors.toList());
    
    // 3. 删除相关SKU及其数据
    if (!actuallyRemovedValueIds.isEmpty()) {
        deleteSkusWithSpecValues(actuallyRemovedValueIds);
    }
}
```

### 🔍 SKU查找匹配算法
```java
/**
 * 根据规格值组合查找对应的SKU
 */
public String findSkuBySpecCombination(String goodsId, List<String> specValueIds) {
    if (CollectionUtils.isEmpty(specValueIds)) {
        return null;
    }
    
    // 查询所有该商品的多规格SKU
    List<GoodsSku> skus = goodsSkuService.list(
        new QueryWrapper<GoodsSku>()
            .eq("goods_id", goodsId)
            .eq("has_specifications", 1)
            .eq("delete_flag", false)
    );
    
    for (GoodsSku sku : skus) {
        // 查询该SKU关联的规格值
        List<GoodsSkuSpecificationValue> skuSpecs = skuSpecService.list(
            new QueryWrapper<GoodsSkuSpecificationValue>()
                .eq("sku_id", sku.getId())
                .eq("goods_id", goodsId)
        );
        
        List<String> skuSpecValueIds = skuSpecs.stream()
            .map(GoodsSkuSpecificationValue::getSpecValueId)
            .sorted()
            .collect(Collectors.toList());
        
        List<String> targetSpecValueIds = new ArrayList<>(specValueIds);
        targetSpecValueIds.sort(String::compareTo);
        
        // 如果规格值ID列表完全匹配，返回该SKU的ID
        if (skuSpecValueIds.equals(targetSpecValueIds)) {
            return sku.getId();
        }
    }
    
    return null;
}
```

### 📊 价格统计算法
```java
/**
 * 批量获取商品价格区间统计
 */
public Map<String, Map<String, BigDecimal>> getMinMaxPriceByGoodsIds(List<String> goodsIds) {
    if (CollectionUtils.isEmpty(goodsIds)) {
        return Collections.emptyMap();
    }

    // 查询所有商品的价格数据
    List<GoodsSkuCalendarPrice> prices = this.list(
        new QueryWrapper<GoodsSkuCalendarPrice>()
            .in("goods_id", goodsIds)
            .eq("delete_flag", false)
            .orderByAsc("goods_id")
            .orderByAsc("adult_price")
    );

    // 按商品ID分组并统计价格区间
    Map<String, Map<String, BigDecimal>> result = new HashMap<>();
    Map<String, List<GoodsSkuCalendarPrice>> pricesByGoodsId = prices.stream()
        .collect(Collectors.groupingBy(GoodsSkuCalendarPrice::getGoodsId));

    for (Map.Entry<String, List<GoodsSkuCalendarPrice>> entry : pricesByGoodsId.entrySet()) {
        String goodsId = entry.getKey();
        List<GoodsSkuCalendarPrice> goodsPrices = entry.getValue();

        if (!goodsPrices.isEmpty()) {
            Map<String, BigDecimal> priceRange = new HashMap<>();
            
            BigDecimal minPrice = goodsPrices.stream()
                .map(GoodsSkuCalendarPrice::getAdultPrice)
                .min(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
                
            BigDecimal maxPrice = goodsPrices.stream()
                .map(GoodsSkuCalendarPrice::getAdultPrice)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);

            priceRange.put("minPrice", minPrice);
            priceRange.put("maxPrice", maxPrice);
            result.put(goodsId, priceRange);
        }
    }

    return result;
}
```

---

## 🛡️ 技术实现要点

### 🔒 事务管理
```java
@Service
@Transactional(rollbackFor = Exception.class)
public class GoodsSkuCalendarPriceServiceImpl {
    
    /**
     * 多规格批量保存价格
     * 使用声明式事务确保数据一致性
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSaveForSpecType1(GoodsSkuCalendarPriceDTO dto) {
        // 业务逻辑处理...
        // 任何异常都会触发事务回滚
    }
}
```

### 📊 日志记录策略
```java
/**
 * 分层级日志记录
 */
@Slf4j
public class GoodsSkuCalendarPriceServiceImpl {
    
    public int batchSaveForSpecType1(GoodsSkuCalendarPriceDTO dto) {
        // INFO级别：关键业务节点
        log.info("开始批量保存多规格日历价格库存，商品ID: {}", dto.getGoodsId());
        
        // DEBUG级别：详细执行过程
        log.debug("开始处理价格项SKU创建，价格项数量: {}", dto.getPriceList().size());
        
        // WARN级别：异常但可处理的情况
        log.warn("所有被标记删除的规格值在本次请求中仍在使用，跳过删除操作");
        
        // ERROR级别：业务异常（通过CheckUtils抛出）
        checkUtils.throwIf(!ok, "批量保存多规格日历价格库存失败 for SPU [{}]", dto.getGoodsId());
        
        return savedCount;
    }
}
```

### 🎯 性能优化

#### 批量操作优化
```java
/**
 * 批量保存优化
 */
public int saveOrUpdateCalendarPrices(GoodsSkuCalendarPriceDTO dto) {
    List<GoodsSkuCalendarPrice> toSaveOrUpdate = new ArrayList<>();
    
    // 批量查询现有记录，减少N+1问题
    Map<String, GoodsSkuCalendarPrice> existingPrices = getExistingPriceMap(dto);
    
    for (GoodsSkuCalendarPrice item : dto.getPriceList()) {
        String key = item.getSkuId() + "_" + DateUtil.formatDate(item.getCalendarDate());
        GoodsSkuCalendarPrice existing = existingPrices.get(key);
        
        if (existing != null) {
            // 更新现有记录
            updateExistingPrice(existing, item);
            toSaveOrUpdate.add(existing);
        } else {
            // 新增记录
            item.setCreateTime(DateUtils.getNowDate());
            toSaveOrUpdate.add(item);
        }
    }
    
    // 批量保存，每批200条
    boolean ok = this.saveOrUpdateBatch(toSaveOrUpdate, 200);
    return toSaveOrUpdate.size();
}
```

#### 查询优化
```java
/**
 * 查询优化 - 建立合适的数据库索引
 */
-- 商品查询索引
CREATE INDEX idx_goods_sku_goods_id ON goods_sku(goods_id, delete_flag);
CREATE INDEX idx_goods_sku_has_spec ON goods_sku(goods_id, has_specifications, delete_flag);

-- 价格查询索引
CREATE INDEX idx_calendar_price_sku_date ON goods_sku_calendar_price(sku_id, calendar_date, delete_flag);
CREATE INDEX idx_calendar_price_goods_id ON goods_sku_calendar_price(goods_id, delete_flag);

-- 规格值关联索引
CREATE INDEX idx_sku_spec_value_sku_id ON goods_sku_specification_value(sku_id, goods_id);
CREATE INDEX idx_sku_spec_value_spec_id ON goods_sku_specification_value(spec_value_id, goods_id);
```

### 🔍 异常处理机制
```java
/**
 * 统一异常处理
 */
@Component
public class CheckUtils {
    
    /**
     * 空值检查
     */
    public void throwIfNull(Object obj, String message, Object... args) {
        if (obj == null) {
            throw new BusinessException(MessageFormat.format(message, args));
        }
    }
    
    /**
     * 条件检查
     */
    public void throwIf(boolean condition, String message, Object... args) {
        if (condition) {
            throw new BusinessException(MessageFormat.format(message, args));
        }
    }
}

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public AjaxResult handleBusinessException(BusinessException e) {
        log.error("业务异常：{}", e.getMessage());
        return AjaxResult.error(e.getMessage());
    }
}
```

### 💾 数据库设计要点

#### 软删除策略
```sql
-- 所有核心表都包含软删除字段
ALTER TABLE goods_spu ADD COLUMN delete_flag BOOLEAN DEFAULT FALSE COMMENT '删除标识';
ALTER TABLE goods_sku ADD COLUMN delete_flag BOOLEAN DEFAULT FALSE COMMENT '删除标识';
ALTER TABLE goods_sku_calendar_price ADD COLUMN delete_flag BOOLEAN DEFAULT FALSE COMMENT '删除标识';

-- 查询时统一过滤已删除数据
SELECT * FROM goods_sku WHERE goods_id = ? AND delete_flag = FALSE;
```

#### 多租户数据隔离
```sql
-- 核心业务表包含旅行社ID
ALTER TABLE goods_spu ADD COLUMN travel_id VARCHAR(64) COMMENT '旅行社ID';
ALTER TABLE goods_sku_specification_value ADD COLUMN travel_id VARCHAR(64) COMMENT '旅行社ID';

-- 查询时按旅行社过滤
SELECT * FROM goods_spu WHERE travel_id = ? AND delete_flag = FALSE;
```

---

## 🧪 测试策略

### 🔬 单元测试 (BDD风格)
```java
@ExtendWith(MockitoExtension.class)
class GoodsSkuCalendarPriceServiceImplTest {
    
    @Test
    public void should_save_unified_spec_price_when_spec_type_is_zero() {
        // Given
        GoodsSkuCalendarPriceDTO dto = buildUnifiedSpecDTO();
        
        // When
        int result = service.batchSaveForSpecType0(dto);
        
        // Then
        assertThat(result).isGreaterThan(0);
        verify(goodsSpuService).updateSpecificationTypeIsZero(dto.getGoodsId());
    }
    
    @Test
    public void should_keep_existing_sku_when_spec_value_still_in_use() {
        // Given
        GoodsSkuCalendarPriceDTO dto = buildMultiSpecDTOWithRemovedValues();
        
        // When
        int result = service.batchSaveForSpecType1(dto);
        
        // Then
        assertThat(result).isGreaterThan(0);
        // 验证SKU没有被删除
        verify(goodsSkuService, never()).removeByIds(anyList());
    }
}
```

### 🌐 接口测试
```java
@SpringBootTest
@AutoConfigureTestDatabase
class GoodsSkuApiIntegrationTest {
    
    @Test
    public void should_return_sku_list_when_goods_exists() {
        // Given
        String goodsId = createTestGoods();
        
        // When
        MockHttpServletResponse response = mockMvc.perform(
            get("/weixin/api/ma/goodssku/goods/{goodsId}/skus", goodsId)
        ).andReturn().getResponse();
        
        // Then
        assertThat(response.getStatus()).isEqualTo(200);
        // 验证返回数据格式和内容
    }
}
```

---

## 📋 部署和运维

### 🚀 部署配置
```yaml
# application.yml
spring:
  datasource:
    url: **************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:root}
    
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    
logging:
  level:
    com.joolun.mall.service: DEBUG
    root: INFO
```

### 📊 监控指标
- **接口响应时间**：价格保存 < 1s，查询 < 500ms
- **数据库连接池**：监控连接数使用情况
- **JVM内存**：监控堆内存使用率
- **业务指标**：SKU创建数量、价格更新频率

---

## 📚 相关文档

- [商品管理-产品需求文档PRD.md](./商品管理-功能设计文档PRD.md)
- [小程序端-线路详情中价格日历接口开发.md](./小程序端-线路详情中价格日历接口开发.md)
- [管理后端-线路管理.md](./管理后端-线路管理.md)

---

*📅 文档创建时间：2025-01-19*  
*✍️ 文档维护：开发团队*  
*🔄 最后更新：基于GoodsSkuCalendarPriceServiceImpl重构后的技术实现整理* 