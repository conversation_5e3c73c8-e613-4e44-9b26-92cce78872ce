# 后台管理 - 管理后端-线路管理-规格配置添加排序

## 📋 需求概述

商品规格需支持「拖拽排序 + 独立保存」，并在所有终端保持一致。

---

## 🛠️ 数据库改造

| 表 | 新增字段 | 说明 |
| --- | --- | --- |
| goods_specification_attribute | `sort_order` INT DEFAULT 999 | 规格分类排序，数值越小越靠前 |
| goods_specification_value | `sort_order` INT DEFAULT 999 | 规格值排序 |

> 迁移脚本：`doc/database/migrations/2025-07-04_add_sort_order_goods_spec.sql`

---

## 🖥️ 后端改造

1. **实体类**
   * `GoodsSpecificationAttribute` / `GoodsSpecificationValue` 增加 `sortOrder` 字段常量。
2. **查询排序**
   * Service 层 `initQueryWrapper` 统一 `orderByAsc(sort_order)`。
   * `selectByGoodsId` 接口：
     * 分类 `orderByAsc(sort_order)`。
     * 分类下的值 `Collections.sort` 根据 `sortOrder`。
3. **排序接口**
   * DTO：`SpecSortDTO(id,type,sortOrder)`。
   * Controller：`POST /mall/attr/sort` 批量更新分类/规格值排序，事务提交。
4. **SKU 查询**
   * `GoodsSkuServiceImpl#getSkuSpecInfoMap` 按分类及值的 `sortOrder` 排序 `specInfo`。

---

## 💻 前端改造 (Vue2 + Element-UI)

1. **拖拽排序**
   * 引入 `vuedraggable@2.24.3`。
   * `SpecificationSelector.vue`
     * 分类 & 规格值列表包裹 `draggable`，`handleCategorySortEnd` / `handleValueSortEnd` 计算 `sortPayload`。
     * 独立「保存排序」按钮，仅在拖拽后启用，调用 `updateSort` API。
     * 取消拖拽时触发的全局 `unsaved-changes`，避免点亮「保存所有更改」。
2. **数据加载**
   * `loadSpecificationData` 按 `sortOrder` 排序分类及规格值。
3. **PriceCalendar.vue** 同步排序逻辑，保证初始渲染一致。

---

## 🌐 接口一览

| 接口 | 方法 | 描述 |
| ---- | ---- | ---- |
| `/mall/attr/sort` | POST | 批量更新分类/规格值排序 |
| `/mall/attr/allAttrList` | GET | 返回全部规格分类（已按 `sort_order` 排序） |
| `/mall/attr/getByGoodsId/{goodsId}` | GET | 返回某商品已选规格（分类/值均按 `sort_order`） |

---

## ⚠️ 注意事项

1. **默认值** `sort_order = 999` 兼容旧数据 & 未显式排序的数据。
2. **并发** 排序接口为幂等更新，最后一次保存为准。
3. **性能** 数据量有限，Java 端显式排序对性能无影响，逻辑简单且稳定。

---

## ✅ 效果

* 后台管理可拖拽排序并独立保存。
* 接口返回顺序与后台一致，前端无需二次处理。
* SKU 详情 `specInfo` 顺序与后台排序保持一致。