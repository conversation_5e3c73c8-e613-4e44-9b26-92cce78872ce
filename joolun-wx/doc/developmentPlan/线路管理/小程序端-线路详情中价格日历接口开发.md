# 小程序端 - 线路详情中价格日历接口开发

## 📋 需求概述
分析小程序端静态页面展示信息，结合项目中当前商品表，商品sku,商品规格值，商品价格日期表，开发选择套餐与日历价格的接口
小程序端静态页面对应路径 /Users/<USER>/Software/trae_workspaces/joolun-wx-uni/pages/commodityDetail/commodityDetail.vue

管理后端页面对应路径 /Users/<USER>/Software/trae_workspaces/joolun-admin/joolun-wx-ui/src/components/Specification/package.vue

将开发计划分为两个大步骤进行
- 一个是获取商品下所有SKU
  - 当前sku中缺少skuName 用于对应小程序端的套餐名，需要思考，管理后端如何增加skuName 且方便用户快速编辑
- 一个通过skuId获取近30天的价格

## 🚀 详细开发计划

### 📌 核心需求确认
为小程序端线路详情页面开发两个关键接口：
1. **获取商品下所有SKU接口** - 用于展示套餐选择
2. **通过skuId获取近30天价格接口** - 用于展示价格日历

### 📌 业务规则确认
- **SKU名称生成规则**：不管是多规格还是统一规格都允许用户修改skuName，多规格可以先默认是所有规格值的组合，但是用户可以修改，如果skuName在商品下已经存在了，需要提示用户修改skuName
- **价格日历默认天数**：30天
- **数据权限控制**：小程序端不需要根据旅行社隔离
- **缓存策略**：暂时不考虑数据更新实时性问题
- **异常处理**：当sku无价格数据时，小程序端的日历价格展示的日期，置灰即可
- **开发范围**：当前仅负责后端API开发，小程序端开发由其他同事处理

## 🗂️ Todo List

### 🏗️ **第一阶段：数据结构完善**

#### 1. **SKU表结构优化**
- [x] **1.1** 在`GoodsSku`实体类中添加`skuName`字段
  - 字段类型：`String`
  - 字段描述：SKU套餐名称，用于小程序端展示
  - 注解：添加`@Excel`注解

- [x] **1.2** 更新数据库表结构
  ```sql
  ALTER TABLE goods_sku ADD COLUMN sku_name VARCHAR(255) COMMENT 'SKU套餐名称';
  ```
  > 注意：SQL脚本已生成为 `add_sku_name_column.sql`，请在数据库中执行

- [x] **1.3** 更新Mapper映射文件
  - 修改`GoodsSkuMapper.xml`，在resultMap和sql语句中添加`sku_name`字段

#### 2. **管理后端界面优化**
- [x] **2.1** 修改`/Users/<USER>/Software/trae_workspaces/joolun-admin/joolun-wx-ui/src/components/Specification/PriceCalendarPanel.vue`组件
  - 在批量设置中增加套餐名的设置  对应  skuName
  - 单个日期设置中增加套餐名的设置  对应  skuName
  - 如果之前已经设置了套餐名，就回显到设置页面上

- [x] **2.2** 实现SKU名称重名校验
  - 在保存时检查同一商品下是否存在重复的`skuName`
  - 重名时提示用户修改
  - 前端实时校验+后端保存校验双重保障

- [x] **2.3** 更新SKU相关的Service和Controller
  - 在`GoodsSkuService`中添加按商品ID校验SKU名称唯一性的方法
  - 确保新增的`skuName`字段在CRUD操作中被正确处理
  - **✅ 新增：** 在`GoodsSkuCalendarPriceServiceImpl`中添加skuName保存逻辑
    - 统一规格保存时：自动设置默认规格或用户自定义的skuName
    - 多规格保存时：根据规格值组合生成默认skuName，支持用户覆盖
    - 支持SKU名称的动态更新

  
### 🌐 **第二阶段：小程序端API开发** ✅

#### 3. **获取商品SKU列表接口** ✅
- [x] **3.1** 创建小程序端专用DTO
  ```java
  // 已创建 GoodsSkuAppDTO.java
  public class GoodsSkuAppDTO {
      private String skuId;           // SKU ID
      private String skuName;         // SKU套餐名称
      private BigDecimal minPrice;    // 最低价格
      private BigDecimal maxPrice;    // 最高价格
      private String specificationType; // 规格类型 0-统一规格 1-多规格
      private List<GoodsAttrValueAppDTO> specInfo; // 规格信息
  }
  ```
  ```java
  // 已创建 GoodsAttrValueAppDTO.java
  public class GoodsAttrValueAppDTO {
      private String attrId;           // 规格名ID
      private String attrName;         // 规格名
      private String valueId;        // 规格值ID (修正为String类型)
      private String valueName;      // 规格值 (修正为String类型)
  }
  ```

- [x] **3.2** 开发小程序Controller接口
  ```java
  // 已创建 GoodsSkuApi.java
  @GetMapping("/goods/{goodsId}/skus")
  public AjaxResult getGoodsSkuList(@PathVariable String goodsId)
  ```
  > **✅ 接口路径：** `/weixin/api/ma/goodssku/goods/{goodsId}/skus`

- [x] **3.3** 实现Service业务逻辑
  - ✅ 根据商品ID查询所有有效SKU（未删除状态）
  - ✅ 组装SKU基本信息（ID、名称、价格区间）
  - ✅ 处理规格信息（统一规格返回默认规格，多规格返回规格组合）
  - ✅ 计算每个SKU的价格区间（从日历价格表中统计成人价格和儿童价格）
  - ✅ 实现了 `IGoodsSkuService.getSkuListByGoodsId()` 方法

#### 4. **获取SKU价格日历接口** ✅
- [x] **4.1** 创建价格日历DTO
  ```java
  // 已创建 SkuCalendarPriceAppDTO.java
  public class SkuCalendarPriceAppDTO {
      private String date;            // 日期 yyyy-MM-dd
      private BigDecimal adultPrice;  // 成人价格
      private BigDecimal childPrice;  // 儿童价格
      private Long stock;             // 库存
      private Integer status;         // 状态 1-可售 2-停售 0-无数据(置灰)
      private BigDecimal commission;  // 佣金比例
  }
  ```

- [x] **4.2** 开发Controller接口
  ```java
  // 已在 GoodsSkuApi.java 中添加
  @GetMapping("/sku/{skuId}/calendar-prices")
  public AjaxResult getSkuCalendarPrices(
      @PathVariable String skuId,
      @RequestParam(defaultValue = "30") @Min(1) @Max(90) Integer days)
  ```
  > **✅ 接口路径：** `/weixin/api/ma/goodssku/sku/{skuId}/calendar-prices`
  > **✅ 参数校验：** 天数范围1-90天，带有@Min/@Max验证

- [x] **4.3** 实现Service业务逻辑
  - ✅ 查询指定SKU的近30天价格数据（从今天开始）
  - ✅ 生成完整的日期列表（支持自定义天数1-90天）
  - ✅ 将查询到的价格数据填充到日期列表中
  - ✅ 无价格数据的日期设置status=0（供小程序端置灰显示）
  - ✅ 按日期升序排序返回
  - ✅ SKU有效性验证（检查SKU是否存在且未删除）
  - ✅ 实现了 `IGoodsSkuService.getSkuCalendarPrices()` 方法

#### 5. **异常处理和数据校验**
- [ ] **5.1** 接口参数校验
  - 商品ID和SKU ID的格式校验
  - 天数参数的范围校验（建议1-90天）

- [ ] **5.2** 业务异常处理
  - 商品不存在的处理
  - SKU不存在的处理
  - SKU与商品不匹配的处理

- [ ] **5.3** 数据格式统一
  - 确保返回的JSON格式符合小程序端要求
  - 日期格式统一为 yyyy-MM-dd
  - 价格保留两位小数

### 🧪 **第三阶段：测试与文档**

#### 6. **接口测试**
- [ ] **6.1** 单元测试
  - 为新增的Service方法编写单元测试（BDD风格）
  ```java
  @Test
  public void should_return_all_valid_skus_when_goods_exists() {
      // Given - When - Then
  }
  
  @Test
  public void should_return_30_days_calendar_with_gray_dates_when_sku_has_partial_prices() {
      // Given - When - Then  
  }
  ```

- [ ] **6.2** API测试
  - 使用Postman或其他工具测试接口
  - 验证各种边界情况的处理

#### 7. **接口文档**
- [ ] **7.1** 编写API接口文档
  - 接口路径、请求参数、返回格式
  - 状态码说明和异常情况处理
  - 提供示例请求和响应

- [ ] **7.2** 与小程序端开发同事对接
  - 确认接口格式符合前端需求
  - 提供测试数据和测试环境

## 📝 关键技术细节

### SKU名称生成规则实现
```java
// 伪代码示例
public String generateDefaultSkuName(String goodsId, String skuId) {
    if (isUnifiedSpec(goodsId)) {
        return "默认规格";
    } else {
        // 查询SKU关联的规格值
        List<SpecValue> specValues = getSkuSpecValues(skuId);
        return specValues.stream()
            .map(SpecValue::getName)
            .collect(Collectors.joining("-"));
    }
}
```

### 价格日历数据处理
```java
// 伪代码示例
public List<SkuCalendarPriceAppDTO> getCalendarPrices(String skuId, Integer days) {
    LocalDate startDate = LocalDate.now();
    LocalDate endDate = startDate.plusDays(days - 1);
    
    // 查询实际价格数据
    Map<LocalDate, GoodsSkuCalendarPrice> priceMap = getPriceMap(skuId, startDate, endDate);
    
    // 生成完整日期列表
    return startDate.datesUntil(endDate.plusDays(1))
        .map(date -> {
            GoodsSkuCalendarPrice price = priceMap.get(date);
            return price != null ? 
                buildPriceDTO(price) : 
                buildEmptyPriceDTO(date); // status=0 置灰
        })
        .collect(Collectors.toList());
}
```

## 📊 数据库设计变更

### 新增字段
```sql
-- goods_sku 表新增字段
ALTER TABLE goods_sku ADD COLUMN sku_name VARCHAR(255) COMMENT 'SKU套餐名称';

-- 建议添加索引（可选）
CREATE INDEX idx_goods_sku_name ON goods_sku(goods_id, sku_name);
```

## 🔗 相关文件路径
- 后端SKU实体：`joolun-mall/src/main/java/com/joolun/mall/entity/GoodsSku.java`
- 管理端规格组件：`joolun-wx-ui/src/components/Specification/package.vue`
- 小程序商品详情页：`joolun-wx-uni/pages/commodityDetail/commodityDetail.vue`

## 📅 开发时间预估
- 第一阶段：2-3天
- 第二阶段：3-4天
- 第三阶段：1-2天
- **总计：6-9天**
