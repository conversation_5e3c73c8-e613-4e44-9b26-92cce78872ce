# 小程序端-订单接口测试计划

## 📋 测试概述
本测试计划涵盖订单模块的完整生命周期，重点验证状态一致性、超时处理机制和异常场景的兜底策略。

**测试重点**：
- ⭐ Redis键丢失时的兜底处理  
- ⭐ 订单和订单项状态一致性
- ⭐ 超时订单的自动处理

---

## 🚀 一、基础功能测试（必测）

### 1.1 订单创建流程
- [✅] **正常创建订单**
  - 接口：`POST /orderinfo/orderSub`
  - 测试数据：包含商品ID、SKU、数量、出行人、地址等
  - 验证：订单状态为待支付(0)，订单项状态为待支付(0)，Redis键设置成功，库存正确扣减，outTime返回剩余秒数 - 都有

- [✅] **库存不足场景**
  - 测试：购买数量超过库存
  - 验证：订单创建失败，返回库存不足提示，库存未被扣减 - 提示信息对于用户来说不太友好

- [✅] **SKU日历价格测试**
  - 测试：不同出发日期的价格计算
  - 验证：价格根据日历正确计算，库存从对应日期扣减

### 1.2 订单支付流程 - 测试环境的已测试通过
- [ ] **正常支付成功**
  - 接口：`POST /orderinfo/notify`（支付回调）
  - 验证：
  - OrderInfo.status(0→2)，
  - OrderItem.status(0→2)，
  - isPay(0→1)，商品销量增加，发送短信通知

- [ ] **0元订单支付**
  - 接口：`POST /orderinfo/processZeroPayment`
  - 验证：直接标记为已支付，状态正确更新，不调用微信支付

- [ ] **重复支付测试**
  - 测试：多次调用支付回调
  - 验证：第一次成功处理，后续调用被忽略，避免重复处理

### 1.3 订单查询功能
- [✅] **订单详情查询**
  - 接口：`GET /orderinfo/{id}`
  - 测试不同状态订单：
  - 待支付(返回outTime)，已测试
  - 已支付(outTime为null)，已测试
  - 已取消(outTime为null) 已测试 - 自动取消

- [✅] **订单项详情查询**
  - 接口：`GET /orderinfo/getOrderItemAppByOrderId/{id}`
  - 验证：返回完整的订单项信息，包含出行人、商品详情、状态描述等

- [✅] **订单列表查询**
  - 接口：`GET /orderinfo/page`
  - 测试多状态查询：`status="0,1,2"`
  - 验证：支持逗号分隔的多状态查询，分页功能正常，出行人数量统计正确 - 已测试，增加订单创建时间倒序输出以及订单创建时间展示

---

## ⏱️ 二、超时机制测试（重点）

### 2.1 正常超时流程
- [✅] **30分钟自动取消**
  - 测试：创建订单后等待30分钟或修改Redis过期时间
  - 验证：订单状态变为已取消(5)，订单项状态变为已取消(5)，库存回滚成功

### 2.2 Redis键丢失场景 ⭐⭐⭐
- [✅] **超时订单自动处理**
  - 测试步骤：
    1. 创建订单（31分钟前）
    2. 手动删除Redis键：`DEL mall:order:is_pay_0:{orderId}`
    3. 调用订单查询接口
  - 验证：订单自动取消，状态变为已取消(5)，库存回滚

- [✅] **未超时订单Redis键恢复**
  - 测试步骤：
    1. 创建订单（10分钟前）
    2. 手动删除Redis键：`DEL mall:order:is_pay_0:{orderId}`
    3. 调用订单查询接口
  - 验证：Redis键自动恢复，outTime正确计算剩余时间，订单状态保持待支付

### 2.3 边界时间测试 -  暂时不做
- [ ] **29分钟查询**
  - 验证：返回剩余倒计时（约60秒）

- [ ] **31分钟查询**
  - 验证：订单自动取消

- [ ] **30分0秒查询**
  - 验证：边界情况处理正确

---

## 🚚 三、订单发货和收货测试 - 没有发货环节

### 3.1 订单发货流程
- [ ] **正常发货**
  - 接口：`PUT /orderinfo`（更新物流信息）
  - 测试数据：包含物流公司和快递单号
  - 验证：物流信息更新成功，设置7天自动收货Redis键，deliveryTime设置为当前时间

- [ ] **重复发货测试**
  - 测试：对已发货订单再次发货
  - 验证：物流信息可以更新，不重复设置Redis键

### 3.2 订单收货流程
- [ ] **手动确认收货**
  - 接口：`PUT /orderinfo/receive`
  - 验证：OrderInfo.status(2→3)，OrderItem.status(2→3)，设置收货时间，设置评价标记，钱包状态更新

- [ ] **7天自动收货**
  - 测试：模拟Redis键过期触发自动收货
  - 验证：效果同手动确认收货

---

## ❌ 四、订单取消测试

### 4.1 用户主动取消
- [✅] **待支付订单取消**
  - 接口：`PUT /orderinfo/cancel`
  - 验证：OrderInfo.status(0→5)，OrderItem.status(0→5)，SKU库存回滚，SPU库存回滚（降级处理）。

- [✅] **已支付订单取消测试**
  - 测试：尝试取消已支付订单
  - 验证：取消失败，返回错误提示 - 订单已支付，无法取消订单 

---

## 💰 五、订单退款测试

### 5.1 退款申请
- [✅] **申请退款**
  - 接口：`POST /orderinfo/saveRefunds`
  - 验证：订单项状态变为退款中(4)，设置退款申请时间

### 5.2 退款处理（真实支付） - 暂未开发
- [ ] **24小时内退款**
  - 接口：`POST /orderinfo/doOrderRefunds`
  - 验证：按twentyFourHoursRefun比例计算退款金额

- [ ] **48小时内退款**
  - 验证：按fortyEightHoursRefun比例计算

- [ ] **7天内退款**
  - 验证：按sevenDayRefund比例计算

- [ ] **15天内退款**
  - 验证：按fifteenDayRefun比例计算

- [ ] **超过15天退款**
  - 验证：全额退款

### 5.3 模拟支付退款
- [✅] **模拟支付订单退款**
  - 接口：`POST /orderinfo/doOrderRefundsForMock`
  - 验证：直接标记退款成功，不调用微信退款接口

---

## 🚨 六、异常场景测试

### 6.1 数据异常测试
- [✅] **订单不存在**
  - 测试：使用不存在的订单ID查询
  - 验证：返回404或相应错误提示

- [✅] **SKU不存在**
  - 测试：创建订单时使用不存在的SKU
  - 验证：创建失败，返回商品不存在提示

- [ ] **用户权限测试**
  - 测试：查询他人订单
  - 验证：权限校验正常

### 6.2 Redis异常测试
- [ ] **Redis服务异常**
  - 测试：Redis服务不可用时的订单操作
  - 验证：基本功能不受影响，只是缺少超时机制

### 6.3 并发测试 - 当前是可以反复调用下单接口
- [ ] **同时购买同一SKU**
  - 测试：多用户并发购买库存为1的商品
  - 验证：只有一个用户成功，其他用户提示库存不足

- [ ] **重复点击测试**
  - 测试：快速重复点击支付/取消按钮
  - 验证：操作幂等性，避免重复处理

---

## 🔄 七、状态一致性测试（重点）⭐⭐⭐

### 7.1 订单状态同步验证
- [ ] **支付成功后状态检查**
  - 验证：OrderInfo.status 和 OrderItem.status 都更新为2

- [ ] **订单取消后状态检查**
  - 验证：OrderInfo.status 和 OrderItem.status 都更新为5

- [ ] **确认收货后状态检查**
  - 验证：OrderInfo.status 和 OrderItem.status 都更新为3

### 7.2 状态描述一致性
- [ ] **状态描述验证**
  - 验证：statusDesc 与实际status值对应正确

---

## 📊 八、数据统计测试

### 8.1 订单统计功能
- [ ] **出行人数量统计**
  - 验证：adultQuantity 和 childrenQuantity 统计正确

- [ ] **订单金额计算**
  - 验证：总金额、实付金额、优惠金额计算正确

---

## ✅ 测试完成标准

**基础功能测试**：所有checkbox都勾选完成  
**重点场景测试**：标记⭐的场景必须全部通过  
**异常处理测试**：至少完成80%的异常场景测试  

---

## 📝 测试记录

**测试环境**：_____________  
**测试时间**：_____________  
**测试人员**：_____________  

**发现问题记录**：
- [ ] 问题1：___________________________
- [ ] 问题2：___________________________
- [ ] 问题3：___________________________

**测试结论**：
- [ ] 功能测试通过
- [ ] 性能测试通过  
- [ ] 异常测试通过
- [ ] 整体测试通过，可以发布
