# 小程序端-支付接口区分测试和正式

## 开发要求

优先完成相关单元测试后再修改现有的接口代码

涉及接口：

1. 统一下单接口 `/weixin/api/ma/orderinfo/unifiedOrder`
2. 支付回调 `/weixin/api/ma/orderinfo/notify-order` 
3. 发起退款 `/weixin/api/ma/orderinfo/refunds`
4. 退款回调 `/weixin/api/ma/orderinfo/notify-refunds`

---

## 📚 技术文档 & 运维文档

### 🎯 改造完成情况
✅ **已完成** - Spring Profile + 模拟支付的混合方案实施

#### 改造内容
1. **单元测试编写**：15个测试用例，覆盖支付核心场景
2. **统一支付服务**：`WxPaymentService` 封装所有支付逻辑
3. **模拟支付服务**：`MockPaymentService` 用于开发和测试环境
4. **环境配置**：dev/test/prod 三环境配置文件
5. **接口改造**：4个核心支付接口完全改造

#### 技术架构
```
PaymentConfig (配置类)
├── MockPaymentService (模拟支付)
└── WxPaymentService (统一支付服务)
    ├── 模拟支付处理
    └── 真实支付处理
```

### 🔧 环境配置说明

#### 开发环境 (dev)
- **模拟支付**：启用，95%成功率，2秒延迟
- **支付超时**：30分钟
- **日志级别**：DEBUG

#### 测试环境 (test)  
- **模拟支付**：启用，98%成功率，1秒延迟
- **支付超时**：15分钟
- **日志级别**：INFO

#### 生产环境 (prod)
- **模拟支付**：禁用，使用真实微信支付
- **支付超时**：60分钟  
- **日志级别**：WARN

### 📋 接口变更

| 接口 | 原逻辑 | 新逻辑 | 说明 |
|-----|-------|-------|------|
| `/unifiedOrder` | 直接调用微信API | 通过WxPaymentService | 支持环境区分 |
| `/notify-order` | 直接解析处理 | 通过WxPaymentService | 统一回调处理 |
| `/refunds` | 直接调用service | 通过WxPaymentService | 支持模拟退款 |
| `/notify-refunds` | 直接解析处理 | 通过WxPaymentService | 统一退款回调 |

### 🚀 部署指南

#### 1. 启动参数配置
```bash
# 开发环境
java -jar app.jar --spring.profiles.active=dev

# 测试环境  
java -jar app.jar --spring.profiles.active=test

# 生产环境
java -jar app.jar --spring.profiles.active=prod
```

#### 2. 配置文件检查
确保以下配置文件存在且正确：
- `application-dev.yml`
- `application-test.yml` 
- `application-prod.yml`

#### 3. 日志监控
关键日志前缀：
- `[PAYMENT]` - 支付处理日志
- `[MOCK_PAYMENT]` - 模拟支付日志

### 🧪 测试验证

#### 单元测试
```bash
mvn test -Dtest=PaymentServiceTest
mvn test -Dtest=WxPayConfigurationTest
```

#### 功能测试
1. **开发环境**：验证模拟支付是否正常工作
2. **测试环境**：验证模拟支付高成功率场景
3. **生产环境**：验证真实微信支付集成

### ⚠️ 注意事项

#### 开发注意事项
- 模拟支付延迟可通过配置调整
- 模拟支付成功率可配置，便于测试失败场景
- 所有支付相关日志都有统一前缀，便于监控

#### 运维注意事项  
- 生产环境务必确保 `payment.mock.enabled=false`
- 支付回调URL需根据环境配置正确的域名
- 建议定期检查支付超时订单处理情况

#### 故障排查
1. **支付失败**：检查当前环境配置和日志
2. **回调异常**：查看 `[PAYMENT]` 前缀日志
3. **环境切换**：确认 `spring.profiles.active` 配置

### 📊 监控指标

建议监控以下指标：
- 支付成功率
- 支付平均响应时间  
- 回调处理成功率
- 超时订单数量

---

*文档更新时间：2024-12-25*  
*改造负责人：JL开发团队*