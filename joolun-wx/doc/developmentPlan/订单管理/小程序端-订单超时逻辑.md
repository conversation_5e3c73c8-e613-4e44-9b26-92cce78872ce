# 小程序端-订单超时逻辑

## 概述
`outTime` 是一个倒计时功能，用于自动处理订单状态变更。主要有两种场景：
- **订单支付超时**：未支付订单30分钟后自动取消
- **订单收货超时**：已发货订单7天后自动确认收货

## 执行流程图
```mermaid
graph TD
    A[订单创建] --> B[设置支付超时键<br/>30分钟]
    B --> C[用户查询订单]
    C --> D[获取Redis键剩余时间<br/>作为outTime返回]
    
    E[订单发货] --> F[设置收货超时键<br/>7天]
    F --> G[用户查询订单]
    G --> H[获取Redis键剩余时间<br/>作为outTime返回]
    
    I[Redis键过期] --> J[触发监听器]
    J --> K{键类型判断}
    K -->|支付超时| L[自动取消订单]
    K -->|收货超时| M[自动确认收货]
    
    L --> N[订单状态变更为已取消<br/>回滚库存]
    M --> O[订单状态变更为已完成<br/>释放佣金]
```

## 详细实现逻辑

### 1. Redis键设置逻辑

#### 1.1 订单创建时设置支付超时
**位置**: `OrderPersistenceStep.setOrderTimeout()`
```java
// 设置30分钟支付超时
String keyRedis = String.valueOf(
    StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_IS_PAY_0, orderInfo.getId()));

redisTemplate.opsForValue().set(keyRedis, orderInfo.getOrderNo(), 
    DEFAULT_ORDER_TIMEOUT_MINUTES, TimeUnit.MINUTES); // 30分钟
```

#### 1.2 订单发货时设置收货超时
**位置**: `OrderInfoServiceImpl.updateById()`
```java
// 发货后设置7天自动收货超时
if(StrUtil.isNotBlank(entity.getLogistics()) && StrUtil.isNotBlank(entity.getLogisticsNo())){
    String keyRedis = String.valueOf(
        StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_STATUS_2, entity.getId()));
    
    redisTemplate.opsForValue().set(keyRedis, entity.getOrderNo(), 
        MallConstants.ORDER_TIME_OUT_2, TimeUnit.DAYS); // 7天
}
```

### 2. 超时时间获取逻辑

#### 2.1 查询订单详情时获取剩余时间
**位置**: `OrderInfoServiceImpl.getById2()` 和 `OrderInfoServiceImpl.getOrderItemAppByOrderId()`
```java
String keyRedis = null;

// 获取自动取消倒计时（未支付订单）
if(CommonConstants.NO.equals(orderInfo.getIsPay())){
    keyRedis = String.valueOf(
        StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_IS_PAY_0, orderInfo.getId()));
}

// 获取Redis键的剩余过期时间
if(keyRedis != null){
    Long outTime = redisTemplate.getExpire(keyRedis);
    if(outTime != null && outTime > 0){
        orderInfo.setOutTime(outTime); // 设置剩余秒数
    }
}
```

### 3. Redis过期监听处理

#### 3.1 监听器配置
**位置**: `RedisKeyExpirationListener.onMessage()`
```java
@Component
public class RedisKeyExpirationListener implements MessageListener {
    
    @Override
    public void onMessage(Message message, byte[] bytes) {
        String channel = String.valueOf(serializer.deserialize(message.getChannel()));
        String body = String.valueOf(serializer.deserialize(message.getBody()));
        
        // 监听key过期事件
        if(StrUtil.format("__keyevent@{}__:expired", database).equals(channel)){
            
            // 处理支付超时 - 自动取消订单
            if(body.contains(MallConstants.REDIS_ORDER_KEY_IS_PAY_0)) {
                String orderId = extractOrderId(body);
                OrderInfo orderInfo = orderInfoService.getById(orderId);
                if(orderInfo != null && CommonConstants.NO.equals(orderInfo.getIsPay())){
                    orderInfoService.orderCancel(orderInfo); // 自动取消订单
                }
            }
            
            // 处理收货超时 - 自动确认收货
            if(body.contains(MallConstants.REDIS_ORDER_KEY_STATUS_2)) {
                String orderId = extractOrderId(body);
                OrderInfo orderInfo = orderInfoService.getById(orderId);
                if(orderInfo != null && OrderInfoEnum.STATUS_2.getValue().equals(orderInfo.getStatus())){
                    orderInfoService.orderReceive(orderInfo); // 自动确认收货
                }
            }
        }
    }
}
```

### 4. 时间常量定义

**位置**: `MallConstants.java`
```java
public interface MallConstants {
    // 支付超时时间：30分钟
    long ORDER_TIME_OUT_0 = 30;
    
    // 收货超时时间：7天
    long ORDER_TIME_OUT_2 = 7;
    
    // Redis键前缀
    String REDIS_ORDER_KEY_IS_PAY_0 = "mall:order:is_pay_0:";
    String REDIS_ORDER_KEY_STATUS_2 = "mall:order:status_2:";
}
```

## 业务场景说明

### 场景1：支付超时自动取消
1. **触发条件**：订单创建后30分钟内未支付
2. **处理逻辑**：
   - 订单状态变更为已取消（STATUS_5）
   - 回滚库存（SKU日历价格库存 + SPU库存）
   - 清理相关Redis键

### 场景2：收货超时自动确认
1. **触发条件**：订单发货后7天内用户未主动确认收货
2. **处理逻辑**：
   - 订单状态变更为已完成（STATUS_3）
   - 设置评价标记为待评价
   - 释放佣金（钱包状态变更为已结算）

## 技术特点

1. **实时倒计时**：`outTime` 返回的是Redis键的剩余过期时间（秒）
2. **自动化处理**：基于Redis过期事件自动触发订单状态变更
3. **业务分离**：支付超时和收货超时使用不同的Redis键
4. **防重复处理**：监听器中有状态检查，避免重复处理
5. **容错机制**：即使Redis服务异常，也不影响主业务流程

## 前端展示逻辑

### 小程序端倒计时显示
```javascript
// 前端通过 outTime 字段显示倒计时
if (orderInfo.outTime && orderInfo.outTime > 0) {
    // 将秒数转换为时分秒格式显示
    const hours = Math.floor(orderInfo.outTime / 3600);
    const minutes = Math.floor((orderInfo.outTime % 3600) / 60);
    const seconds = orderInfo.outTime % 60;
    
    // 显示格式：HH:MM:SS
    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}
```

## 注意事项

1. **Redis配置**：需要开启Redis键过期事件通知
2. **时区问题**：确保服务器时区设置正确
3. **并发处理**：监听器中的订单状态检查防止并发问题
4. **性能考虑**：大量订单时需要注意Redis性能影响
5. **业务一致性**：确保订单状态变更的事务性