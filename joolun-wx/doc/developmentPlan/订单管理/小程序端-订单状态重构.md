# 小程序端-订单状态重构

## 改造背景

当前系统订单状态定义与实际业务逻辑不一致，存在以下问题：
1. 枚举定义中缺少"退款中"、"同意退款"、"拒绝退款"等状态
2. 业务代码中硬编码状态值，与枚举定义不匹配
3. 业务流程中没有"待发货"环节，支付完成后直接进入"待使用"状态
4. 前端API统计了不会使用的状态，导致数据显示不准确

## 问题分析

### 1. 枚举定义不完整
原有枚举缺少退款相关状态：
```java
// 原有定义
STATUS_4("4","退款中"),    // 缺失
STATUS_7("7","同意退款"),  // 缺失 
STATUS_8("8","拒绝退款");  // 缺失
```

### 2. 业务代码状态值不一致
在 `OrderInfoServiceImpl.java` 中发现：
```java
// 代码中使用硬编码
orderItem.setStatus("1");                     // 实际表示"退款中"
if("3".equals(orderItem.getStatus())){        // 实际表示"同意退款"
}else if("2".equals(orderItem.getStatus())){  // 实际表示"拒绝退款"
```

但枚举定义中：
```java
STATUS_1("1","待发货"),  // 与实际业务不符
STATUS_2("2","待使用"),  // 与实际业务不符
STATUS_3("3","已完成"); // 与实际业务不符
```

### 3. 无效状态统计
前端API在统计永远不会使用的"待发货"状态，导致该数值恒为0。

## 解决方案

### 1. 完善枚举定义
添加缺失的退款相关状态：
```java
public enum OrderInfoEnum implements IEnum<String> {
    STATUS_0("0","待付款"),
    STATUS_1("1","待发货"),    // 暂时不用，当前业务直接跳过此状态
    STATUS_2("2","待使用"),
    STATUS_3("3","已完成"),
    STATUS_4("4","退款中"),    // 新增
    STATUS_5("5","已取消"),
    STATUS_6("6","已退款"),
    STATUS_7("7","同意退款"), // 新增
    STATUS_8("8","拒绝退款"); // 新增
}
```

### 2. 统一业务代码状态值
将所有硬编码状态值替换为枚举值：
```java
// 修改前
orderItem.setStatus("1");
if("3".equals(orderItem.getStatus())){
}else if("2".equals(orderItem.getStatus())){

// 修改后  
orderItem.setStatus(OrderInfoEnum.STATUS_4.getValue());
if(OrderInfoEnum.STATUS_7.getValue().equals(orderItem.getStatus())){
}else if(OrderInfoEnum.STATUS_8.getValue().equals(orderItem.getStatus())){
```

### 3. 调整API统计逻辑
注释掉不使用的状态统计：
```java
// STATUS_1("待发货")暂时不用，当前业务直接跳过此状态，故不统计
// countAll.put(OrderInfoEnum.STATUS_1.getValue(), ...
```

## 具体修改内容

### 1. 枚举文件修改
**文件**: `joolun-mall/src/main/java/com/joolun/mall/enums/OrderInfoEnum.java`
- 添加 `STATUS_4("4","退款中")`
- 添加 `STATUS_7("7","同意退款")`  
- 添加 `STATUS_8("8","拒绝退款")`
- 为 `STATUS_1` 添加注释说明暂时不用

### 2. 业务逻辑修改
**文件**: `joolun-mall/src/main/java/com/joolun/mall/service/impl/OrderInfoServiceImpl.java`
- `saveRefunds()` 方法：`setStatus("1")` → `setStatus(OrderInfoEnum.STATUS_4.getValue())`
- `doOrderRefunds()` 方法：状态判断改用枚举值
- `notifyRefunds()` 方法：状态判断改用枚举值

### 3. 支付服务修改
**文件**: `joolun-mall/src/main/java/com/joolun/mall/service/WxPaymentService.java`
- 退款条件判断改用枚举值
- 添加 `OrderInfoEnum` 导入

### 4. API接口修改
**文件**: `joolun-admin/src/main/java/com/joolun/web/api/OrderInfoApi.java`
- 注释掉 `STATUS_1` 的订单统计逻辑

### 5. 测试代码修改
**文件**: `joolun-mall/src/test/java/com/joolun/mall/service/impl/PaymentServiceTest.java`
- 退款条件测试改用枚举值

## 订单状态流转图

```mermaid
graph LR
    A["STATUS_0<br/>待付款"] --> B["STATUS_2<br/>待使用"]
    B --> C["STATUS_3<br/>已完成"]
    B --> D["STATUS_4<br/>退款中"]
    D --> E["STATUS_7<br/>同意退款"]
    D --> F["STATUS_8<br/>拒绝退款"]
    E --> G["STATUS_6<br/>已退款"]
    A --> H["STATUS_5<br/>已取消"]
    
    I["STATUS_1<br/>待发货"] -.-> J["暂时不用<br/>跳过此状态"]
    
    style I fill:#f9f9f9,stroke:#ccc,stroke-dasharray: 5 5
    style J fill:#fff2cc,stroke:#d6b656
```

## 核心业务流程

### 1. 正常购买流程
1. 用户下单 → `STATUS_0("待付款")`
2. 支付完成 → `STATUS_2("待使用")` (跳过待发货)
3. 服务完成 → `STATUS_3("已完成")`

### 2. 退款流程
1. 用户申请退款 → `STATUS_4("退款中")`
2. 商家处理：
   - 同意 → `STATUS_7("同意退款")` → `STATUS_6("已退款")`
   - 拒绝 → `STATUS_8("拒绝退款")`

### 3. 取消流程
1. 订单取消 → `STATUS_5("已取消")`

## 注意事项

1. **向后兼容性**: 保留了 `STATUS_1`，将来如需发货功能可直接启用
2. **数据一致性**: 所有状态判断统一使用枚举值，避免硬编码
3. **前端适配**: 前端需要适配新的状态码，特别是退款相关状态
4. **测试覆盖**: 已更新相关单元测试，确保状态流转正确

## 风险提示

1. 如果数据库中已存在使用旧状态值的数据，需要进行数据迁移
2. 前端小程序需要同步更新状态处理逻辑
3. 其他依赖订单状态的功能模块需要检查兼容性

## 后续优化建议

1. 考虑创建专门的 `OrderItemStatusEnum` 区分订单状态和订单项状态
2. 添加状态机模式，控制状态流转的合法性
3. 完善订单状态变更的日志记录和审计功能


## 老的状态
<el-tab-pane name="-1">
  <span slot="label"><i class="el-icon-s-order" /> 全部订单</span>
</el-tab-pane>
<el-tab-pane name="0">
  <span slot="label"><i class="el-icon-bank-card" /> 待付款</span>
</el-tab-pane>
<el-tab-pane name="2">
  <span slot="label"><i class="el-icon-truck" /> 待使用</span>
</el-tab-pane>
<el-tab-pane name="3">
  <span slot="label"><i class="el-icon-document" /> 已完成</span>
</el-tab-pane>
<el-tab-pane name="5">
  <span slot="label"><i class="el-icon-circle-close" /> 已取消</span>
</el-tab-pane>
<el-tab-pane name="6">
  <span slot="label"><i class="el-icon-circle-close" /> 已退款</span>
</el-tab-pane>


## 修复后的完整流程
### 模拟退款流程（修复后）
```mermaid
graph LR
    A["用户申请退款"] --> B["STATUS_4<br/>退款中"]
    B --> C["管理员审批"]
    C --> D["同意退款<br/>STATUS_7"]
    C --> E["拒绝退款<br/>STATUS_8"]
    D --> F["模拟退款成功<br/>STATUS_6已退款"]
```

### 真实退款流程（保持不变）
```mermaid
graph LR
    A["用户申请退款"] --> B["STATUS_4<br/>退款中"]
    B --> C["管理员审批"]
    C --> D["同意退款<br/>STATUS_7"]
    C --> E["拒绝退款<br/>STATUS_8"]
    D --> F["调用微信API"]
    F --> G["微信回调"]
    G --> H["STATUS_6<br/>已退款"]
```