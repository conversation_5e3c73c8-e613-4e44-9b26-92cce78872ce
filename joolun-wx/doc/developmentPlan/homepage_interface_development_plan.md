# 首页及线路接口对接开发计划

## 一、背景
首页静态页面已完成，现需对接后端接口，实现线路分页、搜索、Tab切换、目的地筛选等功能。需梳理现有接口、识别缺口，明确前后端开发任务。

---

## 二、需求梳理
1. 首页主列表展示"线路"分页数据，支持Tab切换（线路类型）、目的地单选、关键词模糊搜索。
2. 公告/通知区块对接现有接口。
3. 搜索页仅查线路，支持目的地单选、关键词模糊搜索。
4. 价格区间、天数、出发时间等筛选暂不开发。

---

## 三、后端接口改造/新增
### 1. 线路分页接口 `/weixin/api/ma/routes/page`
- 支持参数：
  - `current`：当前页码
  - `size`：每页条数
  - `type`：线路类型（字典lv_routes_type）
  - `destinationId`：目的地ID（单选）
  - `name`：线路名称/关键词（模糊搜索）
- 价格区间、天数等参数暂不实现

### 2. 目的地树接口
- 复用`/weixin/api/ma/city/getDestTree`或`/weixin/api/ma/city/treeselect`

### 3. 数据字典接口
- 如需动态获取type列表，使用`/sys/dict/data/type/lv_routes_type`

---

## 四、前端对接/开发任务
### 1. 首页
- c-scroll-view的api切换为routesPage，params结构调整，支持type、destinationId、name
- Tab切换时，params.type动态变化，刷新列表
- 目的地筛选为单选，params.destinationId变化，刷新列表
- 搜索栏输入关键词后，params.name变化，刷新列表

### 2. 搜索页
- 只查线路，复用routesPage接口
- 支持目的地单选、关键词模糊搜索

### 3. 公告/通知
- 继续对接noticeList接口

### 4. 数据字典/目的地树接口对接
- Tab栏type值可写死，也可通过数据字典接口动态获取

---

## 五、任务分解与优先级建议
1. 后端接口改造：routesPage接口参数支持type、destinationId、name（模糊），保持分页兼容 **【已完成】**
2. 前端首页对接：切换api，调整params，Tab、目的地、搜索栏联动 **【已完成】**
3. 前端搜索页对接：目的地单选、关键词模糊 **【已完成】**
4. 数据字典/目的地树接口对接（如需动态获取type/目的地） **【已完成】**

---

## 六、其他说明
- 价格区间、天数、出发时间等筛选暂不开发，后续如有需求可补充
- 目的地筛选为单选，UI可用下拉/弹窗/选择器
- 关键词为模糊搜索，前端直接传name参数即可 