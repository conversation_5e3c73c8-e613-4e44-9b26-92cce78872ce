# 小程序端-用户历史搜索

## 增加用户历史搜索功能，大概保留7天的记录，是不是可以借助Redis缓存实现？

---

## 🎯 开发任务 ToDoList

### 📋 后端开发任务

#### 1. 数据设计与缓存方案
- [x] 设计Redis缓存键名规范 (例如: `search_history:{userId}`)
- [x] 定义搜索历史数据结构 (关键词、搜索时间、搜索类型等)
- [x] 配置Redis过期时间策略 (7天自动清理)
- [x] 设计搜索历史最大保存条数限制 (建议10-20条)

#### 2. 后端接口开发
- [x] 创建搜索历史相关实体类和VO类
- [x] 开发保存搜索历史接口 `POST /weixin/api/ma/search/history`
- [x] 开发获取搜索历史列表接口 `GET /weixin/api/ma/search/history`
- [x] 开发删除单条搜索历史接口 `DELETE /weixin/api/ma/search/history/{keyword}`
- [x] 开发清空搜索历史接口 `DELETE /weixin/api/ma/search/history/clear`
- [x] 集成Redis操作，实现自动去重和排序

#### 3. 业务逻辑实现
- [x] 搜索关键词去重处理 (相同关键词更新时间)
- [x] 实现LRU淘汰策略 (超出数量限制时删除最旧记录)
- [x] 添加敏感词过滤机制
- [x] 实现按时间倒序返回历史记录
- [x] 集成用户身份验证和权限控制
- [x] 处理用户登录状态对搜索历史的影响

---

**预估开发周期：** 2-3个工作日  
**技术栈：** SpringBoot + Redis + MybatisPlus  
**核心难点：** Redis缓存策略设计、搜索历史去重逻辑

---

## 🚀 功能特性

### 已实现的核心功能
1. **Redis缓存存储** - 使用Redis存储搜索历史，支持7天自动过期
2. **LRU淘汰策略** - 超过20条记录时自动删除最旧的记录
3. **关键词去重** - 相同关键词会更新到最新时间
4. **敏感词过滤** - 基础的敏感词过滤机制
5. **多种搜索类型** - 支持线路、商品、文章和综合搜索
6. **用户身份验证** - 集成ThirdSession验证用户身份

### API接口说明

#### 1. 保存搜索历史
```
POST /weixin/api/ma/search/history
Content-Type: application/json

{
  "keyword": "北京旅游",
  "searchType": "route",
  "resultCount": 15
}
```

#### 2. 获取搜索历史列表
```
GET /weixin/api/ma/search/history
```

#### 3. 删除单条搜索历史
```
DELETE /weixin/api/ma/search/history/{keyword}
```

#### 4. 清空搜索历史
```
DELETE /weixin/api/ma/search/history/clear
```

### 搜索类型说明
- `route` - 线路搜索
- `goods` - 商品搜索  
- `article` - 文章搜索
- `all` - 综合搜索

### Redis缓存键设计
- 键格式：`search_history:{userId}`
- 过期时间：7天（604800秒）
- 数据结构：List&lt;SearchHistory&gt;

### 配置说明
- **最大保存条数**：20条（SearchConstants.SEARCH_HISTORY_MAX_SIZE）
- **过期时间**：7天（SearchConstants.SEARCH_HISTORY_EXPIRE_TIME）
- **关键词最大长度**：50个字符
