# SPA 路由部署问题排查清单

## 问题现象
- 本地开发环境刷新页面正常
- 生产环境刷新页面出现 404 或无法访问

## 问题原因
Vue Router 使用 `history` 模式时，需要服务器支持将所有未匹配的路由重定向到 `index.html`

## 解决方案检查清单

### 1. Nginx 配置检查
确保 Nginx 配置文件包含以下关键配置：

```nginx
location / {
    root   /path/to/your/dist;
    try_files $uri $uri/ /index.html;
    index  index.html index.htm;
}
```

### 2. 部署验证步骤

#### 步骤1：检查构建产物
```bash
# 确认 dist 目录存在且包含 index.html
ls -la dist/
```

#### 步骤2：检查 Nginx 配置是否生效
```bash
# 进入容器检查配置
docker exec -it nginx-container nginx -t
docker exec -it nginx-container nginx -s reload
```

#### 步骤3：测试服务器响应
```bash
# 测试根路径
curl -I http://your-domain.com/

# 测试不存在的路径是否返回 index.html
curl -I http://your-domain.com/mall/goods
```

### 3. 常见问题及解决方案

#### 问题1：Nginx 配置未生效
**解决方案：**
```bash
# 重新加载 Nginx 配置
docker-compose restart nginx
# 或
nginx -s reload
```

#### 问题2：路径配置错误
**检查项：**
- `vue.config.js` 中的 `publicPath` 设置
- Nginx 中的 `root` 路径是否正确
- 静态资源路径是否正确

#### 问题3：缓存问题
**解决方案：**
```bash
# 清除浏览器缓存
# 或在 Nginx 中添加缓存控制头
add_header Cache-Control "no-cache, no-store, must-revalidate";
```

### 4. 调试技巧

#### 查看 Nginx 访问日志
```bash
docker logs nginx-container
tail -f /var/log/nginx/access.log
```

#### 查看 Nginx 错误日志
```bash
tail -f /var/log/nginx/error.log
```

#### 使用浏览器开发者工具
1. 打开 Network 面板
2. 刷新页面
3. 查看是否返回 404 状态码
4. 查看响应内容是否为 index.html

### 5. 快速验证脚本

创建 `verify-spa-routing.sh` 脚本：

```bash
#!/bin/bash
echo "=== SPA 路由部署验证 ==="

# 检查根路径
echo "检查根路径..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/)
if [ "$response" -eq 200 ]; then
    echo "✅ 根路径正常"
else
    echo "❌ 根路径异常: $response"
fi

# 检查前端路由
echo "检查前端路由..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/mall/goods)
if [ "$response" -eq 200 ]; then
    echo "✅ 前端路由正常"
else
    echo "❌ 前端路由异常: $response"
fi

echo "=== 验证完成 ==="
```

### 6. 应急方案：使用 Hash 模式

如果 Nginx 配置困难，可以临时切换到 Hash 模式：

**修改 `router/index.js`：**
```javascript
export default new Router({
  mode: 'hash', // 改为 hash 模式
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
```

**优缺点：**
- ✅ 无需服务器配置
- ❌ URL 包含 # 符号，不够美观
- ❌ SEO 不友好 