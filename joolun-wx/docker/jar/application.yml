# 项目相关配置
ruoyi:
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/joolun/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/joolun/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为7500
  port: 7500
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.joolun: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles: 
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: joolun-redis
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30
  
# MyBatisPlus配置
mybatis-plus:
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 自定义TypeHandler
  type-handlers-package: com.joolun.framework.config.typehandler
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.joolun.**.domain,com.joolun.**.entity
  global-config:
    #是否控制台
    banner: false
    db-config:
      #主键类型
      id-type: auto

# PageHelper分页插件
pagehelper: 
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql 

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

wx:
  # 公众号配置
  mp:
    configs:
      - appId: xxxxxxxx
        secret: xxxxxxxxxxxxxxxxxxxxxxxxxx
        token: xxxxxxxxxx
        aesKey: xxxxxxxxxxxxxxxxxxxx
  # 小程序配置
  ma:
    configs:
      - appId: xxxxxxxxxxxxxxxx
        secret: xxxxxxxxxxxxx
        # 微信支付商户号，请去微信支付平台申请
        mchId: 1588227511
        # 微信支付商户APIv2密钥，请去微信支付平台申请
        mchKey: xxxxxxxxxxxxxxxxxxxxx
        # p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
        keyPath: classpath:apiclient_cert.p12

mall:
  # 支付、物流回调地址，即后台服务7500端口的外网访问域名，要保证外网能访问，如：https://后台地址/prod-api
  notify-host: http://xx.xxxx.com