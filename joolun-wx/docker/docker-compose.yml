version : '3'
services:

  joolun-mysql:
    build:
      context: .
      dockerfile: mysql-dockerfile
    environment:
      MYSQL_ROOT_PASSWORD: root
      TZ: Asia/Shanghai
    restart: always
    container_name: joolun-mysql
    image: joolun-mysql
    ports:
      - 3306:3306
    privileged: true
    volumes:
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/logs
      - ./mysql/data:/var/lib/mysql
    command: --lower_case_table_names=1

  joolun-redis:
    container_name: joolun-redis
    image: redis
    build:
      context: .
      dockerfile: redis-dockerfile
    ports:
      - "6379:6379"
    volumes:
      - ./conf/redis.conf:/home/<USER>/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /home/<USER>/redis/redis.conf
  joolun-nginx:
    container_name: joolun-nginx
    image: nginx
    build:
      context: .
      dockerfile: nginx-dockerfile
    ports:
      - "80:80"
    volumes:
      - ./dist:/home/<USER>/projects/joolun-ui
      - ./conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - joolun-server
    links:
      - joolun-server
  joolun-server:
    container_name: joolun-server
    build:
      context: .
      dockerfile: joolun-dockerfile
    ports:
      - "7500:7500"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./joolun/logs:/home/<USER>/logs
      - ./joolun/uploadPath:/home/<USER>/uploadPath
    depends_on:
      - joolun-mysql
      - joolun-redis
    links:
      - joolun-mysql
      - joolun-redis