package com.joolun.common.utils;

import java.util.Calendar;
import java.util.Date;

/**
 * DateUtils 测试类
 */
public class DateUtilsTest {

    public static void main(String[] args) {
        testGetDayOfWeek();
    }

    public static void testGetDayOfWeek() {
        System.out.println("=== 测试 DateUtils.getDayOfWeek 方法 ===");
        
        try {
            // 测试周一
            Calendar calendar = Calendar.getInstance();
            calendar.set(2024, Calendar.DECEMBER, 2); // 2024-12-02 是周一
            Date monday = calendar.getTime();
            String mondayResult = DateUtils.getDayOfWeek(monday);
            System.out.println("2024-12-02 是: " + mondayResult);
            assert "周一".equals(mondayResult) : "周一测试失败，期望: 周一, 实际: " + mondayResult;
            
            // 测试周二
            calendar.set(2024, Calendar.DECEMBER, 3); // 2024-12-03 是周二
            Date tuesday = calendar.getTime();
            String tuesdayResult = DateUtils.getDayOfWeek(tuesday);
            System.out.println("2024-12-03 是: " + tuesdayResult);
            assert "周二".equals(tuesdayResult) : "周二测试失败，期望: 周二, 实际: " + tuesdayResult;
            
            // 测试周日
            calendar.set(2024, Calendar.DECEMBER, 1); // 2024-12-01 是周日
            Date sunday = calendar.getTime();
            String sundayResult = DateUtils.getDayOfWeek(sunday);
            System.out.println("2024-12-01 是: " + sundayResult);
            assert "周日".equals(sundayResult) : "周日测试失败，期望: 周日, 实际: " + sundayResult;
            
            // 测试空日期
            String nullResult = DateUtils.getDayOfWeek(null);
            System.out.println("空日期处理: '" + nullResult + "'");
            assert "".equals(nullResult) : "空日期测试失败，期望: '', 实际: " + nullResult;
            
            System.out.println("✅ 所有测试通过！");
            
            // 测试更多日期
            System.out.println("\n=== 更多测试示例 ===");
            calendar.set(2024, Calendar.DECEMBER, 4); // 2024-12-04 是周三
            System.out.println("2024-12-04 是: " + DateUtils.getDayOfWeek(calendar.getTime()));
            
            calendar.set(2024, Calendar.DECEMBER, 5); // 2024-12-05 是周四
            System.out.println("2024-12-05 是: " + DateUtils.getDayOfWeek(calendar.getTime()));
            
            calendar.set(2024, Calendar.DECEMBER, 6); // 2024-12-06 是周五
            System.out.println("2024-12-06 是: " + DateUtils.getDayOfWeek(calendar.getTime()));
            
            calendar.set(2024, Calendar.DECEMBER, 7); // 2024-12-07 是周六
            System.out.println("2024-12-07 是: " + DateUtils.getDayOfWeek(calendar.getTime()));
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 