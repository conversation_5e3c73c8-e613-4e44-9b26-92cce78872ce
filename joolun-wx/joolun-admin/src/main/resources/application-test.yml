# 项目相关配置
ruoyi:
  # 文件路径 示例（ Windows配置D:/joolun/uploadPath，Linux配置 /home/<USER>/uploadPath）
  #    profile: /Users/<USER>/Software/oschina-workspaces/project-temp
  profile: /www/wwwroot/tongyou_test/uploadPath

# 自定义日志路径配置
log:
  path: /home/<USER>/logs

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为7500
  port: 8089
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 数据源配置
spring:
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 1
    # 密码
    password: YouTong.!@34
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ******************************************************************************************************************************************************
        username: lvyoutest
        password: 86450558z
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

aliyun:
  oss:
    env: test
    endpoint: oss-cn-beijing.aliyuncs.com
    keyId: LTAI5t6EcYJz9wgeLL9riAey
    keySecret: ******************************
    bucketName: xiakexing-wx

# 支付配置
payment:
  # 模拟支付配置（生产环境禁用）
  mock:
    enabled: false               # 禁用模拟支付
    success-rate: 100
    delay-seconds: 0
    failure-reason: "支付失败"
    callback-delay: false
    callback-delay-seconds: 0

  # 支付超时配置
  timeout:
    payment-timeout-minutes: 60      # 生产环境超时1小时
    auto-cancel-expired-orders: true # 自动取消超时订单
    check-interval-minutes: 10       # 检查间隔10分钟

mall:
  order:
    strict-inventory-check: true #严格库存检查:false-不严格;true-严格;

wx:
  # 公众号配置
  mp:
    configs:
      - appId: xxxxxxxx
        secret: xxxxxxxxxxxxxxxxxxxxxxxxxx
        token: xxxxxxxxxx
        aesKey: xxxxxxxxxxxxxxxxxxxx
  # 小程序配置
  ma:
    configs:
      - appId: wxd5b9c838bcfddbda
        secret: 6c49b12a6554ae1f32e25cf127349b81
          #      - appId: wx781f86096dc8423a
          #        secret: 1241d1fa6a6a2a694f7330ed02095389
        # 微信支付商户号，请去微信支付平台申请
        mchId: 1643865976
        # 微信支付商户密钥，请去微信支付平台申请
        mchKey: abc949330924116565fea48453c9a223
        keyPath: /home/<USER>/doc/apiclient_cert.p12
        privateKeyPath: /home/<USER>/doc/apiclient_key.pem
        privateCertPath: /home/<USER>/doc/apiclient_cert.pem
  mall:
    # 支付、物流回调地址，即后台服务7500端口的外网访问域名，要保证外网能访问
    notify-host: https://mp.xiakex.cn/stage-api
    logistics-key: 1
    #  notify-host: http://localhost:7500