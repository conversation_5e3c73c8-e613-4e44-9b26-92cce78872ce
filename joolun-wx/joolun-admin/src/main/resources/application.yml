# 项目相关配置
ruoyi:
  # 实例演示开关
  demoEnabled: false
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 日志配置（使用logback-spring.xml，支持多环境配置）
logging:
  config: classpath:logback-spring.xml

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 120

# MyBatisPlus配置
mybatis-plus:
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 自定义TypeHandler
  type-handlers-package: com.joolun.framework.config.typehandler
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.joolun.**.domain,com.joolun.**.entity
  global-config:
    #是否控制台
    banner: false
    db-config:
      #主键类型
      id-type: auto

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    keyId: LTAI5t6EcYJz9wgeLL9riAey
    keySecret: ******************************
    bucketName: xiakexing-wx