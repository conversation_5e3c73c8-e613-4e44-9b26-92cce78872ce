/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.domain.entity.SysDictData;
import com.joolun.common.utils.StringUtils;
import com.joolun.system.service.ISysDictDataService;
import com.joolun.system.service.ISysDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统字典接口
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/sysdict")
@Api(value = "sysdict", tags = "系统字典接口API")
public class SysDictApi {

    private final ISysDictDataService sysDictDataService;

	private final ISysDictTypeService dictTypeService;


	/**
	 * 根据字典类型查询字典数据信息
	 */
	@ApiOperation(value = "查询字典详情")
	@GetMapping(value = "/type/{dictType}")
	public AjaxResult dictType(@PathVariable String dictType)
	{
		List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
		if (StringUtils.isNull(data))
		{
			data = new ArrayList<SysDictData>();
		}
		return AjaxResult.success(data);
	}
}
