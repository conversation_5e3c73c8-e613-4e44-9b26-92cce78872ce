package com.joolun.web.controller.mall;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.utils.validation.CheckUtils;
import com.joolun.mall.dto.GoodsSpecsAttrDTO;
import com.joolun.mall.dto.GoodsSpecsAttrTagsDTO;
import com.joolun.mall.dto.GoodsSpecsDTO;
import com.joolun.mall.entity.GoodsSkuSpecificationValue;
import com.joolun.mall.entity.GoodsSpecificationValue;
import com.joolun.mall.entity.GoodsSpecificationAttribute;
import com.joolun.mall.entity.vo.GoodsSpecificationAttributeVo;
import com.joolun.mall.entity.vo.GoodsSpuSelectedAttrVo;
import com.joolun.mall.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.Optional;

/**
 * 规格名
 *
 * <AUTHOR>
 * @date 2024-07-18 22:51:10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/mall/attr")
@Api(value = "goodsSpecsAttr", tags = "规格名管理")
public class GoodsSpecificationAttributeController extends BaseController {

  private final GoodsSpecificationValueService specificationValueService;
  private final GoodsSpecificationAttributeService service;
  private final IGoodsSkuSpecificationValueService goodsSkuSpecificationValueService;
  private final CheckUtils checkUtils;

  /** 新增商品规格名-根据商品新增 */
  @Log(title = "新增商品规格名", businessType = BusinessType.INSERT)
  @PostMapping(value = "/addAttrAndAttrValues")
  public AjaxResult addAttrAndAttrValues(@RequestBody GoodsSpecsAttrDTO dto) {
    return service.addAttrAndAttrValues(dto) ? AjaxResult.success() : AjaxResult.error();
  }

  /** 新增商品规格名 */
  @Log(title = "新增商品规格名", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody GoodsSpecsAttrDTO dto) {
    return service.add(dto) ? AjaxResult.success() : AjaxResult.error();
  }

  /** 删除商品规格名 */
  @Log(title = "删除商品规格名", businessType = BusinessType.DELETE)
  @DeleteMapping(value = "{id}")
  public AjaxResult removeAttr(GoodsSpecsAttrDTO dto) {
    if (ObjectUtil.isNull(dto.getId())) {
      return AjaxResult.error("id 请输入规格名ID");
    }
    return service.removeAttr(dto) ? AjaxResult.success() : AjaxResult.error();
  }

  @ApiOperation(value = "list查询")
  @GetMapping("/list")
  public AjaxResult getList(GoodsSpecsAttrDTO dto) {
    List<GoodsSpecificationAttribute> list = service.selectByGoodsId(dto);
    List<GoodsSpecsAttrDTO> resultList = list.stream().map(attr -> {
      GoodsSpecsAttrDTO dtoItem = new GoodsSpecsAttrDTO();
      dtoItem.setId(attr.getId());
      dtoItem.setAttrName(attr.getAttrName());
      dtoItem.setIsPackage(attr.getIsPackage());
      dtoItem.setCommonAttr(attr.getCommonAttr());
      // 查询规格值
      List<GoodsSpecificationValue> specsList = specificationValueService.selectList(GoodsSpecsDTO.builder()
          .attrId(attr.getId())
          .build());
      dtoItem.setGoodsSpecificationValueList(specsList);
      dtoItem.setSpecsValuesCount(specsList.size());
      return dtoItem;
    }).collect(Collectors.toList());
    return AjaxResult.success(resultList);
  }

  /** 获取商品详细信息 */
  @GetMapping(value = "{id}")
  public AjaxResult getInfo(@PathVariable("id") String id) {
    return AjaxResult.success(service.getAttrAndSpecsById(id));
  }

  /** 修改商品规格名 */
  @Log(title = "修改商品规格名", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody GoodsSpecsAttrDTO dto) {
    GoodsSpecificationAttribute byId = service.getById(dto.getId());
    byId.setAttrName(dto.getAttrName());
    byId.setCommonAttr(dto.getCommonAttr());
    byId.setIsPackage(dto.getIsPackage());
    // 查询出所有已存在的属性值
    List<GoodsSpecificationValue> goodsSpecs = specificationValueService.selectList(GoodsSpecsDTO.builder().attrId(dto.getId()).build());
    Set<String> existingSpecValues = goodsSpecs.stream().map(GoodsSpecificationValue::getName).collect(Collectors.toSet());

    // 与 dto 中传递的 goodsSpecsList 的值进行对比
    List<GoodsSpecificationValue> goodsSpecificationValueList = dto.getGoodsSpecificationValueList();
    Set<String> newSpecValues = goodsSpecificationValueList.stream().map(GoodsSpecificationValue::getName).collect(Collectors.toSet());

    // 找到需要删除的属性值
    List<GoodsSpecificationValue> toRemove = goodsSpecs.stream()
            .filter(spec -> !newSpecValues.contains(spec.getName()))
            .collect(Collectors.toList());

    // 找到需要新增的属性值
    List<GoodsSpecificationValue> toAdd = goodsSpecificationValueList.stream()
            .filter(spec -> !existingSpecValues.contains(spec.getName()))
            .collect(Collectors.toList());

    // 删除不需要的属性值
    toRemove.forEach(spec -> specificationValueService.removeById(spec.getId()));

    // 新增新的属性值
    toAdd.forEach(spec -> {
      spec.setAttrId(dto.getId());
      specificationValueService.save(spec);
    });

    // 更新商品规格名
    return service.updateById(byId) ? AjaxResult.success() : AjaxResult.error();
  }

  @Log(title = "新增-非套餐-规格名&规格值", businessType = BusinessType.INSERT)
  @PostMapping(value = "/addNonPackageSpu")
  public AjaxResult addNonPackageSpu(@RequestBody GoodsSpecsAttrTagsDTO dto) {
    return service.addNonPackageSpu(dto)
            ? AjaxResult.success()
            : AjaxResult.error();
  }

  /** 获取商品详细信息 */
//  @GetMapping(value = "/getByGoodsId/{goodsId}")
  public AjaxResult getByGoodsId(@PathVariable("goodsId") String goodsId) {
    if (StrUtil.isBlank(goodsId)) {
      return AjaxResult.error("goodsId 请输入线路ID");
    }
    return AjaxResult.success(
        service.selectByGoodsIdReturnAttrTagsDTO(
            GoodsSpecsAttrDTO.builder().goodsId(goodsId).build()));
  }

  @ApiOperation(value = "导出规格名列表")
  @Log(title = "规格名", businessType = BusinessType.EXPORT)
  @GetMapping("/export")
  public AjaxResult export(GoodsSpecsAttrDTO dto) {
    List<GoodsSpecificationAttribute> list = service.selectByGoodsId(dto);
    List<GoodsSpecsAttrDTO> exportList = list.stream().map(attr -> {
      GoodsSpecsAttrDTO dtoItem = new GoodsSpecsAttrDTO();
      dtoItem.setId(attr.getId());
      dtoItem.setAttrName(attr.getAttrName());
      dtoItem.setIsPackage(attr.getIsPackage());
      dtoItem.setCommonAttr(attr.getCommonAttr());
      // 查询规格值
      List<GoodsSpecificationValue> specsList = specificationValueService.selectList(GoodsSpecsDTO.builder()
          .attrId(attr.getId())
          .build());
      dtoItem.setGoodsSpecificationValueList(specsList);
      return dtoItem;
    }).collect(Collectors.toList());
    
    ExcelUtil<GoodsSpecsAttrDTO> util = new ExcelUtil<>(GoodsSpecsAttrDTO.class);
    return util.exportExcel(exportList, "规格名数据");
  }

  @ApiOperation(value = "list查询")
  @GetMapping("/allAttrList")
  public AjaxResult allAttrList() {
    List<GoodsSpecificationAttribute> list = service.selectAll();
    List<GoodsSpecsAttrDTO> resultList =
        list.stream()
            .map(
                attr -> {
                  GoodsSpecsAttrDTO dtoItem = new GoodsSpecsAttrDTO();
                  dtoItem.setId(attr.getId());
                  dtoItem.setAttrName(attr.getAttrName());
                  dtoItem.setIsPackage(attr.getIsPackage());
                  dtoItem.setCommonAttr(attr.getCommonAttr());
                  // 查询规格值
                  List<GoodsSpecificationValue> specsList =
                      specificationValueService.selectList(
                          GoodsSpecsDTO.builder().attrId(attr.getId()).build());
                  dtoItem.setGoodsSpecificationValueList(specsList);
                  return dtoItem;
                })
            .collect(Collectors.toList());
    return AjaxResult.success(resultList);
  }

  @ApiOperation(value = "查询商品信息下的规格")
  @GetMapping(value = "/getByGoodsId/{goodsId}")
  public AjaxResult selectByGoodsId(@PathVariable("goodsId") String goodsId) {
    checkUtils.throwIfBlank(goodsId, "goodsId", "goodsId为空");

    List<GoodsSkuSpecificationValue> spuAttrValues = goodsSkuSpecificationValueService.listByGoodsId(goodsId);
    if (ObjectUtil.isEmpty(spuAttrValues)) {
      return AjaxResult.success();
    }

    List<GoodsSpecificationValue> attrValues =
        specificationValueService.listByIds(
            spuAttrValues.stream()
                .map(GoodsSkuSpecificationValue::getSpecValueId)
                .collect(Collectors.toSet()));
    Map<String, List<GoodsSpecificationValue>> attrValuesMap =
        attrValues.stream()
            .collect(Collectors.groupingBy(GoodsSpecificationValue::getAttrId));

    List<GoodsSpecificationAttribute> goodsAttr =
        service.list(
            new LambdaQueryWrapper<GoodsSpecificationAttribute>()
                .in(GoodsSpecificationAttribute::getId,
                    attrValues.stream()
                        .map(GoodsSpecificationValue::getAttrId)
                        .collect(Collectors.toSet()))
                .eq(GoodsSpecificationAttribute::getDeleteFlag, Boolean.FALSE)
                .orderByAsc(GoodsSpecificationAttribute::getSortOrder));
    List<GoodsSpecificationAttributeVo> attributeVos =
        BeanUtil.copyToList(goodsAttr, GoodsSpecificationAttributeVo.class);

    GoodsSpuSelectedAttrVo result = new GoodsSpuSelectedAttrVo();
    for (GoodsSpecificationAttributeVo attr : attributeVos) {
      List<GoodsSpecificationValue> tempAttrValues = attrValuesMap.get(attr.getId());
      if (ObjectUtil.isNotEmpty(tempAttrValues)) {
        // 按 sort_order 对规格值排序
        tempAttrValues.sort(Comparator.comparing(v -> Optional.ofNullable(v.getSortOrder()).orElse(999)));
        attr.setAttrValues(tempAttrValues);
      }
    }
    result.setAttrList(attributeVos);

    return AjaxResult.success(result);
  }

  @ApiOperation(value = "批量更新规格分类/值排序")
  @Log(title = "规格排序", businessType = BusinessType.UPDATE)
  @PostMapping("/sort")
  public AjaxResult updateSort(@RequestBody List<com.joolun.mall.dto.SpecSortDTO> list) {
    if (ObjectUtil.isEmpty(list)) {
      return AjaxResult.error("请求列表为空");
    }
    // 按类型分组
    List<com.joolun.mall.dto.SpecSortDTO> categoryList =
        list.stream().filter(i -> "CATEGORY".equalsIgnoreCase(i.getType())).collect(Collectors.toList());
    List<com.joolun.mall.dto.SpecSortDTO> valueList =
        list.stream().filter(i -> "VALUE".equalsIgnoreCase(i.getType())).collect(Collectors.toList());

    // 更新分类
    if (ObjectUtil.isNotEmpty(categoryList)) {
      List<com.joolun.mall.entity.GoodsSpecificationAttribute> entities = categoryList.stream()
          .map(i -> {
            com.joolun.mall.entity.GoodsSpecificationAttribute e = new com.joolun.mall.entity.GoodsSpecificationAttribute();
            e.setId(i.getId());
            e.setSortOrder(i.getSortOrder());
            return e;
          }).collect(Collectors.toList());
      service.updateBatchById(entities);
    }
    // 更新规格值
    if (ObjectUtil.isNotEmpty(valueList)) {
      List<com.joolun.mall.entity.GoodsSpecificationValue> entities = valueList.stream()
          .map(i -> {
            com.joolun.mall.entity.GoodsSpecificationValue e = new com.joolun.mall.entity.GoodsSpecificationValue();
            e.setId(i.getId());
            e.setSortOrder(i.getSortOrder());
            return e;
          }).collect(Collectors.toList());
      specificationValueService.updateBatchById(entities);
    }
    return AjaxResult.success();
  }
}
