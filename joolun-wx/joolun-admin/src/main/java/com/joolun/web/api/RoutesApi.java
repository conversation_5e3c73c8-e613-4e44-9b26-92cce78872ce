package com.joolun.web.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.trip.domain.TyRoutesInfo;
import com.joolun.trip.service.ITyRoutesInfoService;
import com.joolun.weixin.constant.MyReturnCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/weixin/api/ma/routes"})
@Api(
    value = "routes",
    tags = {"线路接口API"})
public class RoutesApi {

  @Autowired private final ITyRoutesInfoService tyRoutesInfoService;

  public RoutesApi(ITyRoutesInfoService tyRoutesInfoService) {
    this.tyRoutesInfoService = tyRoutesInfoService;
  }

  @ApiOperation("分页查询")
  @GetMapping({"/page"})
  public AjaxResult getGoodsSpuPage(Page page, TyRoutesInfo tyRoutesInfo) {
    return AjaxResult.success(this.tyRoutesInfoService.page1((IPage) page, tyRoutesInfo));
  }

  @ApiOperation("精选分页查询")
  @GetMapping({"/pageOrderByLv"})
  public AjaxResult getPageOrderByLv() {
    return AjaxResult.success(this.tyRoutesInfoService.getOrderByLv1());
  }

  @ApiOperation("热门目的地")
  @GetMapping({"/selectHotDest"})
  public AjaxResult selectHotDest() {
    List<TyRoutesInfo> tyRoutesInfos = this.tyRoutesInfoService.getHotDest1();
    return AjaxResult.success(tyRoutesInfos);
  }

  @GetMapping({"/getHotDest"})
  public AjaxResult getHotDest() {
    List<TyRoutesInfo> hotDest = this.tyRoutesInfoService.getHotDest1();
    return AjaxResult.success(hotDest);
  }

  @GetMapping({"/getOrderByLv"})
  public AjaxResult getOrderByLv() {
    List<TyRoutesInfo> orderByLv1 = this.tyRoutesInfoService.getOrderByLv1();
    return AjaxResult.success(orderByLv1);
  }

  @ApiOperation("通过id查询线路详情")
  @GetMapping({"/{id}"})
  public AjaxResult getById(HttpServletRequest request, @PathVariable("id") String sid) {
    long id = Long.parseLong(sid);
    TyRoutesInfo routesInfo = this.tyRoutesInfoService.getById1(Long.valueOf(id));
    if (routesInfo == null)
      return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), MyReturnCode.ERR_80004.getMsg());
    return AjaxResult.success(routesInfo);
  }
}
