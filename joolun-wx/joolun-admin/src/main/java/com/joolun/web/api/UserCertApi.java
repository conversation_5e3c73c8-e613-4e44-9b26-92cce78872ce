/**
 * Copyright (C) 2018-2019 All rights reserved, Designed By www.joolun.com 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.entity.UserCertInfo;
import com.joolun.mall.service.IUserCertInfoService;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/** 身份信息接口API */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/userCert")
@Api(value = "cert", tags = "身份信息接口API")
public class UserCertApi {

  private final IUserCertInfoService userCertInfoService;

  /**
   * 分页查询
   *
   * @param page 分页对象
   * @param userCertInfo 身份信息
   * @return
   */
  @ApiOperation(value = "根据用户openid查询用户维护的身份信息")
  @GetMapping("/page")
  public AjaxResult getGoodsSpuPage(Page page, UserCertInfo userCertInfo) {
    String wxUserId = ThirdSessionHolder.getWxUserId();
    userCertInfo.setCreateBy(wxUserId);
    return AjaxResult.success(userCertInfoService.selectByPageForApp(page, userCertInfo));
  }

  /**
   * 通过id查询身份信息详情
   *
   * @param id
   * @return R
   */
  @ApiOperation(value = "通过id查询身份信息详情")
  @GetMapping("/{id}")
  public AjaxResult getById(@PathVariable("id") String id) {
    UserCertInfo UserCertInfo = userCertInfoService.getById(id);
    if (UserCertInfo == null) {
      return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), MyReturnCode.ERR_80004.getMsg());
    }
    return AjaxResult.success(UserCertInfo);
  }

  /**
   * 新增或修改身份信息
   *
   * @param UserCertInfo 用户身份信息
   * @return R
   */
  @ApiOperation(value = "新增身份信息")
  @PostMapping
  public AjaxResult saveOrUpdate(@RequestBody UserCertInfo UserCertInfo) {

    int i = userCertInfoService.saveOrUpdateUserCertInfo(UserCertInfo);
    if (i <= 0) {
      return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
    }
    return AjaxResult.success(true);
  }

  /**
   * 通过id删除身份信息
   *
   * @param id
   * @return R
   */
  @ApiOperation(value = "通过id删除身份信息")
  @DeleteMapping("/{id}")
  public AjaxResult removeById(@PathVariable String id) {
    UserCertInfo certInfo = userCertInfoService.getById(id);
    if (certInfo == null) {
      return AjaxResult.error(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
    }
    return AjaxResult.success(userCertInfoService.deleteUserCertInfoById(id));
  }
}
