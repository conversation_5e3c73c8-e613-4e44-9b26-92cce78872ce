/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.trip.domain.TyUserCertInfo;
import com.joolun.trip.service.ITyUserCertInfoService;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 身份信息liao
 */
@Deprecated
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/cert")
@Api(value = "cert", tags = "身份信息接口API")
public class WxUserCertApi {

    private final ITyUserCertInfoService tyUserCertInfoService;

	/**
	* 分页查询
	* @param page 分页对象
	* @param tyUserCertInfo 身份信息
	* @return
	*/
	@ApiOperation(value = "根据用户openid查询用户维护的身份信息")
    @GetMapping("/page")
    public AjaxResult getGoodsSpuPage(Page page, TyUserCertInfo tyUserCertInfo) {
		String wxUserId = ThirdSessionHolder.getWxUserId();
		tyUserCertInfo.setCreateBy(wxUserId);
		return AjaxResult.success(tyUserCertInfoService.page1(page, tyUserCertInfo));
    }

    /**
    * 通过id查询身份信息详情
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询身份信息详情")
    @GetMapping("/{id}")
    public AjaxResult getById(HttpServletRequest request, @PathVariable("id") String id){
		TyUserCertInfo tyUserCertInfo = tyUserCertInfoService.getById1(id);
		if(tyUserCertInfo == null){
			return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), MyReturnCode.ERR_80004.getMsg());
		}
        return AjaxResult.success(tyUserCertInfo);
    }

	/**
	 * 新增身份信息
	 * @param tyUserCertInfo 用户身份信息
	 * @return R
	 */
	@ApiOperation(value = "新增身份信息")
	@PostMapping
	public AjaxResult save(@RequestBody TyUserCertInfo tyUserCertInfo){
		tyUserCertInfo.setCreateBy(ThirdSessionHolder.getWxUserId());
		tyUserCertInfo.setCreateTime(DateUtils.getNowDate());
		tyUserCertInfo.setId(IdUtils.simpleUUID());
		int i = tyUserCertInfoService.insertTyUserCertInfo(tyUserCertInfo);
		if(i<=0){
			return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
		}
		return AjaxResult.success(true);
	}

	/**
	 * 通过id删除身份信息
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "通过id删除身份信息")
	@DeleteMapping("/{id}")
	public AjaxResult removeById(@PathVariable String id){
		TyUserCertInfo certInfo = tyUserCertInfoService.getById(id);
		if(certInfo == null){
			return AjaxResult.error(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		return AjaxResult.success(tyUserCertInfoService.removeById(id));
	}


}
