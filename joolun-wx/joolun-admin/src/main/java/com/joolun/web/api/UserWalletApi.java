/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 */
package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.entity.SysUserWallet;
import com.joolun.mall.service.ISysUserWalletService;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 微信用户
 *
 * <AUTHOR>
 * @date 2019-08-25 15:39:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/userWallet")
@Api(value = "userWallet", tags = "小程序用户钱包API")
public class UserWalletApi {

	private final ISysUserWalletService userWalletService;

	/**
	 * 分页查询
	 * @param page 分页对象
	 * @param sysUserWallet 钱包信息
	 * @return
	 */
	@ApiOperation(value = "分页查询")
	@GetMapping("/page")
	public AjaxResult page(Page page, SysUserWallet sysUserWallet) {
		sysUserWallet.setUserId(ThirdSessionHolder.getWxUserId());
		return AjaxResult.success(userWalletService.page2(page,sysUserWallet));
	}


	/**
	 * 钱包信息
	 * @return
	 */
	@ApiOperation(value = "钱包信息")
	@GetMapping("/wallteInfo")
	public AjaxResult selectWalletByUserId() {
		return AjaxResult.success(userWalletService.selectWalletByUserId(ThirdSessionHolder.getWxUserId()));
	}



}
