package com.joolun.web.api;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.common.utils.validation.CheckUtils;
import com.joolun.framework.utils.UserContextUtils;
import com.joolun.mall.entity.TyUserBrowseInfo;
import com.joolun.mall.entity.TyUserCollect;
import com.joolun.mall.entity.query.TyUserCollectQuery;
import com.joolun.mall.service.ITyUserBrowseInfoService;
import com.joolun.mall.service.ITyUserCollectService;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/** 线路api */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/userCollect")
@Api(value = "userCollect", tags = "用户收藏接口API")
public class TyUserCollectApi {

  private final ITyUserCollectService tyUserCollectService;

  private final ITyUserBrowseInfoService tyUserBrowseInfoService;
  private final CheckUtils checkUtils;

  /**
   * 分页查询
   *
   * @param page 分页对象
   * @param tyUserCollect 收藏信息
   * @return
   */
  @ApiOperation(value = "根据用户查询用户的收藏信息")
  @GetMapping("/page")
  public AjaxResult getGoodsSpuPage(Page page, TyUserCollect tyUserCollect) {
    String currentUserId = UserContextUtils.getCurrentUserId();

    // 使用条件构造器，只在值不为空时才拼接查询条件
    return AjaxResult.success(
        tyUserCollectService.page(
            page,
            Wrappers.<TyUserCollect>lambdaQuery()
                .eq(
                    StrUtil.isNotBlank(tyUserCollect.getGoodId()),
                    TyUserCollect::getGoodId,
                    tyUserCollect.getGoodId())
                .eq(
                    StrUtil.isNotBlank(tyUserCollect.getUserId()),
                    TyUserCollect::getUserId,
                    tyUserCollect.getUserId())
                .eq(StrUtil.isNotBlank(currentUserId), TyUserCollect::getCreateBy, currentUserId)
                .eq(TyUserCollect::getDeleteFlag, false)));
  }

  /**
   * 检测用户是否收藏对应商品
   *
   * @param goodId,userId
   * @return
   */
  @ApiOperation(value = "检测用户是否收藏对应商品")
  @GetMapping("/check")
  public AjaxResult checkCollect(
      @RequestParam("goodId") String goodId) {
    TyUserCollect userCollect = new TyUserCollect();
    userCollect.setGoodId(goodId);
    userCollect.setUserId(UserContextUtils.getCurrentUserId());
    List<TyUserCollect> tyUserCollects = tyUserCollectService.selectTyUserCollectList(userCollect);
    // 查询收藏记录代表访问了该商品(线路)，记录浏览信息
    TyUserBrowseInfo tyUserBrowseInfo = new TyUserBrowseInfo();
    tyUserBrowseInfo.setId(IdUtils.simpleUUID());
    tyUserBrowseInfo.setGoodId(goodId);
    tyUserBrowseInfo.setUserId(UserContextUtils.getCurrentUserId());
    tyUserBrowseInfo.setCreateTime(DateUtils.getNowDate());
    tyUserBrowseInfoService.insertTyUserBrowseInfo(tyUserBrowseInfo);
    if (tyUserCollects.size() <= 0) {
      return AjaxResult.success("当前商品未收藏", false);
    } else {
      return AjaxResult.success("当前商品已收藏", true);
    }
  }

  /**
   * 通过id查询收藏信息详情
   *
   * @param id
   * @return R
   */
  @ApiOperation(value = "通过id查询收藏信息详情")
  @GetMapping("/{id}")
  public AjaxResult getById(@PathVariable("id") String id) {
    TyUserCollect userCollect = tyUserCollectService.selectTyUserCollectById(id);
    if (userCollect == null) {
      return AjaxResult.error(MyReturnCode.ERR_90001.getCode(), MyReturnCode.ERR_90001.getMsg());
    }
    return AjaxResult.success(userCollect);
  }

  /**
   * 新增收藏
   *
   * @param tyUserCollect 收藏信息
   * @return R
   */
  @ApiOperation(value = "新增收藏")
  @PostMapping
  public AjaxResult save(@RequestBody TyUserCollect tyUserCollect) {
    String userId = ThirdSessionHolder.getWxUserId();
    tyUserCollect.setUserId(userId);
    tyUserCollect.setDeleteFlag(Boolean.FALSE);
    if (StrUtil.isBlank(tyUserCollect.getGoodId())) {
      return AjaxResult.error("线路id不能为空");
    }
    
    // 检查是否已经收藏过该商品
    List<TyUserCollect> existingCollects = tyUserCollectService.list(
        Wrappers.<TyUserCollect>lambdaQuery()
            .eq(TyUserCollect::getGoodId, tyUserCollect.getGoodId())
            .eq(TyUserCollect::getUserId, userId)
            .eq(TyUserCollect::getDeleteFlag, false)
    );
    
    if (!existingCollects.isEmpty()) {
      log.info("商品已收藏 true");
      return AjaxResult.success(true);
    }
    
    boolean insert = tyUserCollectService.save(tyUserCollect);
    if (!insert) {
      return AjaxResult.error("收藏信息添加失败");
    }
    return AjaxResult.success(true);
  }

  /**
   * 批量删除用户收藏记录
   *
   * @param query
   * @return R
   */
  @ApiOperation(value = "批量删除用户收藏记录")
  @PostMapping("/removeByGoodIds")
  public AjaxResult removeByGoodIds(@RequestBody TyUserCollectQuery query) {
    checkUtils.throwIfEmpty(query.getGoodIds(), "goodIds 不可以为空");
    boolean remove =
        tyUserCollectService.remove(
            Wrappers.<TyUserCollect>lambdaQuery()
                .in(TyUserCollect::getGoodId, query.getGoodIds())
                .eq(TyUserCollect::getUserId, ThirdSessionHolder.getWxUserId()));
    if (!remove) {
      return AjaxResult.success("收藏信息删除失败，未查询到收藏信息", false);
    }
    return AjaxResult.success(true);
  }
}
