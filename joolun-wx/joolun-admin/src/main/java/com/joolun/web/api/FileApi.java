package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.system.service.IFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR> @Description @Date 2023/11/8-23:48 @Version 1.0.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/file")
@Api(value = "file", tags = "文件公共接口")
public class FileApi {

  private final IFileService fileService;

  private static final String AVATAR_MODULE = "avatar";

  /**
   * 文件上传
   *
   * @param file
   * @param module
   * @return
   */
  @ApiOperation("文件上传")
  @PostMapping("/upload")
  public AjaxResult upload(
      @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file,
      @ApiParam(value = "模块", required = true) @RequestParam("module") String module) {
    try {
      InputStream inputStream = file.getInputStream();
      String originalFilename = file.getOriginalFilename();

      // 获取travelId，如果是后台管理端用户则获取，如果是小程序端用户则为null
      String travelId = null;
      try {
        // 尝试获取当前登录用户的travelId
        travelId = SecurityUtils.getLoginUser().getUser().getTravelId();
      } catch (Exception e) {
        // 如果获取失败（比如小程序端用户），travelId保持为null
        log.info("无法获取travelId，可能是小程序端用户上传: {}", e.getMessage());
      }

      String uploadUrl = fileService.upload(inputStream, module, originalFilename, travelId);
      // 返回R对象
      return AjaxResult.success("文件上传成功", uploadUrl);
    } catch (IOException e) {
      log.error("文件上传错误：{}", e.getMessage(), e);
      throw new RuntimeException();
    }
  }

  /**
   * 用于微信小程序端用户上传头像
   *
   * @param file
   * @return
   */
  @ApiOperation("用于微信小程序端用户上传头像")
  @PostMapping("/uploadAvatarForWeChatApp")
  public AjaxResult uploadAvatarForWeChatApp(
      @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file) {
    try {
      InputStream inputStream = file.getInputStream();
      String originalFilename = file.getOriginalFilename();
      String uploadUrl = fileService.upload(inputStream, AVATAR_MODULE, originalFilename, null);
      return AjaxResult.success("头像上传成功", uploadUrl);
    } catch (IOException e) {
      log.error("文件上传错误：{}", e.getMessage(), e);
      throw new RuntimeException();
    }
  }

  @ApiOperation(value = "删除oss文件")
  @DeleteMapping("/remove")
  public AjaxResult remove(
      @ApiParam(value = "要删除的文件路径", required = true) @RequestParam("url") String url) {
    fileService.remove(url);
    return AjaxResult.success("文件删除成功");
  }
}
