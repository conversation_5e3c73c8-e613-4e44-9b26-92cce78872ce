package com.joolun.web.controller.mall;

import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.service.TravelAgencyCommonConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 旅行社信息-通用配置信息 Controller
 *
 * <AUTHOR>
 * @date 2024-10-12
 */
@RestController
@RequestMapping("/mall/travel/common/config")
public class TravelAgencyCommonConfigController extends BaseController {
  @Autowired private TravelAgencyCommonConfigService service;

  /** 获取旅行社通用配置信息 */
  @GetMapping(value = "/getByTravelId/{id}")
  public AjaxResult getByTravelId(@PathVariable("id") String id) {
    return AjaxResult.success(service.getByTravelId(id));
  }
}
