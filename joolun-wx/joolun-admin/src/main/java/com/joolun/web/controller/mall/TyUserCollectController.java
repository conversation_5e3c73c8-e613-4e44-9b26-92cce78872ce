package com.joolun.web.controller.mall;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.entity.TyUserCollect;
import com.joolun.mall.service.ITyUserCollectService;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.core.page.TableDataInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 用户收藏列表Controller
 * 
 * <AUTHOR>
 * @date 2023-09-26
 */
@RestController
@RequestMapping("/mall/collect")
public class TyUserCollectController extends BaseController
{
    @Autowired
    private ITyUserCollectService tyUserCollectService;

    /**
     * 查询用户收藏列表列表
     */
    @PreAuthorize("@ss.hasPermi('mall:collect:list')")
    @GetMapping("/list")
    public TableDataInfo list(TyUserCollect tyUserCollect)
    {
        startPage();
        List<TyUserCollect> list = tyUserCollectService.selectTyUserCollectList(tyUserCollect);
        return getDataTable(list);
    }



    /**
     * 分页查询
     * @param page 分页对象
     * @param tyUserCollect
     * @return
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermi('mall:useraddress:index')")
    public AjaxResult page(Page page, TyUserCollect tyUserCollect) {
        return AjaxResult.success(tyUserCollectService.page(page, Wrappers.query(tyUserCollect)));
    }





    /**
     * 导出用户收藏列表列表
     */
    @PreAuthorize("@ss.hasPermi('mall:collect:export')")
    @Log(title = "用户收藏列表", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TyUserCollect tyUserCollect)
    {
        List<TyUserCollect> list = tyUserCollectService.selectTyUserCollectList(tyUserCollect);
        ExcelUtil<TyUserCollect> util = new ExcelUtil<TyUserCollect>(TyUserCollect.class);
        return util.exportExcel(list, "collect");
    }

    /**
     * 获取用户收藏列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:collect:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tyUserCollectService.selectTyUserCollectById(id));
    }

    /**
     * 新增用户收藏列表
     */
    @PreAuthorize("@ss.hasPermi('mall:collect:add')")
    @Log(title = "用户收藏列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TyUserCollect tyUserCollect)
    {
        return toAjax(tyUserCollectService.insertTyUserCollect(tyUserCollect));
    }

    /**
     * 修改用户收藏列表
     */
    @PreAuthorize("@ss.hasPermi('mall:collect:edit')")
    @Log(title = "用户收藏列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TyUserCollect tyUserCollect)
    {
        return toAjax(tyUserCollectService.updateTyUserCollect(tyUserCollect));
    }

    /**
     * 删除用户收藏列表
     */
    @PreAuthorize("@ss.hasPermi('mall:collect:remove')")
    @Log(title = "用户收藏列表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tyUserCollectService.deleteTyUserCollectByIds(ids));
    }
}
