package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.entity.ArticleInfo;
import com.joolun.mall.entity.GoodsEvaluate;
import com.joolun.mall.entity.GoodsSpu;
import com.joolun.mall.service.GoodsSpuService;
import com.joolun.mall.service.IArticleInfoService;
import com.joolun.mall.service.ILabelInfoService;
import com.joolun.weixin.constant.MyReturnCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2023/11/06-09:09
 * @Version 1.0.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/articleInfo")
@Api(value = "articleInfo", tags = "文章接口")
public class ArticleInfoApi {

    private final IArticleInfoService articleInfoService;

    private final GoodsSpuService goodsSpuService;

    private final ILabelInfoService labelInfoService;


    /**
     * 分页查询
     *
     * @param page        分页对象
     * @param articleInfo 文章信息
     * @return
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public AjaxResult getGoodsSpuPage(Page page, ArticleInfo articleInfo) {
        return AjaxResult.success(articleInfoService.page1(page, articleInfo));
    }


    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param userId 用户id
     * @return
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/collectPage")
    public AjaxResult selectCollectArticleList(Page page, String userId) {
        return AjaxResult.success(articleInfoService.selectCollectArticleList(page, userId));
    }

    /**
     * 通过id查询文章
     *
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过id查询文章")
    @GetMapping("/{id}")
    public AjaxResult getById(@PathVariable("id") String id) {
        ArticleInfo articleInfo = articleInfoService.selectArticleInfoById(id);
        GoodsSpu goodsSpu = goodsSpuService.getById1(articleInfo.getGoodId());
        articleInfo.setGoodInfo(goodsSpu);
        if (articleInfo == null) {
            return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), MyReturnCode.ERR_80004.getMsg());
        }
        return AjaxResult.success(articleInfo);
    }

    /**
     * 新增文章信息
     *
     * @param articleInfo 评论信息
     * @return R
     */
    @ApiOperation(value = "新增文章信息")
    @PostMapping
    public AjaxResult save(@RequestBody ArticleInfo articleInfo) {

        int i = articleInfoService.insertArticleInfo(articleInfo);
        if (i <= 0) {
            return AjaxResult.error(MyReturnCode.ERR_10000.getCode(), MyReturnCode.ERR_10000.getMsg());
        }
        return AjaxResult.success(true);
    }

    /**
     * 通过id删除文章信息
     *
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过id删除文章信息")
    @DeleteMapping("/{id}")
    public AjaxResult removeById(@PathVariable String id) {
        ArticleInfo articleInfo = articleInfoService.selectArticleInfoById(id);
        if (articleInfo == null) {
            return AjaxResult.error(MyReturnCode.ERR_10002.getCode(), MyReturnCode.ERR_10002.getMsg());
        }
        return AjaxResult.success(articleInfoService.deleteArticleInfoById(id));
    }


    /**
     * 更新文章信息
     *
     * @param articleInfo 评论信息
     * @return R
     */
    @ApiOperation(value = "更新文章信息")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody ArticleInfo articleInfo) {

        int i = articleInfoService.updateArticleInfo(articleInfo);
        if (i <= 0) {
            return AjaxResult.error(MyReturnCode.ERR_10000.getCode(), MyReturnCode.ERR_10000.getMsg());
        }
        return AjaxResult.success(true);
    }


}
