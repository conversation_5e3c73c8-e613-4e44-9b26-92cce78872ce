package com.joolun.web.controller.mall;

import java.util.List;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.constant.UserConstants;
import com.joolun.common.core.domain.entity.SysUser;
import com.joolun.common.core.redis.RedisCache;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.common.utils.StringUtils;
import com.joolun.common.utils.sms.SMSUtils;
import com.joolun.mall.constant.TripConstants;
import com.joolun.mall.dto.TravelAgencyResetPasswordDTO;
import com.joolun.mall.entity.SysUserWallet;
import com.joolun.mall.entity.UserAddress;
import com.joolun.system.service.ISysUserService;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.entity.TravelAgency;
import com.joolun.mall.service.ITravelAgencyService;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.core.page.TableDataInfo;

/**
 * 旅行社信息Controller
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
@RestController
@RequestMapping("/mall/travel")
public class TravelAgencyController extends BaseController
{
    @Autowired
    private ITravelAgencyService travelAgencyService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 查询旅行社信息列表
     */
    @PreAuthorize("@ss.hasPermi('mall:travel:list')")
    @GetMapping("/list")
    public TableDataInfo list(TravelAgency travelAgency)
    {
        startPage();
        List<TravelAgency> list = travelAgencyService.selectTravelAgencyList(travelAgency);
        return getDataTable(list);
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param travelAgency
     * @return
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermi('mall:travel:list')")
    public AjaxResult page(TravelAgency travelAgency) {
        startPage();
        List<TravelAgency> list = travelAgencyService.selectTravelAgencyList(travelAgency);
        return AjaxResult.success(list);
    }

    /**
     * 导出旅行社信息列表
     */
    @PreAuthorize("@ss.hasPermi('mall:travel:export')")
    @Log(title = "旅行社信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TravelAgency travelAgency)
    {
        List<TravelAgency> list = travelAgencyService.selectTravelAgencyList(travelAgency);
        ExcelUtil<TravelAgency> util = new ExcelUtil<TravelAgency>(TravelAgency.class);
        return util.exportExcel(list, "travel");
    }

    /**
     * 获取旅行社信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:travel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(travelAgencyService.selectTravelAgencyById(id));
    }

    /**
     * 新增旅行社信息
     */
    @PreAuthorize("@ss.hasPermi('mall:travel:add')")
    @Log(title = "旅行社信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TravelAgency travelAgency)
    {
        travelAgency.setStatus(TripConstants.STATUS_0);
        return toAjax(travelAgencyService.insertTravelAgency(travelAgency));
    }

    /**
     * 修改旅行社信息
     */
    @PreAuthorize("@ss.hasPermi('mall:travel:edit')")
    @Log(title = "旅行社信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TravelAgency travelAgency)
    {
        return toAjax(travelAgencyService.updateTravelAgency(travelAgency));
    }

    /**
     * 审批旅行社信息
     */
    @PreAuthorize("@ss.hasPermi('mall:travel:edit')")
    @Log(title = "旅行社信息", businessType = BusinessType.UPDATE)
    @PostMapping("/approveInfo")
    public AjaxResult approveInfo(@RequestBody TravelAgency travelAgency)
    {
        TravelAgency agency = travelAgencyService.selectTravelAgencyById(travelAgency.getId());
        agency.setUpdateBy(SecurityUtils.getUsername());
        agency.setUpdateTime(DateUtils.getNowDate());
        agency.setStatus(travelAgency.getStatus());
        if(TripConstants.STATUS_1.equals(agency.getStatus())){
            SysUser user = new SysUser();
            user.setUserName(agency.getUsername());
            user.setPassword(agency.getPassword());
            user.setPhonenumber(agency.getPhone());
            user.setNickName(agency.getContacts());
            user.setTravelId(agency.getId());
            if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName())))
            {
                return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
            }
            else if (StringUtils.isNotEmpty(agency.getPhone())
                    && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user)))
            {
                return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
            }
            user.setCreateBy(SecurityUtils.getUsername());
            user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
            Long[] role={new Long(100)};

            user.setRoleIds(role);

            userService.insertUser(user);

        }

        return toAjax(travelAgencyService.updateTravelAgency(agency));
    }


    /**
     * 删除旅行社信息
     */
    @PreAuthorize("@ss.hasPermi('mall:travel:remove')")
    @Log(title = "旅行社信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(travelAgencyService.deleteTravelAgencyByIds(ids));
    }


    /**
     * 新增旅行社信息
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody TravelAgency travelAgency)
    {
        travelAgency.setStatus(TripConstants.STATUS_0);
        SysUser sysUser = userService.selectUserByUserName(travelAgency.getUsername());
        if(null!=sysUser){
            return AjaxResult.error("登录账号已存在，请修改后重试！",null);
        }else{
         if(sysUser.getPhonenumber().equals(travelAgency.getPhone())){
             return AjaxResult.error("手机号已存在，请修改后重试！",null);
         }
        }
        TravelAgency tAgency = new TravelAgency();
        tAgency.setName(travelAgency.getName());
        List<TravelAgency> travelAgencies = travelAgencyService.selectTravelAgencyList(tAgency);
        if(travelAgencies.size()>0){
            return AjaxResult.error("旅行社出现同名，请修改后重试！",null);
        }
        return toAjax(travelAgencyService.insertTravelAgency(travelAgency));
    }


    /**
     * 旅行社信息审批
     */
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody TravelAgency travelAgency)
    {
        travelAgency.setStatus(TripConstants.STATUS_0);
        return toAjax(travelAgencyService.insertTravelAgency(travelAgency));
    }

    @PostMapping("/sendSmsVerificationCode")
    public AjaxResult sendSmsVerificationCode(@RequestBody TravelAgency travelAgency) {
        if (StrUtil.isBlank(travelAgency.getPhone())) {
            return AjaxResult.error("请填写手机号");
        }

        String redisKey = "verificationCode:" + travelAgency.getPhone();
        String cooldownKey = "verificationCodeCooldown:" + travelAgency.getPhone();

        // 检查是否在冷却时间内
        Long cooldownRemainingTime = redisCache.getExpire(cooldownKey);
        if (cooldownRemainingTime != null && cooldownRemainingTime > 0) {
            return AjaxResult.error("验证码已发送，请" + cooldownRemainingTime + "秒后再试");
        }

        // 检查是否已经发送验证码
        String verificationCode = (String) redisCache.getCacheObject(redisKey);
        if (ObjectUtil.isNotNull(verificationCode)) {
            return AjaxResult.error("已发送验证码，请稍后再试");
        }

        // 生成四位数验证码
        String randomNumbers = RandomUtil.randomNumbers(4);
        redisCache.setCacheObject(redisKey, randomNumbers, 5, TimeUnit.MINUTES);

        // 设置冷却标识，60秒过期
        redisCache.setCacheObject(cooldownKey, true, 60, TimeUnit.SECONDS);

        SMSUtils.sendMessage(
                "1400841079",
                "友同小程序",
                "2279519",
                new String[] {randomNumbers},
                new String[] {"15365895999"});

        return AjaxResult.success("已发送验证码，请注意查收短信");
    }

    @PostMapping("/resetPassword")
    public AjaxResult resetPassword(@RequestBody TravelAgencyResetPasswordDTO travelAgency) {
        SysUser sysUser = userService.selectUserByUserName(travelAgency.getUsername());
        if (null == sysUser) {
            return AjaxResult.error("账号不存在，请确认后重试！");
        }

        if (StrUtil.isBlank(travelAgency.getPhone())) {
            return AjaxResult.error("请填写手机号");
        }

        String redisKey = "verificationCode:" + travelAgency.getPhone();
        String verificationCode = (String) redisCache.getCacheObject(redisKey);

        if (ObjectUtil.isNull(verificationCode)) {
            return AjaxResult.error("验证码不正确或已失效，请重新获取验证码");
        }

        if (!verificationCode.equals(travelAgency.getVerificationCode())) {
            return AjaxResult.error("验证码不正确，请重新输入验证码");
        }

        sysUser.setPassword(SecurityUtils.encryptPassword(travelAgency.getPassword()));
        int i = userService.resetPwd(sysUser);
        if (i > 0) {
            redisCache.deleteObject(redisKey);
        }
        return toAjax(i);
    }



}
