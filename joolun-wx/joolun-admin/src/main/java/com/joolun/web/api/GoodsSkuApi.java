package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.dto.GoodsSkuAppDTO;
import com.joolun.mall.dto.SkuCalendarPriceAppDTO;
import com.joolun.mall.service.IGoodsSkuService;
import com.joolun.weixin.constant.MyReturnCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import java.util.List;

/**
 * 商品SKU api
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/goodssku")
@Api(value = "goodssku", tags = "商品SKU接口API")
public class GoodsSkuApi {

  private final IGoodsSkuService goodsSkuService;

  /**
   * 根据商品ID获取所有SKU列表
   *
   * @param goodsId 商品ID
   * @return 商品SKU列表
   */
  @ApiOperation(value = "根据商品ID获取所有SKU列表")
  @GetMapping("/goods/{goodsId}/skus")
  public AjaxResult getGoodsSkuList(@PathVariable("goodsId") String goodsId) {
    log.info("获取商品SKU列表，商品ID: {}", goodsId);
    
    try {
      List<GoodsSkuAppDTO> skuList = goodsSkuService.getSkuListByGoodsId(goodsId);
      return AjaxResult.success(skuList);
    } catch (Exception e) {
      log.error("获取商品SKU列表失败，商品ID: {}, 错误信息: {}", goodsId, e.getMessage(), e);
      return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), "获取商品SKU列表失败");
    }
  }

  /**
   * 获取SKU价格日历
   *
   * @param skuId SKU ID
   * @param days 天数，默认30天
   * @return SKU价格日历列表
   */
  @ApiOperation(value = "获取SKU价格日历")
  @GetMapping("/sku/{skuId}/calendar-prices")
  public AjaxResult getSkuCalendarPrices(
      @PathVariable("skuId") String skuId,
      @RequestParam(defaultValue = "30") @Min(1) @Max(90) Integer days) {
    log.info("获取SKU价格日历，SKU ID: {}, 天数: {}", skuId, days);
    
    try {
      List<SkuCalendarPriceAppDTO> calendarPrices = goodsSkuService.getSkuCalendarPrices(skuId, days);
      return AjaxResult.success(calendarPrices);
    } catch (Exception e) {
      log.error("获取SKU价格日历失败，SKU ID: {}, 错误信息: {}", skuId, e.getMessage(), e);
      return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), "获取SKU价格日历失败");
    }
  }
} 