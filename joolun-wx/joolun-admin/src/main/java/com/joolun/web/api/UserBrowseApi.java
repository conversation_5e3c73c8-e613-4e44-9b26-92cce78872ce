package com.joolun.web.api;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.common.utils.validation.CheckUtils;
import com.joolun.mall.entity.TyUserBrowseInfo;
import com.joolun.mall.entity.TyUserCollect;
import com.joolun.mall.entity.query.TyUserBrowseInfoQuery;
import com.joolun.mall.entity.query.TyUserCollectQuery;
import com.joolun.mall.service.ITyUserBrowseInfoService;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/userbrowse")
public class UserBrowseApi {

  private final ITyUserBrowseInfoService tyUserBrowseInfoService;
  private final CheckUtils checkUtils;

  /**
   * 分页查询
   *
   * @param page 分页对象
   * @param tyUserBrowseInfo 浏览记录
   * @return
   */
  @ApiOperation(value = "查询浏览记录")
  @GetMapping("/page")
  public AjaxResult getPage(Page page, TyUserBrowseInfo tyUserBrowseInfo) {
    return AjaxResult.success(tyUserBrowseInfoService.selectByPageForApp(page, tyUserBrowseInfo));
  }

  /**
   * 添加浏览记录
   *
   * @param tyUserBrowseInfo
   * @return
   */
  @ApiOperation(value = "添加浏览记录")
  @PostMapping
  public AjaxResult save(@RequestBody TyUserBrowseInfo tyUserBrowseInfo) {
    if (ObjectUtil.isNull(tyUserBrowseInfo.getGoodId())) {
      return AjaxResult.error("添加浏览记录失败，请传递商品ID");
    }

    String userId = ThirdSessionHolder.getWxUserId();
    String goodId = tyUserBrowseInfo.getGoodId();

    // 先删除该用户对该商品的已有浏览记录，避免重复记录
    tyUserBrowseInfoService.remove(
        tyUserBrowseInfoService
            .lambdaQuery()
            .eq(TyUserBrowseInfo::getUserId, userId)
            .eq(TyUserBrowseInfo::getGoodId, goodId)
            .getWrapper());

    // 插入新的浏览记录
    tyUserBrowseInfo.setId(IdUtils.simpleUUID());
    tyUserBrowseInfo.setCreateTime(DateUtils.getNowDate());
    tyUserBrowseInfo.setUserId(userId);
    int i = tyUserBrowseInfoService.insertTyUserBrowseInfo(tyUserBrowseInfo);
    if (i <= 0) {
      return AjaxResult.error("添加浏览记录失败");
    }
    return AjaxResult.success(true);
  }

  /**
   * 批量删除用户浏览记录
   *
   * @param query
   * @return R
   */
  @ApiOperation(value = "批量删除用户浏览记录")
  @PostMapping("/removeByGoodIds")
  public AjaxResult removeByGoodIds(@RequestBody TyUserBrowseInfoQuery query) {
    checkUtils.throwIfEmpty(query.getGoodIds(), "goodIds 不可以为空");
    boolean remove =
        tyUserBrowseInfoService.remove(
            Wrappers.<TyUserBrowseInfo>lambdaQuery()
                .in(TyUserBrowseInfo::getGoodId, query.getGoodIds())
                .eq(TyUserBrowseInfo::getUserId, ThirdSessionHolder.getWxUserId()));
    if (!remove) {
      return AjaxResult.success("用户浏览记录删除失败，未查询到浏览信息", false);
    }
    return AjaxResult.success(true);
  }
}
