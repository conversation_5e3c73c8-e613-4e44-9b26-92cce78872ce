package com.joolun.web.api;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.framework.utils.UserContextUtils;
import com.joolun.mall.entity.TyUserFollowInfo;
import com.joolun.mall.service.ITyUserFollowInfoService;
import com.joolun.weixin.entity.LoginMaDTO;
import com.joolun.weixin.entity.WxOpenDataDTO;
import com.joolun.weixin.entity.WxUser;
import com.joolun.weixin.entity.dto.WxUserDTO;
import com.joolun.weixin.service.IWxUserLevelService;
import com.joolun.weixin.service.WxUserService;
import com.joolun.framework.utils.ThirdSessionHolder;
import com.joolun.weixin.utils.WxMaUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 微信用户
 *
 * <AUTHOR>
 * @date 2019-08-25 15:39:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/wxuser")
@Api(value = "wxuser", tags = "小程序用户API")
public class WxUserApi {

  private final WxUserService wxUserService;

  private final IWxUserLevelService wxUserLevelService;

  private final ITyUserFollowInfoService userFollowInfoService;

  /*
   * 小程序用户登录
   * @param request
   * @param loginMaDTO
   * @return
   */
  @ApiOperation(value = "小程序用户登录")
  @PostMapping("/login")
  public AjaxResult login(HttpServletRequest request, @RequestBody LoginMaDTO loginMaDTO) {
    try {
      WxUser wxUser = wxUserService.loginMa(WxMaUtil.getAppId(request), loginMaDTO.getJsCode());
      return AjaxResult.success(wxUser);
    } catch (Exception e) {
      e.printStackTrace();
      return AjaxResult.error(e.getMessage());
    }
  }

  /**
   * 获取用户信息
   *
   * @param
   * @return
   */
  @ApiOperation(value = "获取用户信息")
  @GetMapping
  public AjaxResult get() {
    String id = ThirdSessionHolder.getThirdSession().getWxUserId();
    WxUser user = wxUserService.getById(id);
    user.setWxUserLevel(wxUserLevelService.selectWxUserLevelById(user.getLevelId()));
    ;
    return AjaxResult.success(user);
  }

  /**
   * 保存用户信息
   *
   * @param wxOpenDataDTO
   * @return
   */
  @ApiOperation(value = "保存用户信息")
  @Deprecated
  @PostMapping
  public AjaxResult saveOrUptateWxUser(@RequestBody WxOpenDataDTO wxOpenDataDTO) {
    wxOpenDataDTO.setAppId(ThirdSessionHolder.getThirdSession().getAppId());
    wxOpenDataDTO.setUserId(ThirdSessionHolder.getThirdSession().getWxUserId());
    
    // 兼容处理：检查传入的sessionKey类型
    String inputSessionKey = wxOpenDataDTO.getSessionKey();
    String thirdSessionKey = ThirdSessionHolder.getThirdSession().getSessionKey();
    
    if (inputSessionKey != null && inputSessionKey.length() > 20 && inputSessionKey.contains("=")) {
      // 如果传入的是真实的微信sessionKey（base64编码，通常包含=），直接使用
      log.info("检测到前端传入真实sessionKey，直接使用进行解密");
      // 保持前端传入的真实sessionKey
    } else {
      // 否则使用系统管理的sessionKey
      log.info("使用系统管理的sessionKey进行解密");
      wxOpenDataDTO.setSessionKey(thirdSessionKey);
    }
    
    log.info("wxOpenDataDTO:{}", JSON.toJSONString(wxOpenDataDTO));
    WxUser wxUser = wxUserService.saveOrUptateWxUser(wxOpenDataDTO);
    return AjaxResult.success(wxUser);
  }

  /**
   * 保存用户信息
   *
   * @param wxUser
   * @return
   */
  @ApiOperation(value = "保存用户信息")
  @PostMapping("/updateWxUser")
  public AjaxResult updateWxUser(@RequestBody WxUserDTO wxUser) {
    WxUser user = wxUserService.updateWxUser(wxUser);
    return AjaxResult.success(user);
  }

  /**
   * 更新上级邀请人
   *
   * @return
   */
  @ApiOperation(value = "更新上级邀请人")
  @PostMapping("/updateUpperUser")
  public AjaxResult updateUpperUser(@RequestBody WxOpenDataDTO wxOpenDataDTO) {
    WxUser user =
        wxUserService.getOne(
            Wrappers.<WxUser>lambdaQuery()
                .eq(WxUser::getInviteCode, wxOpenDataDTO.getInviteCode()));
    WxUser wxUser = new WxUser();
    wxUser.setUpperUser(user.getId());
    wxUser.setId(wxOpenDataDTO.getUserId());
    return AjaxResult.success(wxUserService.saveOrUpdateWxUser2(wxUser));
  }

  /**
   * 邀请明细-分页查询
   *
   * @param page 分页对象
   * @return
   */
  @ApiOperation(value = "邀请明细-分页查询")
  @GetMapping("/getInvitePage")
  public AjaxResult getInvitePage(Page page) {
    String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
    return AjaxResult.success(wxUserService.page1(page, wxUserId));
  }

  /**
   * 添加关注
   *
   * @param userId 用户id
   * @return
   */
  @ApiOperation(value = "添加关注")
  @PutMapping("/addFollow/{userId}")
  public AjaxResult addFollow(@PathVariable("userId") String userId) {
    TyUserFollowInfo tyUserFollowInfo = new TyUserFollowInfo();
    tyUserFollowInfo.setUserId(ThirdSessionHolder.getThirdSession().getWxUserId());
    tyUserFollowInfo.setFollowUserId(userId);
    int i = userFollowInfoService.insertTyUserFollowInfo(tyUserFollowInfo);
    if (i > 0) {
      return AjaxResult.success("添加成功");
    }
    return AjaxResult.error("添加失败");
  }

  /**
   * 删除关注
   *
   * @param userId 用户id
   * @return
   */
  @ApiOperation(value = "删除关注")
  @PutMapping("/delFollow/{userId}")
  public AjaxResult delFollow(@PathVariable("userId") String userId) {
    String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
    int i = userFollowInfoService.deleteTyUserFollowInfo(wxUserId, userId);
    if (i > 0) {
      return AjaxResult.success("取消成功");
    }
    return AjaxResult.error("取消失败");
  }

  /**
   * 关注我的明细-分页查询
   *
   * @param page 分页对象
   * @return
   */
  @ApiOperation(value = "关注我的明细-分页查询")
  @GetMapping("/getFollowMePage")
  public AjaxResult getFollowMePage(Page page) {
    return AjaxResult.success(
        wxUserService.selectFollowMePage(page, UserContextUtils.getCurrentUserId()));
  }

  /**
   * 我的关注明细-分页查询
   *
   * @param page 分页对象
   * @return
   */
  @ApiOperation(value = "我的关注明细-分页查询")
  @GetMapping("/getMyFollowPage")
  public AjaxResult getMyFollowPage(Page page) {
    return AjaxResult.success(
        wxUserService.selectMyFollowPage(page, UserContextUtils.getCurrentUserId()));
  }

  /**
   * 保存签名
   *
   * @param
   * @return
   */
  @ApiOperation(value = "保存签名")
  @PostMapping("/saveSignature")
  public AjaxResult saveSignature(@RequestBody WxUser wxUser) {

    int i =
        wxUserService.updateSignature(
            ThirdSessionHolder.getThirdSession().getWxUserId(), wxUser.getSignature());
    if (i > 0) {
      return AjaxResult.success("保存成功", wxUser.getSignature());
    }
    return AjaxResult.error("保存失败", null);
  }
}
