package com.joolun.web.api;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.DateUtils;
import com.joolun.mall.config.MallConfigProperties;
import com.joolun.mall.dto.PlaceUserLvDTO;
import com.joolun.mall.entity.TyUserLvOrderInfo;
import com.joolun.mall.service.UserLvOrderInfoService;
import com.joolun.weixin.config.WxPayConfiguration;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.weixin.entity.WxUser;
import com.joolun.weixin.service.IWxUserLevelService;
import com.joolun.framework.utils.ThirdSessionHolder;
import com.joolun.weixin.utils.WxMaUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2023/8/23-18:03
 * @Version 1.0.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/userLvOrder")
@Api(value = "userLvOrder", tags = "用户等级订单API")
public class UserLvOrderApi {

   private final UserLvOrderInfoService userLvOrderInfoService;
   private final MallConfigProperties mallConfigProperties;

   private final IWxUserLevelService wxUserLevelService;

   /**
    * 新增等级购买订单
    * @param placeUserLvDTO 新增等级购买订单
    * @return R
    */
   @ApiOperation(value = "新增等级购买订单")
   @PostMapping("byUserLv")
   public AjaxResult byUserLv(HttpServletRequest request, @RequestBody PlaceUserLvDTO placeUserLvDTO){
       //检验用户session登录
       WxUser wxUser = new WxUser();
       wxUser.setId(ThirdSessionHolder.getThirdSession().getWxUserId());
       wxUser.setSessionKey(ThirdSessionHolder.getThirdSession().getSessionKey());
       wxUser.setOpenId(ThirdSessionHolder.getThirdSession().getOpenId());
        placeUserLvDTO.setUserId(ThirdSessionHolder.getWxUserId());
       TyUserLvOrderInfo tyUserLvOrderInfo = userLvOrderInfoService.orderUserLv(placeUserLvDTO);
       if(tyUserLvOrderInfo == null){
            return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
       }
       return AjaxResult.success(tyUserLvOrderInfo);
   }


    /**
     * 调用统一下单接口，并组装生成支付所需参数对象.
     *
     * @param orderInfo 统一下单请求参数
     * @return 返回 {@link com.github.binarywang.wxpay.bean.order}包下的类对象
     */
    @ApiOperation(value = "调用统一下单接口")
    @PostMapping("/unifiedOrder")
    public AjaxResult unifiedOrder(HttpServletRequest request, @RequestBody TyUserLvOrderInfo orderInfo) throws WxPayException {
        //检验用户session登录
        WxUser wxUser = new WxUser();
        wxUser.setId(ThirdSessionHolder.getThirdSession().getWxUserId());
        wxUser.setSessionKey(ThirdSessionHolder.getThirdSession().getSessionKey());
        wxUser.setOpenId(ThirdSessionHolder.getThirdSession().getOpenId());
        TyUserLvOrderInfo tyUserLvOrderInfo = userLvOrderInfoService.getById(orderInfo.getId());
        if(tyUserLvOrderInfo == null){
            return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
        }
        String appId = WxMaUtil.getAppId(request);
        WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();
        wxPayUnifiedOrderRequest.setAppid(appId);

        wxPayUnifiedOrderRequest.setBody(tyUserLvOrderInfo.getOrderName());
        wxPayUnifiedOrderRequest.setOutTradeNo(tyUserLvOrderInfo.getOrderNo());
        wxPayUnifiedOrderRequest.setTotalFee(tyUserLvOrderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue());
        wxPayUnifiedOrderRequest.setTradeType("JSAPI");
        wxPayUnifiedOrderRequest.setNotifyUrl(mallConfigProperties.getNotifyHost()+"/weixin/api/ma/userLvOrder/notify-order");
        wxPayUnifiedOrderRequest.setSpbillCreateIp("127.0.0.1");
        wxPayUnifiedOrderRequest.setOpenid(wxUser.getOpenId());
        WxPayService wxPayService = WxPayConfiguration.getPayService();
        return AjaxResult.success(JSONUtil.parse(wxPayService.createOrder(wxPayUnifiedOrderRequest)));
    }

    /**
     * 支付回调
     * @param xmlData
     * @return
     * @throws WxPayException
     */
    @ApiOperation(value = "支付回调")
    @PostMapping("/notify-order")
    public String notifyOrder(@RequestBody String xmlData) throws WxPayException {
        System.out.println("支付回调:"+xmlData);
        WxPayService wxPayService = WxPayConfiguration.getPayService();
        WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);
        TyUserLvOrderInfo orderInfo = userLvOrderInfoService.getOne(Wrappers.<TyUserLvOrderInfo>lambdaQuery().eq(TyUserLvOrderInfo::getOrderNo,notifyResult.getOutTradeNo()));
        if(orderInfo != null){
            if(orderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue() == notifyResult.getTotalFee()){
                String timeEnd = notifyResult.getTimeEnd();
//				LocalDateTime paymentTime = LocalDateTimeUtils.parse(timeEnd);
                orderInfo.setPaymentTime(DateUtils.parseDate(timeEnd));
                orderInfo.setTransactionId(notifyResult.getTransactionId());
                userLvOrderInfoService.notifyOrder(orderInfo);
                return WxPayNotifyResponse.success("成功");
            }else{
                return WxPayNotifyResponse.fail("付款金额与订单金额不等");
            }
        }else{
            return WxPayNotifyResponse.fail("无此订单");
        }
    }



}
