package com.joolun.web.api;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.entity.ArticleInfo;
import com.joolun.mall.entity.LabelInfo;
import com.joolun.mall.service.GoodsSpuService;
import com.joolun.mall.service.IArticleInfoService;
import com.joolun.mall.service.ILabelInfoService;
import com.joolun.weixin.constant.MyReturnCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2023/11/06-09:09
 * @Version 1.0.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/labelinfo")
@Api(value = "labelApi", tags = "标签接口")
public class LabelApi {
 
   private final ILabelInfoService labelInfoService;


      /**
       * 分页查询
       * @param page 分页对象
       * @param labelInfo 标签信息
       * @return
       */
      @ApiOperation(value = "分页查询")
      @GetMapping("/page")
      public AjaxResult getGoodsSpuPage(Page page, LabelInfo labelInfo) {
       return AjaxResult.success(labelInfoService.page(page,Wrappers.query(labelInfo)));
      }




}
