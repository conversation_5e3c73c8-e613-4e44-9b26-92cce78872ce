package com.joolun.web.controller.mall;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.entity.UserCertInfo;
import com.joolun.mall.service.IUserCertInfoService;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.core.page.TableDataInfo;

/**
 * 用户身份信息Controller
 * 
 * <AUTHOR>
 * @date 2023-07-12
 */
@RestController
@RequestMapping("/userCertInfo")
public class UserCertInfoController extends BaseController
{
    @Autowired
    private IUserCertInfoService userCertInfoService;

    /**
     * 查询用户身份信息列表
     */
    @PreAuthorize("@ss.hasPermi('UserCertInfo:usercertinfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserCertInfo userCertInfo)
    {
        startPage();
        List<UserCertInfo> list = userCertInfoService.selectUserCertInfoList(userCertInfo);
        return getDataTable(list);
    }


    /**
     * 导出用户身份信息列表
     */
    @PreAuthorize("@ss.hasPermi('UserCertInfo:usercertinfo:export')")
    @Log(title = "用户身份信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(UserCertInfo userCertInfo)
    {
        List<UserCertInfo> list = userCertInfoService.selectUserCertInfoList(userCertInfo);
        ExcelUtil<UserCertInfo> util = new ExcelUtil<UserCertInfo>(UserCertInfo.class);
        return util.exportExcel(list, "usercertinfo");
    }

    /**
     * 获取用户身份信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('UserCertInfo:usercertinfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(userCertInfoService.selectUserCertInfoById(id));
    }

    /**
     * 新增用户身份信息
     */
    @PreAuthorize("@ss.hasPermi('UserCertInfo:usercertinfo:add')")
    @Log(title = "用户身份信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserCertInfo userCertInfo)
    {
        return toAjax(userCertInfoService.insertUserCertInfo(userCertInfo));
    }

    /**
     * 修改用户身份信息
     */
    @PreAuthorize("@ss.hasPermi('UserCertInfo:usercertinfo:edit')")
    @Log(title = "用户身份信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserCertInfo userCertInfo)
    {
        return toAjax(userCertInfoService.updateUserCertInfo(userCertInfo));
    }

    /**
     * 删除用户身份信息
     */
    @PreAuthorize("@ss.hasPermi('UserCertInfo:usercertinfo:remove')")
    @Log(title = "用户身份信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(userCertInfoService.deleteUserCertInfoByIds(ids));
    }
}
