/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.mall.entity.TyUserFollowInfo;
import com.joolun.mall.service.ITyUserFollowInfoService;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 线路api
 *
 *
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/userFollow")
@Api(value = "userFollow", tags = "用户关注接口API")
public class TyUserFollowApi {

    private final ITyUserFollowInfoService tyUserFollowInfoService;



	/**
	 * 分页查询
	 * @param page 分页对象
	 * @return
	 */
	@ApiOperation(value = "根据用户查询用户的关注信息")
	@GetMapping("/page")
	public AjaxResult getGoodsSpuPage(Page page, TyUserFollowInfo tyUserFollowInfo) {
		String wxUserId = ThirdSessionHolder.getWxUserId();
		tyUserFollowInfo.setCreateBy(wxUserId);
		return AjaxResult.success(tyUserFollowInfoService.page(page, Wrappers.query(tyUserFollowInfo)));
	}



	/**
	 * 根据被关注用户的ID判断当前用户是否已关注
	 * @param userId 分页对象
	 * @return
	 */
	@ApiOperation(value = "根据被关注用户的ID判断当前用户是否已关注")
	@GetMapping("/checkFollow/{userId}")
	public AjaxResult checkFollow(Page page, @PathVariable("userId") String userId) {
		String wxUserId = ThirdSessionHolder.getWxUserId();
		TyUserFollowInfo tyUserFollowInfo = new TyUserFollowInfo();
		tyUserFollowInfo.setUserId(wxUserId);
		tyUserFollowInfo.setFollowUserId(userId);
		List<TyUserFollowInfo> tyUserFollowInfos = tyUserFollowInfoService.selectTyUserFollowInfoList(tyUserFollowInfo);
		if(tyUserFollowInfos.size()>0){
			return AjaxResult.success("已关注",tyUserFollowInfos.get(0));
		}else {
			return AjaxResult.success("未关注","");
		}
	}




	/**
	 * 添加关注
	 * @param tyUserFollowInfo
	 * @return
	 */
	@ApiOperation(value = "添加关注")
	@PostMapping
	public AjaxResult save(@RequestBody TyUserFollowInfo tyUserFollowInfo){
		tyUserFollowInfo.setId(IdUtils.simpleUUID());
		tyUserFollowInfo.setUserId(ThirdSessionHolder.getThirdSession().getWxUserId());
		int i = tyUserFollowInfoService.insertTyUserFollowInfo(tyUserFollowInfo);
		if(i<=0){
			return AjaxResult.error("添加关注失败");
		}
		return AjaxResult.success(true);
	}


	/***
	 * 关注信息统计
	 *
	 * @return
	 */
	@ApiOperation(value = "根据用户查询用户的关注信息")
	@GetMapping("/followCount/{userId}")
	public AjaxResult getFollowCount(HttpServletRequest request, @PathVariable("userId") String userId) {
		TyUserFollowInfo tyUserFollowInfo = new TyUserFollowInfo();
		tyUserFollowInfo.setUserId(userId);
		List<TyUserFollowInfo> tyUserFollowInfos = tyUserFollowInfoService.selectTyUserFollowInfoList(tyUserFollowInfo);
		TyUserFollowInfo followMe = new TyUserFollowInfo();
		followMe.setFollowUserId(userId);
		List<TyUserFollowInfo> followMes = tyUserFollowInfoService.selectTyUserFollowInfoList(followMe);
		tyUserFollowInfo.setFollowUserCount(tyUserFollowInfos.size());
		tyUserFollowInfo.setFollowMeCount(followMes.size());


		return AjaxResult.success(tyUserFollowInfo);
	}


}
