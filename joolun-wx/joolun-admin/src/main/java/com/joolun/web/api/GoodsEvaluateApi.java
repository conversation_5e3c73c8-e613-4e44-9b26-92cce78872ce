/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.entity.GoodsEvaluate;
import com.joolun.mall.service.IGoodsEvaluateService;
import com.joolun.weixin.constant.MyReturnCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 身份信息liao
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/goodsevaluate")
@Api(value = "cert", tags = "商品评论接口API")
public class GoodsEvaluateApi {

	private final  IGoodsEvaluateService goodsEvaluateService;




	/**
	* 分页查询
	* @param page 分页对象
	* @param goodsEvaluate 评论信息
	* @return
	*/
	@ApiOperation(value = "查询商品评论")
    @GetMapping("/page")
    public AjaxResult getGoodsEvaluatePage(Page page, GoodsEvaluate goodsEvaluate) {
		return AjaxResult.success(goodsEvaluateService.selectPage1(page, goodsEvaluate));
    }

    /**
    * 通过id查询评论详情
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询评论详情")
    @GetMapping("/{id}")
    public AjaxResult getById(HttpServletRequest request, @PathVariable("id") String id){
		GoodsEvaluate goodsEvaluate = goodsEvaluateService.selectGoodsEvaluateById(id);
		if(goodsEvaluate == null){
			return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), MyReturnCode.ERR_80004.getMsg());
		}
        return AjaxResult.success(goodsEvaluate);
    }

	/**
    * 通过id评论点赞+1
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询评论详情")
    @PostMapping("/doGoodsEvaluateLikes")
    public AjaxResult doGoodsEvaluateLikes(HttpServletRequest request, @RequestParam("id") String id){
		int i = goodsEvaluateService.doGoodsEvaluateLikes(id);
		if(i<=0){
			return AjaxResult.error("评论点赞失败！");
		}
        return AjaxResult.success("评论点赞成功！");
    }

	/**
	 * 新增评论信息
	 * @param goodsEvaluate 评论信息
	 * @return R
	 */
	@ApiOperation(value = "新增评论信息")
	@PostMapping
	public AjaxResult save(@RequestBody GoodsEvaluate goodsEvaluate){

		int i = goodsEvaluateService.insertGoodsEvaluate(goodsEvaluate);
		if(i<=0){
			return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
		}
		return AjaxResult.success(true);
	}

	/**
	 * 通过id删除评论信息
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "通过id删除评论")
	@DeleteMapping("/{id}")
	public AjaxResult removeById(@PathVariable String id){
		GoodsEvaluate goodsEvaluate = goodsEvaluateService.selectGoodsEvaluateById(id);
		if(goodsEvaluate == null){
			return AjaxResult.error(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		return AjaxResult.success(goodsEvaluateService.deleteGoodsEvaluateById(id));
	}


}
