package com.joolun.web.api;

import com.alibaba.fastjson.JSONObject;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.redis.RedisCache;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.common.utils.http.HttpUtils;
import com.joolun.system.service.IFileService;
import com.joolun.framework.utils.ThirdSessionHolder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2023/12/11-23:03
 * @Version 1.0.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/public")
public class PublicApi {

    private final RedisCache redisCache;


    private final IFileService fileService;

    /**
     * 获取用户手机号
     */
    @PostMapping(value = "/getPhone")
    public AjaxResult getPhone(@RequestParam("code") String code)
    {
        String token = getToken();
        JSONObject param = new JSONObject();
        param.put("code", code);
        String s = HttpUtils.sendSSLPostAndJson("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token="+token, param.toString());
        JSONObject object = JSONObject.parseObject(s);
        return AjaxResult.success("查询结果",object);
    }

    /**
     * 获取二维码信息
     */
//    @PostMapping(value = "/getRQCode")
//    public AjaxResult getRQcode(@RequestParam("page") String page,@RequestParam("scene") String scene) throws Exception
//    {
//        PutObjectResult result=null;
//        try {
//            String token = getToken();
//            JSONObject param = new JSONObject();
//            param.put("page", page);
//            param.put("scene", scene);
//            String picString = HttpUtils.sendSSLPostAndJson("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+token, param.toString());
//            BASE64Decoder decoder = new BASE64Decoder();
//            byte[] bytes1 = decoder.decodeBuffer(picString);
//
//            ByteArrayInputStream bais = new ByteArrayInputStream(bytes1);
//            BufferedImage img = ImageIO.read(bais);
//            ByteArrayOutputStream os = new ByteArrayOutputStream();
//            ImageIO.write(img, "png", os);
//            String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
//            ByteArrayInputStream inputStream = new ByteArrayInputStream(os.toByteArray());
//            String uploadUrl = fileService.upload(inputStream, "RQCode", wxUserId);
//        }catch (Exception e){
//            e.printStackTrace();
//            return AjaxResult.error("上传失败",e.getMessage());
//        }finally {
//
//        }
//        return AjaxResult.success("返回结果如下：",result.toString());
//    }


    /**
     * 生成小程序码
     */
    @PostMapping(value = "/getRQCode")
    public AjaxResult getRQCode(@RequestParam("page") String page,@RequestParam("scene") String scene) {
        try {
            String token = getToken();
            //获取小程序码，适用于需要的码数量较少的业务场景。通过该接口生成的小程序码，永久有效，有数量限制
//            URL url = new URL("https://api.weixin.qq.com/wxa/getwxacode?access_token=" + accessToken);
            //获取小程序码，适用于需要的码数量极多的业务场景。通过该接口生成的小程序码，永久有效，数量暂无限制。
            URL url = new URL("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + token);

            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod("POST");// 提交模式
            // 发送POST请求必须设置如下两行
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            PrintWriter printWriter = new PrintWriter(httpURLConnection.getOutputStream());
            // 发送请求参数
            cn.hutool.json.JSONObject paramJson = new cn.hutool.json.JSONObject();
            paramJson.put("path", page);
            paramJson.put("scene", scene);
            paramJson.put("width", 430);
            paramJson.put("auto_color", true);
            //设置小程序码版本
            //paramJson.put("env_version","release"); 默认正式
            //paramJson.put("env_version","trial"); 体验版
            //paramJson.put("env_version","develop"); 开发版

            printWriter.write(paramJson.toString());
            // flush输出流的缓冲
            printWriter.flush();
            String contentType = httpURLConnection.getContentType();
            if (contentType.contains("json")) {
                log.info("调用微信小程序生成接口出错,token失效");
                return AjaxResult.error("调用微信小程序生成接口出错");
            } else {
                //开始获取数据
                InputStream is = httpURLConnection.getInputStream();
                String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
                String uploadUrl = fileService.upload(is, "RQCode", wxUserId+".png",  SecurityUtils.getLoginUser().getUser().getTravelId());
                return AjaxResult.success("小程序码已生成",uploadUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("小程序码生成接口出错");
        }
    }



    //获取接口调用token
    private String getToken() {
        Object mpAccessKey = redisCache.getCacheObject("mp_access_key212");
        String token=null;
        if(null==mpAccessKey){
            JSONObject jsonobject = new JSONObject();
            jsonobject.put("grant_type", "client_credential");
            jsonobject.put("appid", "wxd5b9c838bcfddbda");
            jsonobject.put("secret", "6c49b12a6554ae1f32e25cf127349b81");

            String result = HttpUtils.sendSSLPostAndJson("https://api.weixin.qq.com/cgi-bin/stable_token",jsonobject.toString());
            JSONObject object = JSONObject.parseObject(result);
            Object accessToken = object.get("access_token");
            token=accessToken.toString();
            if(null!=accessToken){
                redisCache.setCacheObject("mp_access_key212", token, 7200, TimeUnit.SECONDS);
            }
        }else {
            token=mpAccessKey.toString();
        }
        return token;
    }






}
