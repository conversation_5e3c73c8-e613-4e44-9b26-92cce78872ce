package com.joolun.web.controller.mall;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.entity.SysUserWallet;
import com.joolun.mall.service.ISysUserWalletService;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.core.page.TableDataInfo;

/**
 * 钱包信息Controller
 * 
 * <AUTHOR>
 * @date 2023-07-28
 */
@RestController
@RequestMapping("/mall/wallet")
public class SysUserWalletController extends BaseController
{
    @Autowired
    private ISysUserWalletService sysUserWalletService;

    /**
     * 查询钱包信息列表
     */
    @PreAuthorize("@ss.hasPermi('mall:wallet:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUserWallet sysUserWallet)
    {
        startPage();
        List<SysUserWallet> list = sysUserWalletService.selectSysUserWalletList(sysUserWallet);
        return getDataTable(list);
    }

    /**
     * 导出钱包信息列表
     */
    @PreAuthorize("@ss.hasPermi('mall:wallet:export')")
    @Log(title = "钱包信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SysUserWallet sysUserWallet)
    {
        List<SysUserWallet> list = sysUserWalletService.selectSysUserWalletList(sysUserWallet);
        ExcelUtil<SysUserWallet> util = new ExcelUtil<SysUserWallet>(SysUserWallet.class);
        return util.exportExcel(list, "wallet");
    }

    /**
     * 获取钱包信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:wallet:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(sysUserWalletService.selectSysUserWalletById(id));
    }

    /**
     * 新增钱包信息
     */
    @PreAuthorize("@ss.hasPermi('mall:wallet:add')")
    @Log(title = "钱包信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysUserWallet sysUserWallet)
    {
        return toAjax(sysUserWalletService.insertSysUserWallet(sysUserWallet));
    }

    /**
     * 修改钱包信息
     */
    @PreAuthorize("@ss.hasPermi('mall:wallet:edit')")
    @Log(title = "钱包信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUserWallet sysUserWallet)
    {
        return toAjax(sysUserWalletService.updateSysUserWallet(sysUserWallet));
    }

    /**
     * 删除钱包信息
     */
    @PreAuthorize("@ss.hasPermi('mall:wallet:remove')")
    @Log(title = "钱包信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(sysUserWalletService.deleteSysUserWalletByIds(ids));
    }
}
