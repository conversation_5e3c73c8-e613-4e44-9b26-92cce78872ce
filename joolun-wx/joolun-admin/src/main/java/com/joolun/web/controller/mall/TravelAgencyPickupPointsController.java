package com.joolun.web.controller.mall;

import java.util.List;

import com.joolun.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.entity.TravelAgencyPickupPoints;
import com.joolun.mall.service.ITravelAgencyPickupPointsService;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.core.page.TableDataInfo;

/**
 * 旅行社乘车点
Controller
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@RestController
@RequestMapping("/mall/points")
public class TravelAgencyPickupPointsController extends BaseController
{
    @Autowired
    private ITravelAgencyPickupPointsService travelAgencyPickupPointsService;

    /**
     * 查询旅行社乘车点列表
     */
    @PreAuthorize("@ss.hasPermi('mall:points:list')")
    @GetMapping("/list")
    public TableDataInfo list(TravelAgencyPickupPoints travelAgencyPickupPoints)
    {
        startPage();
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if(!SecurityUtils.isAdmin(userId)){//非管理员 添加对应旅行社id
            travelAgencyPickupPoints.setTravelId(SecurityUtils.getLoginUser().getUser().getTravelId());
        }
        List<TravelAgencyPickupPoints> list = travelAgencyPickupPointsService.selectTravelAgencyPickupPointsList(travelAgencyPickupPoints);
        return getDataTable(list);
    }

    /**
     * 导出旅行社乘车点列表
     */
    @PreAuthorize("@ss.hasPermi('mall:points:export')")
    @Log(title = "旅行社乘车点", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TravelAgencyPickupPoints travelAgencyPickupPoints)
    {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if(!SecurityUtils.isAdmin(userId)){//非管理员 添加对应旅行社id
            travelAgencyPickupPoints.setTravelId(SecurityUtils.getLoginUser().getUser().getTravelId());
        }
        List<TravelAgencyPickupPoints> list = travelAgencyPickupPointsService.selectTravelAgencyPickupPointsList(travelAgencyPickupPoints);
        ExcelUtil<TravelAgencyPickupPoints> util = new ExcelUtil<TravelAgencyPickupPoints>(TravelAgencyPickupPoints.class);
        return util.exportExcel(list, "points");
    }

    /**
     * 获取旅行社乘车点详细信息
     */
    @PreAuthorize("@ss.hasPermi('mall:points:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(travelAgencyPickupPointsService.selectTravelAgencyPickupPointsById(id));
    }

    /**
     * 新增旅行社乘车点
     */
    @PreAuthorize("@ss.hasPermi('mall:points:add')")
    @Log(title = "旅行社乘车点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TravelAgencyPickupPoints travelAgencyPickupPoints)
    {
        return toAjax(travelAgencyPickupPointsService.insertTravelAgencyPickupPoints(travelAgencyPickupPoints));
    }

    /**
     * 修改旅行社乘车点

     */
    @PreAuthorize("@ss.hasPermi('mall:points:edit')")
    @Log(title = "旅行社乘车点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TravelAgencyPickupPoints travelAgencyPickupPoints)
    {
        return toAjax(travelAgencyPickupPointsService.updateTravelAgencyPickupPoints(travelAgencyPickupPoints));
    }

    /**
     * 删除旅行社乘车点

     */
    @PreAuthorize("@ss.hasPermi('mall:points:remove')")
    @Log(title = "旅行社乘车点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(travelAgencyPickupPointsService.deleteTravelAgencyPickupPointsByIds(ids));
    }
}
