package com.joolun.web.api;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.DateUtils;
import com.joolun.mall.config.MallConfigProperties;
import com.joolun.trip.domain.TyOrderInfo;
import com.joolun.trip.dto.RouteOrderDTO;
import com.joolun.trip.service.TyOrderInfoService;
import com.joolun.trip.utils.RequestUtils;
import com.joolun.weixin.config.WxPayConfiguration;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.weixin.entity.WxUser;
import com.joolun.weixin.utils.LocalDateTimeUtils;
import com.joolun.framework.utils.ThirdSessionHolder;
import com.joolun.weixin.utils.WxMaUtil;
import io.swagger.annotations.ApiOperation;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Deprecated
@RestController
@RequestMapping({"/weixin/api/ma/tyOrderInfo"})
public class TyOrderInfoApi {

    private final TyOrderInfoService orderInfoService;

    private final MallConfigProperties mallConfigProperties;

    public TyOrderInfoApi(TyOrderInfoService orderInfoService, MallConfigProperties mallConfigProperties) {
        this.orderInfoService = orderInfoService;
        this.mallConfigProperties = mallConfigProperties;
    }

    @ApiOperation("")
    @GetMapping({"/page"})
    public AjaxResult getOrderInfoPage(Page page, TyOrderInfo orderInfo) {
        orderInfo.setUserId(ThirdSessionHolder.getWxUserId());
        return AjaxResult.success(this.orderInfoService.page2((IPage) page, orderInfo));
    }

    @ApiOperation("")
    @GetMapping({"/{id}"})
    public AjaxResult getById(HttpServletRequest request, @PathVariable("id") String id) {
        return AjaxResult.success(this.orderInfoService.getById2(id));
    }

    @ApiOperation("")
    @PostMapping
    public AjaxResult save(@RequestBody RouteOrderDTO placeOrderDTO) {
        placeOrderDTO.setUserId(ThirdSessionHolder.getWxUserId());
        placeOrderDTO.setOpenId(ThirdSessionHolder.getOpenId());
        TyOrderInfo orderInfo = this.orderInfoService.tyOrderRoutes(placeOrderDTO);
        if (orderInfo == null)
            return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
        return AjaxResult.success(orderInfo);
    }

    @ApiOperation("")
    @PutMapping({"/cancel/{id}"})
    public AjaxResult orderCancel(@PathVariable String id) {
        TyOrderInfo orderInfo = (TyOrderInfo) this.orderInfoService.getById(id);
        if (orderInfo == null)
            return AjaxResult.error(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
        if (!"0".equals(orderInfo.getIsPay()))
            return AjaxResult.error(MyReturnCode.ERR_70001.getCode(), MyReturnCode.ERR_70001.getMsg());
        this.orderInfoService.orderCancel(orderInfo);
        return AjaxResult.success();
    }

    @ApiOperation("")
    @PostMapping({"/unifiedOrder"})
    public AjaxResult unifiedOrder(HttpServletRequest request, @RequestBody TyOrderInfo orderInfo) throws WxPayException {
        WxUser wxUser = new WxUser();
        wxUser.setId(ThirdSessionHolder.getThirdSession().getWxUserId());
        wxUser.setSessionKey(ThirdSessionHolder.getThirdSession().getSessionKey());
        wxUser.setOpenId(ThirdSessionHolder.getThirdSession().getOpenId());
        orderInfo = (TyOrderInfo) this.orderInfoService.getById(orderInfo.getId());
        if (orderInfo == null)
            return AjaxResult.error(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
        if (!"0".equals(orderInfo.getIsPay()))
            return AjaxResult.error(MyReturnCode.ERR_70004.getCode(), MyReturnCode.ERR_70004.getMsg());
        if (orderInfo.getPaymentPrice().compareTo(BigDecimal.ZERO) == 0) {
            orderInfo.setPaymentTime(LocalDateTime.now());
            this.orderInfoService.notifyOrder(orderInfo);
            return AjaxResult.success();
        }
        String appId = WxMaUtil.getAppId(request);
        WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();
        wxPayUnifiedOrderRequest.setAppid(appId);
        String body = orderInfo.getName();
        body = (body.length() > 40) ? body.substring(0, 39) : body;
        wxPayUnifiedOrderRequest.setBody(body);
        wxPayUnifiedOrderRequest.setOutTradeNo(orderInfo.getOrderNo());
        wxPayUnifiedOrderRequest.setTotalFee(Integer.valueOf(orderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue()));
        wxPayUnifiedOrderRequest.setTradeType("JSAPI");
        wxPayUnifiedOrderRequest.setNotifyUrl(this.mallConfigProperties.getNotifyHost() + "/weixin/api/ma/tyOrderInfo/notify-order");
        wxPayUnifiedOrderRequest.setSpbillCreateIp("127.0.0.1");
        wxPayUnifiedOrderRequest.setOpenid(wxUser.getOpenId());
        WxPayService wxPayService = WxPayConfiguration.getPayService();
        return AjaxResult.success(JSONUtil.parse(wxPayService.createOrder(wxPayUnifiedOrderRequest)));
    }

    @ApiOperation("")
    @PostMapping({"/notify-order"})
    public String notifyOrder(@RequestBody String xmlData) throws WxPayException {
        WxPayService wxPayService = WxPayConfiguration.getPayService();
        WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);
        TyOrderInfo orderInfo = (TyOrderInfo) this.orderInfoService.getById(notifyResult.getOutTradeNo());
        if (orderInfo != null) {
            if (orderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue() == notifyResult.getTotalFee().intValue()) {
                String timeEnd = notifyResult.getTimeEnd();
                LocalDateTime paymentTime = LocalDateTimeUtils.parse(timeEnd);
                orderInfo.setPaymentTime(paymentTime);
                orderInfo.setTransactionId(notifyResult.getTransactionId());
                this.orderInfoService.notifyOrder(orderInfo);
                return WxPayNotifyResponse.success("");
            }
            return WxPayNotifyResponse.fail("");
        }
        return WxPayNotifyResponse.fail("");
    }

    @ApiOperation("")
    @GetMapping({"/countAll"})
    public AjaxResult count(TyOrderInfo orderInfo) {
        orderInfo.setUserId(ThirdSessionHolder.getWxUserId());
        Map<String, Integer> countAll = new HashMap<>();
        return AjaxResult.success(countAll);
    }

    @ApiOperation("")
    @PostMapping({"/refundOrder"})
    public AjaxResult refunds(HttpServletRequest request, @RequestBody TyOrderInfo orderInfo) throws WxPayException {
        WxUser wxUser = new WxUser();
        wxUser.setId(ThirdSessionHolder.getThirdSession().getWxUserId());
        wxUser.setSessionKey(ThirdSessionHolder.getThirdSession().getSessionKey());
        wxUser.setOpenId(ThirdSessionHolder.getThirdSession().getOpenId());
        orderInfo = (TyOrderInfo) this.orderInfoService.getById(orderInfo.getId());
        if (orderInfo == null)
            return AjaxResult.error(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
        if (!"1".equals(orderInfo.getIsPay()))
            return AjaxResult.error(MyReturnCode.ERR_70004.getCode(), MyReturnCode.ERR_70004.getMsg());
        if (orderInfo.getPaymentPrice().compareTo(BigDecimal.ZERO) == 0) {
            orderInfo.setPaymentTime(LocalDateTime.now());
            this.orderInfoService.notifyOrder(orderInfo);
            return AjaxResult.success();
        }
        WxPayService wxPayService = WxPayConfiguration.getPayService();
        WxPayRefundV3Request wxPayRefundV3Request = new WxPayRefundV3Request();
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        BigInteger bigInteger = orderInfo.getPaymentPrice().unscaledValue();
        amount.setRefund(Integer.valueOf(orderInfo.getPaymentPrice().unscaledValue().intValue()));
        amount.setTotal(Integer.valueOf(orderInfo.getPaymentPrice().unscaledValue().intValue()));
        amount.setCurrency("CNY");
        wxPayRefundV3Request.setAmount(amount);
        wxPayRefundV3Request.setOutRefundNo(orderInfo.getOrderNo());
        wxPayRefundV3Request.setTransactionId(orderInfo.getTransactionId());
        wxPayRefundV3Request.setNotifyUrl(this.mallConfigProperties.getNotifyHost() + "/weixin/api/ma/tyOrderInfo/refund-order");
        WxPayRefundRequest wxPayRefundRequest = new WxPayRefundRequest();
        this.orderInfoService.orderCancel(orderInfo);
        return AjaxResult.success(JSONUtil.parse(wxPayService.refundV3(wxPayRefundV3Request)));
    }

    @ApiOperation("")
    @PostMapping({"/refund-order"})
    public String refundsrder(HttpServletRequest request, @RequestBody String data) throws WxPayException {
        WxPayService wxPayService = WxPayConfiguration.getPayService();
        SignatureHeader signatureHeader = RequestUtils.fetchRequest2SignatureHeader(request);
        WxPayRefundNotifyV3Result wxPayRefundNotifyV3Result = wxPayService.parseRefundNotifyV3Result(data, signatureHeader);
        WxPayRefundNotifyV3Result.DecryptNotifyResult result = wxPayRefundNotifyV3Result.getResult();
        TyOrderInfo orderInfo = orderInfoService.getById(result.getOutTradeNo());
        if (orderInfo != null) {
            String timeEnd = result.getSuccessTime();
            Date dateTimeNow = DateUtils.parseDate(timeEnd);
            orderInfo.setRefundTime(dateTimeNow);
            orderInfo.setTransactionId(result.getTransactionId());
            this.orderInfoService.refundOrder(orderInfo);
            return WxPayNotifyResponse.success("");
        }
        return WxPayNotifyResponse.fail("");
    }
}
