package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.system.domain.SysNotice;
import com.joolun.system.service.ISysNoticeService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公告信息 <AUTHOR> @Description //TODO @Date 2023/7/28-23:41 @Version 1.0.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/notice")
public class NoticeApi {

  private final ISysNoticeService noticeService;

  /**
   * 获取通知公告列表
   *
   * @param notice 身份信息
   * @return
   */
  @ApiOperation(value = "获取通知公告列表")
  @GetMapping("/list")
  public AjaxResult getNoticeList(SysNotice notice) {
    notice.setStatus("0"); // 近查询有效状态的
    return AjaxResult.success(noticeService.selectNoticeList(notice));
  }

  /** 根据通知公告编号获取详细信息 */
  @GetMapping(value = "/{noticeId}")
  public AjaxResult getInfo(@PathVariable Long noticeId) {
    return AjaxResult.success(noticeService.selectNoticeById(noticeId));
  }
}
