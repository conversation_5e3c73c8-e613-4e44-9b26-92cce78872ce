package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.domain.entity.TyCity;
import com.joolun.mall.service.ITyCityService;
import com.joolun.system.service.ISysConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/config")
public class ConfigApi {

    private final ITyCityService cityService;

    private final ISysConfigService configService;


    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey)
    {
        return AjaxResult.success("查询成功",configService.selectConfigByKey(configKey));
    }

}

