package com.joolun.web.controller.mall;

import com.joolun.common.annotation.Log;
import com.joolun.common.config.JooLunConfig;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.common.utils.file.FileUploadUtils;
import com.joolun.framework.config.ServerConfig;
import com.joolun.mall.constant.GoodsSpuUploadFileConstants;
import com.joolun.mall.entity.GoodsSpuUploadFile;
import com.joolun.mall.service.GoodsSpuUploadFileService;
import com.joolun.system.service.IFileService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * spu商品文件管理
 *
 * <AUTHOR>
 * @date 2024-08-22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsSpu/uploadFile")
@Api(value = "/goodsSpu/uploadFile", tags = "spu商品文件管理")
public class GoodsSpuUploadFileController extends BaseController {
  private final ServerConfig serverConfig;
  private final GoodsSpuUploadFileService goodsSpuUploadFileService;
  private final IFileService fileService;

  @Log(title = "删除商品相关文件", businessType = BusinessType.DELETE)
  @DeleteMapping("{ids}")
  public AjaxResult remove(@PathVariable String[] ids) {
    return goodsSpuUploadFileService.deleteByIds(ids) ? AjaxResult.success() : AjaxResult.error();
  }

  /**
   * 上传请求
   *
   * @param file 上传的文件
   * @param fileName 文件名
   * @param objectType 图片类型（0-封面图，1-轮播图，2-线路介绍图），默认为0
   * @param objectId 关联的商品ID
   * @return 上传结果
   */
  @PostMapping("/save")
  public AjaxResult save(
      MultipartFile file,
      @RequestParam("fileName") String fileName,
      @RequestParam(value = "objectType", defaultValue = GoodsSpuUploadFileConstants.UPLOAD_FILE_TYPE_DEFAULT) String objectType,
      @RequestParam(value = "objectId", required = false) String objectId) {
    try {
      // 参数校验
      if (!isValidObjectType(objectType)) {
        return AjaxResult.error("无效的图片类型参数");
      }
      
      // 如果是封面图，检查数量限制
      if (GoodsSpuUploadFileConstants.UPLOAD_FILE_TYPE_DEFAULT.equals(objectType) && 
          StringUtils.hasText(objectId)) {
        boolean exceedsLimit = goodsSpuUploadFileService.checkCoverImageLimit(objectId);
        if (exceedsLimit) {
          return AjaxResult.error("封面图最多只能上传1张");
        }
      }

      // 使用OSS上传文件，传递travelId参数
      String url =
          fileService.upload(
              file.getInputStream(),
              "goods",
              fileName,
              SecurityUtils.getLoginUser().getUser().getTravelId());

      GoodsSpuUploadFile.GoodsSpuUploadFileBuilder goodsSpuUploadFileBuilder =
          GoodsSpuUploadFile.builder()
              .objectType(objectType)
              .objectId(objectId)
              .fileUrl(url)
              .fileName(fileName)
              .customName(file.getOriginalFilename())
              .filePath("oss")
              .fileSort(0);
              
      GoodsSpuUploadFile build = goodsSpuUploadFileBuilder.build();
      goodsSpuUploadFileService.save(build);
      return AjaxResult.success(build);
    } catch (Exception e) {
      log.error("上传文件 发生异常:{}", e.getMessage(), e);
      return AjaxResult.error(e.getMessage());
    }
  }
  
  /**
   * 批量更新图片的商品关联ID
   *
   * @param fileIds 图片ID列表，多个ID用逗号分隔
   * @param objectId 商品ID
   * @return 更新结果
   */
  @PostMapping("/updateObjectId")
  @Log(title = "批量更新图片商品关联", businessType = BusinessType.UPDATE)
  public AjaxResult updateObjectId(
      @RequestParam("fileIds") String fileIds,
      @RequestParam("objectId") String objectId) {
    try {
      if (!StringUtils.hasText(fileIds) || !StringUtils.hasText(objectId)) {
        return AjaxResult.error("参数不能为空");
      }
      
      List<String> fileIdList = Arrays.asList(fileIds.split(","));
      boolean result = goodsSpuUploadFileService.updateObjectIdBatch(fileIdList, objectId);
      return result ? AjaxResult.success("批量更新成功") : AjaxResult.error("批量更新失败");
    } catch (Exception e) {
      log.error("批量更新图片关联ID失败: {}", e.getMessage(), e);
      return AjaxResult.error("批量更新失败: " + e.getMessage());
    }
  }
  
  /**
   * 查询孤儿图片（运营监控接口）
   * 用于监控未关联到商品的图片，帮助运营人员发现和处理问题
   *
   * @return 孤儿图片列表
   */
  @GetMapping("/orphanImages")
  @Log(title = "查询孤儿图片", businessType = BusinessType.OTHER)
  public AjaxResult getOrphanImages() {
    try {
      // 查找1小时前到现在的未关联图片
      Date oneHourAgo = new Date(System.currentTimeMillis() - 60 * 60 * 1000);
      
      List<GoodsSpuUploadFile> orphanImages = goodsSpuUploadFileService
          .lambdaQuery()
          .and(wrapper -> wrapper
              .isNull(GoodsSpuUploadFile::getObjectId)
              .or()
              .eq(GoodsSpuUploadFile::getObjectId, "")
          )
          .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
          .ge(GoodsSpuUploadFile::getCreateTime, oneHourAgo)
          .orderByDesc(GoodsSpuUploadFile::getCreateTime)
          .list();
      
      log.info("发现 {} 个孤儿图片（1小时内）", orphanImages.size());
      return AjaxResult.success(orphanImages);
    } catch (Exception e) {
      log.error("查询孤儿图片失败:{}", e.getMessage(), e);
      return AjaxResult.error("查询失败：" + e.getMessage());
    }
  }
  
  /**
   * 校验图片类型参数是否有效
   * 
   * @param objectType 图片类型
   * @return 是否有效
   */
  private boolean isValidObjectType(String objectType) {
    List<String> validTypes = Arrays.asList(
        GoodsSpuUploadFileConstants.UPLOAD_FILE_TYPE_DEFAULT,
        GoodsSpuUploadFileConstants.UPLOAD_FILE_TYPE_CAROUSEL,
        GoodsSpuUploadFileConstants.UPLOAD_FILE_TYPE_ITINERARY_INFOGRAPHIC
    );
    return validTypes.contains(objectType);
  }
}
