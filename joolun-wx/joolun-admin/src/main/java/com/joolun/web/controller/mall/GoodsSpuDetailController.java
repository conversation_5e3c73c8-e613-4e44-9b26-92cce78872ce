/**
 * Copyright (C) 2018-2019 All rights reserved, Designed By www.joolun.com 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.controller.mall;

import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.page.TableDataInfo;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.dto.GoodsSpuDetailDTO;
import com.joolun.mall.dto.GoodsSpuDetailPricesDTO;
import com.joolun.mall.dto.GoodsSpuDetailSaveDTO;
import com.joolun.mall.entity.GoodsSpuDetail;
import com.joolun.mall.service.GoodsSpuDetailService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * spuDetail商品SKU
 *
 * <AUTHOR>
 * @date 2019-08-12 16:25:10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsSpuDetail")
@Api(value = "goodsSpuDetail", tags = "spu商品SKU详情管理")
@Deprecated
public class GoodsSpuDetailController extends BaseController {

  private final GoodsSpuDetailService service;

  /** 查询商品SKU列表 */
  @PostMapping("/list")
  public TableDataInfo list(@RequestBody GoodsSpuDetailDTO dto) {
    startPage();
    List<GoodsSpuDetailPricesDTO> list = service.selectGoodsSpuDetailList(dto);
    return getDataTable(list);
  }

  /** 获取商品SKU详细信息 */
  @GetMapping(value = "/getInfo")
  public AjaxResult getInfo(@ModelAttribute GoodsSpuDetailDTO dto) {
    return AjaxResult.success(service.getInfo(dto));
  }

  /**
   * 新增商品SKU
   *
   * @param dto
   * @return
   */
  @Log(title = "商品SKU", businessType = BusinessType.INSERT)
  @PostMapping(value = "/add")
  public AjaxResult add(@RequestBody GoodsSpuDetailSaveDTO dto) {
    return service.add(dto) ? AjaxResult.success() : AjaxResult.error();
  }

  /** 修改商品SKU */
  @Log(title = "商品SKU", businessType = BusinessType.UPDATE)
  @PostMapping(value = "/edit")
  public AjaxResult edit(@RequestBody GoodsSpuDetail goodsSpuDetail) {
    return service.updateById(goodsSpuDetail) ? AjaxResult.success() : AjaxResult.error();
  }

  /** 删除商品SKU */
  @Log(title = "商品SKU", businessType = BusinessType.DELETE)
  @DeleteMapping("/deleteGoodsSpuDetail")
  public AjaxResult deleteGoodsSpuDetail(@RequestBody GoodsSpuDetailDTO dto) {
    return service.removeGoodsSpuDetail(dto) ? AjaxResult.success() : AjaxResult.error();
  }
}
