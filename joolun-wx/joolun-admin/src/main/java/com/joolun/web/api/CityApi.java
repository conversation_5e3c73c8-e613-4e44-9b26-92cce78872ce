package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.domain.entity.TyCity;
import com.joolun.mall.service.ITyCityService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 城市接口-小程序端
 * <AUTHOR>
 * @pactera.com
 * @Description
 * @Date 2023/5/18-18:00
 * @Version 1.0.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/city")
public class CityApi {

  private final ITyCityService cityService;

  /** 获取城市树列表 */
  @GetMapping("/treeselect")
  public AjaxResult treeselect(TyCity city) {
    List<TyCity> tyCityList = cityService.selectTyCityList(city);
    return AjaxResult.success(cityService.buildCityTree(tyCityList));
  }

  /** 获取城市树列表 */
  @GetMapping("/getDestTree")
  public AjaxResult getDestTree() {
    List<TyCity> tyCityList = cityService.selectDest();
    return AjaxResult.success(cityService.buildCityTree(tyCityList));
  }

  /** 获取城市树列表 */
  @GetMapping("/getHostDest")
  public AjaxResult getHostDest() {
    return AjaxResult.success(cityService.selectHostDest());
  }

  /** 获取热门城市列表 */
  @GetMapping("/hotCities")
  public AjaxResult getHotCities() {
    List<TyCity> hotCities = cityService.selectHotCities();
    return AjaxResult.success(hotCities);
  }
}
