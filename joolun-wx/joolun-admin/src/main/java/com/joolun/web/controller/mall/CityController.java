package com.joolun.web.controller.mall;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.domain.entity.TyCity;
import com.joolun.common.core.domain.query.TyCityQuery;
import com.joolun.common.core.page.TableDataInfo;
import com.joolun.common.enums.BusinessType;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.mall.service.ITyCityService;
import com.joolun.system.service.IFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.joolun.common.annotation.Log;

/**
 * 商品类目
 *
 * <AUTHOR>
 * @date 2019-08-12 11:46:28
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/city")
@Api(value = "city", tags = "城市信息")
public class CityController extends BaseController {

  private final ITyCityService cityService;
  private final IFileService fileService;

  /** 获取城市树列表 */
  @GetMapping("/tree")
  public AjaxResult treeselect(TyCity city) {
    List<TyCity> tyCityList = cityService.selectTyCityList(city);
    return AjaxResult.success(cityService.buildCityTree(tyCityList));
  }

  /** 获取城市树列表 */
  @GetMapping("/treeNotZero")
  public AjaxResult selectTyCityListNotZero(TyCity city) {
    List<TyCity> tyCityList = cityService.selectTyCityListNotZero();
    return AjaxResult.success(cityService.buildCityTree(tyCityList));
  }

//  @ApiOperation(value = "获取文件列表")
//  @GetMapping("/list")
//  public AjaxResult list() {
//    return AjaxResult.success(fileService.listFiles());
//  }

  /** 设置热门城市 */
  @ApiOperation(value = "设置热门城市")
  @PostMapping("/setHotCities")
  public AjaxResult setHotCities(@RequestBody TyCityQuery query) {
    return toAjax(cityService.setHotCities(query));
  }

  /** 获取热门城市列表 */
  @ApiOperation(value = "获取热门城市列表")
  @GetMapping("/hotCities")
  public AjaxResult getHotCities() {
    List<TyCity> hotCities = cityService.selectHotCities();
    return AjaxResult.success(hotCities);
  }

  /** 查询city列表 */
  @PreAuthorize("@ss.hasPermi('system:city:list')")
  @GetMapping("/list")
  public TableDataInfo list(TyCity tyCity) {
    startPage();
    List<TyCity> list = cityService.selectTyCityList(tyCity);
    return getDataTable(list);
  }

  /**
   * 分页查询
   *
   * @param page 分页对象
   * @param tyCity
   * @return
   */
  @ApiOperation(value = "分页查询")
  @GetMapping("/page")
  @PreAuthorize("@ss.hasPermi('mall:useraddress:index')")
  public AjaxResult page(Page page, TyCity tyCity) {
    return AjaxResult.success(cityService.page(page, Wrappers.query(tyCity)));
  }

  /** 导出city列表 */
  @PreAuthorize("@ss.hasPermi('system:city:export')")
  @Log(title = "city", businessType = BusinessType.EXPORT)
  @GetMapping("/export")
  public AjaxResult export(TyCity tyCity) {
    List<TyCity> list = cityService.selectTyCityList(tyCity);
    ExcelUtil<TyCity> util = new ExcelUtil<TyCity>(TyCity.class);
    return util.exportExcel(list, "city");
  }

  /** 获取city详细信息 */
  @PreAuthorize("@ss.hasPermi('system:city:query')")
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id) {
    return AjaxResult.success(cityService.selectTyCityById(id));
  }

  /** 新增city */
  @PreAuthorize("@ss.hasPermi('system:city:add')")
  @Log(title = "city", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody TyCity tyCity) {
    return toAjax(cityService.insertTyCity(tyCity));
  }

  /** 修改city */
  @PreAuthorize("@ss.hasPermi('system:city:edit')")
  @Log(title = "city", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody TyCity tyCity) {
    return toAjax(cityService.updateTyCity(tyCity));
  }

  /** 删除city */
  @PreAuthorize("@ss.hasPermi('system:city:remove')")
  @Log(title = "city", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Long[] ids) {
    return toAjax(cityService.deleteTyCityByIds(ids));
  }
}
