package com.joolun.web.api;

import cn.hutool.core.util.StrUtil;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.weixin.constant.SearchConstants;
import com.joolun.weixin.entity.SearchHistoryVO;
import com.joolun.weixin.service.ISearchHistoryService;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 搜索历史API
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/search/history")
@Api(value = "searchHistory", tags = "搜索历史API")
@Validated
public class SearchHistoryApi {

  private final ISearchHistoryService searchHistoryService;

  /** 保存搜索历史 */
  @ApiOperation(value = "保存搜索历史")
  @PostMapping
  public AjaxResult saveSearchHistory(@RequestBody Map<String, Object> params) {
    try {
      String keyword = (String) params.get("keyword");
      String searchType = (String) params.get("searchType");
      Integer resultCount = (Integer) params.get("resultCount");

      if (StrUtil.isBlank(keyword)) {
        return AjaxResult.success("无关键词保存");
      }

      if (StrUtil.isNotBlank(keyword)) {
        keyword = keyword.trim();
      }

      if (searchType == null || searchType.trim().isEmpty()) {
        searchType = SearchConstants.SEARCH_TYPE_ALL;
      }

      if (resultCount == null) {
        resultCount = 0;
      }

      String userId = ThirdSessionHolder.getWxUserId();
      boolean success =
          searchHistoryService.saveSearchHistory(keyword.trim(), searchType, userId, resultCount);

      if (success) {
        return AjaxResult.success("保存成功");
      } else {
        return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), "保存失败");
      }

    } catch (Exception e) {
      log.error("保存搜索历史异常", e);
      return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), "保存失败");
    }
  }

  /** 获取搜索历史列表 */
  @ApiOperation(value = "获取搜索历史列表")
  @GetMapping
  public AjaxResult getSearchHistoryList() {
    try {
      String userId = ThirdSessionHolder.getWxUserId();
      List<SearchHistoryVO> historyList = searchHistoryService.getSearchHistoryList(userId);
      return AjaxResult.success(historyList);

    } catch (Exception e) {
      log.error("获取搜索历史列表异常", e);
      return AjaxResult.error(MyReturnCode.ERR_70002.getCode(), "获取失败");
    }
  }

  /** 删除单条搜索历史 */
  @ApiOperation(value = "删除单条搜索历史")
  @DeleteMapping("/{keyword}")
  public AjaxResult deleteSearchHistory(
      @ApiParam(value = "搜索关键词", required = true) @PathVariable("keyword") @NotBlank
          String keyword) {
    try {
      String userId = ThirdSessionHolder.getWxUserId();
      boolean success = searchHistoryService.deleteSearchHistory(userId, keyword);

      if (success) {
        return AjaxResult.success("删除成功");
      } else {
        return AjaxResult.error(MyReturnCode.ERR_70004.getCode(), "删除失败");
      }

    } catch (Exception e) {
      log.error("删除搜索历史异常: keyword={}", keyword, e);
      return AjaxResult.error(MyReturnCode.ERR_70004.getCode(), "删除失败");
    }
  }

  /** 清空搜索历史 */
  @ApiOperation(value = "清空搜索历史")
  @DeleteMapping("/clear")
  public AjaxResult clearSearchHistory() {
    try {
      String userId = ThirdSessionHolder.getWxUserId();
      boolean success = searchHistoryService.clearSearchHistory(userId);

      if (success) {
        return AjaxResult.success("清空成功");
      } else {
        return AjaxResult.error(MyReturnCode.ERR_70004.getCode(), "清空失败");
      }

    } catch (Exception e) {
      log.error("清空搜索历史异常", e);
      return AjaxResult.error(MyReturnCode.ERR_70004.getCode(), "清空失败");
    }
  }
}
