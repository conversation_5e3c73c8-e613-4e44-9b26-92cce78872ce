/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.controller.mall;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.page.TableDataInfo;
import com.joolun.common.enums.BusinessType;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.common.utils.StringUtils;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.mall.dto.GoodsSpuDTO;
import com.joolun.mall.entity.GoodsCostDetail;
import com.joolun.mall.entity.GoodsSpu;
import com.joolun.mall.service.GoodsSpuService;
import com.joolun.mall.service.IGoodsCostDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * spu商品
 *
 * <AUTHOR>
 * @date 2019-08-12 16:25:10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsspu")
@Api(value = "goodsspu", tags = "商品SPU管理")
public class GoodsSpuController extends BaseController {

    private final GoodsSpuService goodsSpuService;
    private final IGoodsCostDetailService goodsCostDetailService;


	/**
    * 分页查询
    * @param page 分页对象
    * @param goodsSpu spu商品
    * @return
    */
	@ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermi('mall:goodsspu:index')")
    public AjaxResult getGoodsSpuPage(Page page, GoodsSpu goodsSpu) {
		Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
		if(!SecurityUtils.isAdmin(userId)){//非管理员 添加对应旅行社id
			goodsSpu.setTravelId(SecurityUtils.getLoginUser().getUser().getTravelId());
		}
		return AjaxResult.success(goodsSpuService.page1(page, goodsSpu));
    }

	/**
	 * list查询
	 * @param goodsSpu
	 * @return
	 */
	@ApiOperation(value = "list查询")
	@GetMapping("/list")
	public List<GoodsSpu> getList(GoodsSpu goodsSpu) {
		return goodsSpuService.list(Wrappers.query(goodsSpu).lambda()
						.select(GoodsSpu::getId,
								GoodsSpu::getName)
				);
	}

	/**
	 * 查询数量
	 * @param goodsSpu
	 * @return
	 */
	@ApiOperation(value = "查询数量")
	@GetMapping("/count")
	public AjaxResult getCount(GoodsSpu goodsSpu) {
		Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
		if(!SecurityUtils.isAdmin(userId)){//非管理员 添加对应旅行社id
			goodsSpu.setTravelId(SecurityUtils.getLoginUser().getUser().getTravelId());
		}
		return AjaxResult.success(goodsSpuService.count(Wrappers.query(goodsSpu)));
	}
//
//    /**
//    * 通过id查询spu商品
//    * @param id
//    * @return AjaxResult
//    */
//	@ApiOperation(value = "通过id查询spu商品")
//    @GetMapping("/{id}")
//    @PreAuthorize("@ss.hasPermi('mall:goodsspu:get')")
//    public AjaxResult getById(@PathVariable("id") String id){
//        return AjaxResult.success(goodsSpuService.getById1(id));
//    }
//
//    /**
//    * 新增spu商品
//    * @param goodsSpu spu商品
//    * @return AjaxResult
//    */
//	@ApiOperation(value = "新增spu商品")
//    @PostMapping
//    @PreAuthorize("@ss.hasPermi('mall:goodsspu:add')")
//    public AjaxResult save(@RequestBody GoodsSpu goodsSpu){
//        return AjaxResult.success(goodsSpuService.save1(goodsSpu));
//    }
//
//    /**
//    * 修改spu商品
//    * @param goodsSpu spu商品
//    * @return AjaxResult
//    */
//	@ApiOperation(value = "修改spu商品")
//    @PutMapping
//    @PreAuthorize("@ss.hasPermi('mall:goodsspu:edit')")
//    public AjaxResult updateById(@RequestBody GoodsSpu goodsSpu){
//        return AjaxResult.success(goodsSpuService.updateById1(goodsSpu));
//    }

	/**
	 * 商品上下架操作
	 * @param shelf
	 * @param ids
	 * @return AjaxResult
	 */
	@ApiOperation(value = "商品上下架操作")
	@PutMapping("/shelf")
//	@PreAuthorize("@ss.hasPermi('mall:goodsspu:edit')")
	public AjaxResult updateById(@RequestParam(value = "shelf") String shelf, @RequestParam(value = "ids") String ids){
		GoodsSpu goodsSpu = new GoodsSpu();
		goodsSpu.setShelf(shelf);
		return AjaxResult.success(goodsSpuService.update(goodsSpu,Wrappers.<GoodsSpu>lambdaQuery()
				.in(GoodsSpu::getId, Convert.toList(ids))));
	}

	/**
	 * 商品是否展示操作
	 * @param isShow
	 * @param ids
	 * @return AjaxResult
	 */
	@ApiOperation(value = "商品是否展示操作")
	@PutMapping("/isShow")
//	@PreAuthorize("@ss.hasPermi('mall:goodsspu:edit')")
	public AjaxResult updateShowById(@RequestParam(value = "isShow") String isShow, @RequestParam(value = "ids") String ids){
		GoodsSpu goodsSpu = new GoodsSpu();
		goodsSpu.setIsShow(isShow);
		return AjaxResult.success(goodsSpuService.update(goodsSpu,Wrappers.<GoodsSpu>lambdaQuery()
				.in(GoodsSpu::getId, Convert.toList(ids))));
	}
//
//    /**
//    * 通过id删除spu商品
//    * @param id
//    * @return AjaxResult
//    */
//	@ApiOperation(value = "通过id删除spu商品")
//    @DeleteMapping("/{id}")
//    @PreAuthorize("@ss.hasPermi('mall:goodsspu:del')")
//    public AjaxResult removeById(@PathVariable String id){
//        return AjaxResult.success(goodsSpuService.removeById(id));
//    }

	/****新方法***/

	/**
	 * 查询商品列表
	 */
//	@PreAuthorize("@ss.hasPermi('GoodsSpu:GoodsSpu:list')")
	@GetMapping("/listnew")
	public TableDataInfo list(GoodsSpu goodsSpu)
	{
		startPage();
		List<GoodsSpu> list = goodsSpuService.selectGoodsSpuList(goodsSpu);
		return getDataTable(list);
	}

	/**
	 * 导出商品列表
	 */
//	@PreAuthorize("@ss.hasPermi('GoodsSpu:GoodsSpu:export')")
	@Log(title = "商品", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult export(GoodsSpu goodsSpu)
	{
		List<GoodsSpu> list = goodsSpuService.selectGoodsSpuList(goodsSpu);
		ExcelUtil<GoodsSpu> util = new ExcelUtil<GoodsSpu>(GoodsSpu.class);
		return util.exportExcel(list, "GoodsSpu");
	}

  /** 获取商品详细信息 */
  //	@PreAuthorize("@ss.hasPermi('GoodsSpu:GoodsSpu:query')")
  @GetMapping(value = "{id}")
  public AjaxResult getInfo(@PathVariable("id") String id) {
    return AjaxResult.success(goodsSpuService.selectGoodsSpuById(id));
  }

  /**
   * 新增商品
   *
   * @param goodsSpuDto
   * @return
   */
  @Log(title = "商品", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody GoodsSpuDTO goodsSpuDto) {
    if (StrUtil.isNotBlank(goodsSpuDto.getId())) {
      // 更新商品
      GoodsSpu goodsSpu = BeanUtil.copyProperties(goodsSpuDto, GoodsSpu.class, "pickupPoints");
      goodsSpu.setStartingPoint(Joiner.on(",").skipNulls().join(goodsSpuDto.getStartingPoint()));
      goodsSpu.setDest(Joiner.on(",").skipNulls().join(goodsSpuDto.getDest()));
      int result = goodsSpuService.updateGoodsSpu(goodsSpu);
      if (result > 0) {
        // 保存费用详情
        if (ObjectUtil.isNotEmpty(goodsSpuDto.getCostDetails())) {
          goodsCostDetailService.saveOrUpdateCostDetails(goodsSpu.getId(), goodsSpuDto.getCostDetails());
        }
        // 返回更新后的商品信息
        GoodsSpuDTO updatedGoodsSpu = goodsSpuService.selectGoodsSpuById(goodsSpu.getId());
        return AjaxResult.success("更新成功", updatedGoodsSpu);
      } else {
        return AjaxResult.error("更新失败");
      }
    }
    
    // 新增商品
    if (StringUtils.isEmpty(goodsSpuDto.getIsShow())) {
      goodsSpuDto.setIsShow("0"); // 默认不展示
    }
    GoodsSpu goodsSpu = BeanUtil.copyProperties(goodsSpuDto, GoodsSpu.class, "pickupPoints");
    goodsSpu.setStartingPoint(Joiner.on(",").skipNulls().join(goodsSpuDto.getStartingPoint()));
    goodsSpu.setDest(Joiner.on(",").skipNulls().join(goodsSpuDto.getDest()));
    
    int result = goodsSpuService.insertGoodsSpu(goodsSpu);
    if (result > 0) {
      // 保存费用详情
      if (ObjectUtil.isNotEmpty(goodsSpuDto.getCostDetails())) {
        goodsCostDetailService.saveOrUpdateCostDetails(goodsSpu.getId(), goodsSpuDto.getCostDetails());
      }
      // 返回新增后的商品信息，包含生成的ID
      GoodsSpuDTO newGoodsSpu = goodsSpuService.selectGoodsSpuById(goodsSpu.getId());
      return AjaxResult.success("新增成功", newGoodsSpu);
    } else {
      return AjaxResult.error("新增失败");
    }
  }

  /** 修改商品 */
  @Log(title = "商品", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody GoodsSpuDTO goodsSpuDto) {
    GoodsSpu goodsSpu = BeanUtil.copyProperties(goodsSpuDto, GoodsSpu.class, "pickupPoints");
    goodsSpu.setStartingPoint(Joiner.on(",").skipNulls().join(goodsSpuDto.getStartingPoint()));
    goodsSpu.setDest(Joiner.on(",").skipNulls().join(goodsSpuDto.getDest()));
    int result = goodsSpuService.updateGoodsSpu(goodsSpu);
    if (result > 0) {
      // 保存费用详情
      if (ObjectUtil.isNotEmpty(goodsSpuDto.getCostDetails())) {
        goodsCostDetailService.saveOrUpdateCostDetails(goodsSpu.getId(), goodsSpuDto.getCostDetails());
      }
    }
    return toAjax(result);
  }

	/**
	 * 删除商品
	 */
	@Log(title = "商品", businessType = BusinessType.DELETE)
	@DeleteMapping("{ids}")
	public AjaxResult remove(@PathVariable String[] ids)
	{
		return toAjax(goodsSpuService.deleteGoodsSpuByIds(ids));
	}


	/**
	 * 切换商品上架/下架状态
	 * @param id 商品ID
	 * @return AjaxResult
	 */
	@ApiOperation(value = "切换商品上架/下架状态")
	@PutMapping("/{id}/shelf")
	@Log(title = "商品上架/下架", businessType = BusinessType.UPDATE)
	public AjaxResult toggleShelf(@PathVariable("id") String id) {
		try {
			int result = goodsSpuService.toggleShelfStatus(id);
			return result > 0 ? AjaxResult.success("操作成功") : AjaxResult.error("操作失败");
		} catch (Exception e) {
			log.error("切换商品上架状态失败", e);
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 切换商品显示/隐藏状态
	 * @param id 商品ID
	 * @return AjaxResult
	 */
	@ApiOperation(value = "切换商品显示/隐藏状态")
	@PutMapping("/{id}/show")
	@Log(title = "商品显示/隐藏", businessType = BusinessType.UPDATE)
	public AjaxResult toggleShow(@PathVariable("id") String id) {
		try {
			int result = goodsSpuService.toggleShowStatus(id);
			return result > 0 ? AjaxResult.success("操作成功") : AjaxResult.error("操作失败");
		} catch (Exception e) {
			log.error("切换商品显示状态失败", e);
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 获取小程序码
	 * @return
	 */
	@GetMapping(value = "getRQCode/{id}")
	public AjaxResult getRQCode(@PathVariable("id") String id){

		String rqCode = goodsSpuService.getRQCode("pages/goods/goods-detail/index?id="+id, null);
		int i = goodsSpuService.updateGoodsSpuRQCode(id, rqCode);
		return AjaxResult.success("成功",rqCode);
	}


}
