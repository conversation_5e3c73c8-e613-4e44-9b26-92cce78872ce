/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.entity.ArticleEvaluate;
import com.joolun.mall.service.IArticleEvaluateService;
import com.joolun.weixin.constant.MyReturnCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 身份信息liao
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/articleevaluate")
@Api(value = "cert", tags = "文章评论接口API")
public class ArticleEvaluateApi {

	private final IArticleEvaluateService articleEvaluateService;




	/**
	* 分页查询
	* @param page 分页对象
	* @param articleEvaluate 评论信息
	* @return
	*/
	@ApiOperation(value = "查询文章评论")
    @GetMapping("/page")
    public AjaxResult getArticleEvaluatePage(Page page, ArticleEvaluate articleEvaluate) {
		return AjaxResult.success(articleEvaluateService.selectPage1(page, articleEvaluate));
    }

    /**
    * 通过id查询评论详情
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询评论详情")
    @GetMapping("/{id}")
    public AjaxResult getById(HttpServletRequest request, @PathVariable("id") String id){
		ArticleEvaluate articleEvaluate = articleEvaluateService.selectArticleEvaluateById(id);
		if(articleEvaluate == null){
			return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), MyReturnCode.ERR_80004.getMsg());
		}
        return AjaxResult.success(articleEvaluate);
    }



	/**
	 * 新增评论信息
	 * @param articleEvaluate 评论信息
	 * @return R
	 */
	@ApiOperation(value = "新增评论信息")
	@PostMapping
	public AjaxResult save(@RequestBody ArticleEvaluate articleEvaluate){



		int i = articleEvaluateService.insertArticleEvaluate(articleEvaluate);
		if(i<=0){
			return AjaxResult.error(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
		}
		return AjaxResult.success(true);
	}

	/**
	 * 通过id删除评论信息
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "通过id删除评论")
	@DeleteMapping("/{id}")
	public AjaxResult removeById(@PathVariable String id){
		ArticleEvaluate articleEvaluate = articleEvaluateService.selectArticleEvaluateById(id);
		if(articleEvaluate == null){
			return AjaxResult.error(MyReturnCode.ERR_10003.getCode(), MyReturnCode.ERR_10003.getMsg());
		}
		return AjaxResult.success(articleEvaluateService.deleteArticleEvaluateById(id));
	}


}
