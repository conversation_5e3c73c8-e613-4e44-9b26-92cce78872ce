/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.weixin.entity.WxUserLevel;
import com.joolun.weixin.service.IWxUserLevelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 用户级别信息
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/userLv")
@Api(value = "userLv", tags = "用户级别接口API")
public class WxUserLevelApi {

    private final IWxUserLevelService wxUserLevelService;

	/**
	* 分页查询
	* @param page 分页对象
	* @param wxUserLevel 级别信息
	* @return
	*/
	@ApiOperation(value = "用户级别信息查询")
    @GetMapping("/page")
    public AjaxResult getGoodsSpuPage(Page page, WxUserLevel wxUserLevel) {
		return AjaxResult.success(wxUserLevelService.selectWxUserLevelList(wxUserLevel));
    }

}
