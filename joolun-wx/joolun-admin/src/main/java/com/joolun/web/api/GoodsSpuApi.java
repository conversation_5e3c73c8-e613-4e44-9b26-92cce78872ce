package com.joolun.web.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.framework.utils.ThirdSessionHolder;
import com.joolun.framework.utils.UserContextUtils;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.dto.GoodsSpuDTO;
import com.joolun.mall.entity.GoodsSpu;
import com.joolun.mall.service.GoodsSpuService;
import com.joolun.weixin.constant.MyReturnCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.utils.StringUtils;

/**
 * 商品api
 *
 * <AUTHOR>
 * @date 2019-08-12 16:25:10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/goodsspu")
@Api(value = "goodsspu", tags = "商品接口API")
public class GoodsSpuApi {

  private final GoodsSpuService goodsSpuService;

  /**
   * 分页查询
   *
   * @param page 分页对象
   * @param goodsSpu spu商品
   * @param keyword 搜索关键词
   * @return
   */
  @ApiOperation(value = "分页查询")
  @GetMapping("/page")
  public AjaxResult getGoodsSpuPage(Page page, GoodsSpu goodsSpu, 
                                    @RequestParam(required = false) String keyword) {
    goodsSpu.setShelf(CommonConstants.YES);
    goodsSpu.setIsShow(CommonConstants.YES);
    
    // 处理搜索关键词 - 使用name字段临时传递关键词到Service层
    if (StringUtils.isNotEmpty(keyword) && StringUtils.isNotBlank(keyword.trim())) {
        goodsSpu.setName(keyword.trim());
        log.info("设置搜索关键词: {}", keyword.trim());
    } else {
        log.info("搜索关键词为空，不设置搜索条件");
    }
    
    return AjaxResult.success(goodsSpuService.selectByPageForApp(page, goodsSpu));
  }

  /**
   * 我的收藏分页查询
   *
   * @param page 分页对象
   * @return
   */
  @ApiOperation(value = "分页查询-我的收藏")
  @GetMapping("/collectPage")
  public AjaxResult getGoodsSpuPage(Page page) {
    return AjaxResult.success(
        goodsSpuService.selectCollectPageByUserIdForApp(page, UserContextUtils.getCurrentUserId()));
  }

  /**
   * 我的浏览记录分页查询
   *
   * @param page 分页对象
   * @return
   */
  @ApiOperation(value = "分页查询-我的浏览")
  @GetMapping("/browsePage")
  public AjaxResult getBrowsePage(Page page) {
    return AjaxResult.success(
        goodsSpuService.selectBrowsePageByUserIdForApp(page, UserContextUtils.getCurrentUserId()));
  }

  /**
   * 通过id查询spu商品
   *
   * @param id
   * @return R
   */
  @ApiOperation(value = "通过id查询spu商品")
  @GetMapping("/{id}")
  public AjaxResult getById(@PathVariable("id") String id) {
    GoodsSpuDTO goodsSpu = goodsSpuService.selectGoodsSpuById(id);
    if (goodsSpu == null) {
      return AjaxResult.error(MyReturnCode.ERR_80004.getCode(), MyReturnCode.ERR_80004.getMsg());
    }
    return AjaxResult.success(goodsSpu);
  }
}
