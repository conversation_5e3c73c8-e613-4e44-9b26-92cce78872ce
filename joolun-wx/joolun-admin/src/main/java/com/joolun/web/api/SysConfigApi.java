/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.core.domain.entity.SysDictData;
import com.joolun.common.utils.StringUtils;
import com.joolun.system.service.ISysConfigService;
import com.joolun.system.service.ISysDictDataService;
import com.joolun.system.service.ISysDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统配置接口
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/sysconfig")
@Api(value = "sysconfig", tags = "系统配置接口API")
public class SysConfigApi {

	private final ISysConfigService sysConfigService;



	/**
	 * 根据参数key查询内容
	 */
	@ApiOperation(value = "根据参数key查询内容")
	@GetMapping(value = "/getkey/{key}")
	public AjaxResult getkey(@PathVariable String key)
	{

		String s = sysConfigService.selectConfigByKey(key);
		return AjaxResult.success("查询成功",s);
	}
}
