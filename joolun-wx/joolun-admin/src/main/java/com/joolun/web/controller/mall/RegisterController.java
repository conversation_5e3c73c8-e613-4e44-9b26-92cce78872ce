package com.joolun.web.controller.mall;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.constant.TripConstants;
import com.joolun.mall.entity.TravelAgency;
import com.joolun.mall.service.ITravelAgencyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/1/29-22:41
 * @Version 1.0.0
 */
@RestController
public class RegisterController {

    @Autowired
    private ITravelAgencyService travelAgencyService;


    /**
     * 新增旅行社信息
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody TravelAgency travelAgency) {
        travelAgency.setStatus(TripConstants.STATUS_0);
        int i = travelAgencyService.insertTravelAgency(travelAgency);
        if (i>0){
            return AjaxResult.success("新增成功","");
        }
        return AjaxResult.error("新增成功","");
    }


}
