/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.mall.entity.ArticleCollect;
import com.joolun.mall.service.IArticleCollectService;
import com.joolun.framework.utils.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 身份信息liao
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/articlecollect")
@Api(value = "articlecollect", tags = "文章收藏接口API")
public class ArticleCollectApi {

	private final IArticleCollectService articleCollectService;



	/**
	 * 新增文章收藏或点赞
	 * @return R
	 */
	@ApiOperation(value = "新增文章收藏")
	@PostMapping("/addCollectOrLikes")
	public AjaxResult addCollectOrLikes(@RequestBody ArticleCollect articleCollect){
		String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
		articleCollect.setId(IdUtils.simpleUUID());
		articleCollect.setCreateBy(wxUserId);
		articleCollect.setCreateTime(DateUtils.getNowDate());
		articleCollect.setUserId(wxUserId);
		int i = articleCollectService.insertArticleCollect(articleCollect);
		if(i>0){
			return AjaxResult.success("添加成功",true);
		}else{
			return AjaxResult.success("添加失败",false);
		}
	}


	/**
	 * 检查是否收藏文章
	 * @return R
	 */
	@ApiOperation(value = "检查是否收藏文章")
	@PostMapping("/check")
	public AjaxResult checkCoolect(@RequestBody ArticleCollect articleCollect){
		String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
		articleCollect.setUserId(wxUserId);
		List<ArticleCollect> articleCollects = articleCollectService.selectArticleCollectList(articleCollect);
		if(articleCollects.size()>0){
			return AjaxResult.success("已存在",true);
		}else{
			return AjaxResult.success("不存在",false);
		}
	}


	/**
	 * 删除文章收藏
	 * @return R
	 */
	@ApiOperation(value = "删除文章收藏")
	@PostMapping("/delInfo")
	public AjaxResult removeById(@RequestBody ArticleCollect articleCollect){
		String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
		articleCollect.setUserId(wxUserId);
		List<ArticleCollect> articleCollects = articleCollectService.selectArticleCollectList(articleCollect);
		if (articleCollects.size()>0){
			int i = articleCollectService.deleteArticleCollectById(articleCollects.get(0).getId());
			if(i>0){
				return AjaxResult.success("删除数据成功",true);
			}else{
				return AjaxResult.success("删除数据失败",false);
			}
		}else{
			return AjaxResult.success("删除数据失败",false);
		}
	}



	/***
	 * 根据用户查询用户的收藏与获赞信息
	 *
	 * @return
	 */
	@ApiOperation(value = "根据用户查询用户的收藏与获赞信息")
	@GetMapping("/likesCount/{userId}")
	public AjaxResult getFollowCount(HttpServletRequest request, @PathVariable("userId") String userId) {
		ArticleCollect articleCollect = new ArticleCollect();
		int i = articleCollectService.selectArticleLikesCount(userId);
		return AjaxResult.success(i);
	}



}
