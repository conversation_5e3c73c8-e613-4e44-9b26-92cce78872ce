/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 */
package com.joolun.web.api;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.config.CommonConstants;
import com.joolun.trip.domain.TyRouteCategory;
import com.joolun.trip.service.ITyRouteCategoryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 线路分类
 *
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/weixin/api/ma/routecategory")
public class RouteCategoryApi {

    private final ITyRouteCategoryService tyRouteCategoryService;

    /**
    * 返回树形集合
    */
    @GetMapping("/tree")
    public AjaxResult goodsCategoryTree(TyRouteCategory tyRouteCategory) {
        tyRouteCategory.setEnable(CommonConstants.YES);
		return AjaxResult.success(tyRouteCategoryService.selectTree(tyRouteCategory));
    }

}
