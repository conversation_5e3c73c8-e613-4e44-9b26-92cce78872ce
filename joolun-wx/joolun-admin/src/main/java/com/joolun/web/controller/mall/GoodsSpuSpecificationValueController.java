package com.joolun.web.controller.mall;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.entity.GoodsSpuSpecificationValue;
import com.joolun.mall.service.IGoodsSpuSpecificationValueService;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.core.page.TableDataInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * SPU与规格值的关联Controller
 *
 * <AUTHOR>
 * @date 2025-04-12
 */
@RestController
@RequestMapping("/mall/spuAttrValues")
public class GoodsSpuSpecificationValueController extends BaseController {
  @Autowired private IGoodsSpuSpecificationValueService goodsSpuSpecificationValueService;

  /** 查询SPU与规格值的关联列表 */
  @PreAuthorize("@ss.hasPermi('system:value:list')")
  @GetMapping("/list")
  public TableDataInfo list(GoodsSpuSpecificationValue goodsSpuSpecificationValue) {
    startPage();
    List<GoodsSpuSpecificationValue> list =
        goodsSpuSpecificationValueService.selectGoodsSpuSpecificationValueList(
            goodsSpuSpecificationValue);
    return getDataTable(list);
  }

  /**
   * 分页查询
   *
   * @param page 分页对象
   * @param goodsSpuSpecificationValue
   * @return
   */
  @ApiOperation(value = "分页查询")
  @GetMapping("/page")
  @PreAuthorize("@ss.hasPermi('mall:useraddress:index')")
  public AjaxResult page(Page page, GoodsSpuSpecificationValue goodsSpuSpecificationValue) {
    return AjaxResult.success(
        goodsSpuSpecificationValueService.page(page, Wrappers.query(goodsSpuSpecificationValue)));
  }

  /** 导出SPU与规格值的关联列表 */
  @PreAuthorize("@ss.hasPermi('system:value:export')")
  @Log(title = "SPU与规格值的关联", businessType = BusinessType.EXPORT)
  @GetMapping("/export")
  public AjaxResult export(GoodsSpuSpecificationValue goodsSpuSpecificationValue) {
    List<GoodsSpuSpecificationValue> list =
        goodsSpuSpecificationValueService.selectGoodsSpuSpecificationValueList(
            goodsSpuSpecificationValue);
    ExcelUtil<GoodsSpuSpecificationValue> util =
        new ExcelUtil<GoodsSpuSpecificationValue>(GoodsSpuSpecificationValue.class);
    return util.exportExcel(list, "value");
  }

  /** 获取SPU与规格值的关联详细信息 */
  @PreAuthorize("@ss.hasPermi('system:value:query')")
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id) {
    return AjaxResult.success(
        goodsSpuSpecificationValueService.selectGoodsSpuSpecificationValueById(id));
  }

  /** 新增SPU与规格值的关联 */
  @PreAuthorize("@ss.hasPermi('system:value:add')")
  @Log(title = "SPU与规格值的关联", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody GoodsSpuSpecificationValue goodsSpuSpecificationValue) {
    return toAjax(
        goodsSpuSpecificationValueService.insertGoodsSpuSpecificationValue(
            goodsSpuSpecificationValue));
  }

  /** 修改SPU与规格值的关联 */
  @PreAuthorize("@ss.hasPermi('system:value:edit')")
  @Log(title = "SPU与规格值的关联", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody GoodsSpuSpecificationValue goodsSpuSpecificationValue) {
    return toAjax(
        goodsSpuSpecificationValueService.updateGoodsSpuSpecificationValue(
            goodsSpuSpecificationValue));
  }

  /** 删除SPU与规格值的关联 */
  @PreAuthorize("@ss.hasPermi('system:value:remove')")
  @Log(title = "SPU与规格值的关联", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Long[] ids) {
    return toAjax(goodsSpuSpecificationValueService.deleteGoodsSpuSpecificationValueByIds(ids));
  }
}
