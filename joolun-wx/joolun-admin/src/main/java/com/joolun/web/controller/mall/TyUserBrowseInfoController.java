package com.joolun.web.controller.mall;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.joolun.common.annotation.Log;
import com.joolun.common.core.controller.BaseController;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.enums.BusinessType;
import com.joolun.mall.entity.TyUserBrowseInfo;
import com.joolun.mall.service.ITyUserBrowseInfoService;
import com.joolun.common.utils.poi.ExcelUtil;
import com.joolun.common.core.page.TableDataInfo;

/**
 * 用户浏览记录Controller
 *
 * <AUTHOR>
 * @date 2023-12-09
 */
@RestController
@RequestMapping("/mall/userBrowse")
public class TyUserBrowseInfoController extends BaseController {
  @Autowired private ITyUserBrowseInfoService tyUserBrowseInfoService;

  /** 查询用户浏览记录列表 */
  @PreAuthorize("@ss.hasPermi('mall:userBrowse:list')")
  @GetMapping("/list")
  public TableDataInfo list(TyUserBrowseInfo tyUserBrowseInfo) {
    startPage();
    List<TyUserBrowseInfo> list =
        tyUserBrowseInfoService.selectTyUserBrowseInfoList(tyUserBrowseInfo);
    return getDataTable(list);
  }

  /** 导出用户浏览记录列表 */
  @PreAuthorize("@ss.hasPermi('mall:userBrowse:export')")
  @Log(title = "用户浏览记录", businessType = BusinessType.EXPORT)
  @GetMapping("/export")
  public AjaxResult export(TyUserBrowseInfo tyUserBrowseInfo) {
    List<TyUserBrowseInfo> list =
        tyUserBrowseInfoService.selectTyUserBrowseInfoList(tyUserBrowseInfo);
    ExcelUtil<TyUserBrowseInfo> util = new ExcelUtil<TyUserBrowseInfo>(TyUserBrowseInfo.class);
    return util.exportExcel(list, "userBrowse");
  }

  /** 获取用户浏览记录详细信息 */
  @PreAuthorize("@ss.hasPermi('mall:userBrowse:query')")
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") String id) {
    return AjaxResult.success(tyUserBrowseInfoService.selectTyUserBrowseInfoById(id));
  }

  /** 新增用户浏览记录 */
  @PreAuthorize("@ss.hasPermi('mall:userBrowse:add')")
  @Log(title = "用户浏览记录", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody TyUserBrowseInfo tyUserBrowseInfo) {
    return toAjax(tyUserBrowseInfoService.insertTyUserBrowseInfo(tyUserBrowseInfo));
  }

  /** 修改用户浏览记录 */
  @PreAuthorize("@ss.hasPermi('mall:userBrowse:edit')")
  @Log(title = "用户浏览记录", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody TyUserBrowseInfo tyUserBrowseInfo) {
    return toAjax(tyUserBrowseInfoService.updateTyUserBrowseInfo(tyUserBrowseInfo));
  }

  /** 删除用户浏览记录 */
  @PreAuthorize("@ss.hasPermi('mall:userBrowse:remove')")
  @Log(title = "用户浏览记录", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable String[] ids) {
    return toAjax(tyUserBrowseInfoService.deleteTyUserBrowseInfoByIds(ids));
  }
}
