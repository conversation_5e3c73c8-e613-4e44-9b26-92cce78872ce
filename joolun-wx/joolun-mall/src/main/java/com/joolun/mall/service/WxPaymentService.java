package com.joolun.mall.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.joolun.common.constant.HttpStatus;
import com.joolun.common.core.domain.AjaxResult;
import com.joolun.common.exception.CustomException;
import com.joolun.common.utils.DateUtils;
import com.joolun.framework.utils.ThirdSessionHolder;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.config.MallConfigProperties;
import com.joolun.mall.config.PaymentConfig;
import com.joolun.mall.entity.OrderInfo;
import com.joolun.mall.entity.OrderItem;
import com.joolun.mall.entity.query.OrderItemQuery;
import com.joolun.mall.enums.OrderInfoEnum;
import com.joolun.weixin.config.WxPayConfiguration;
import com.joolun.weixin.constant.MyReturnCode;
import com.joolun.weixin.utils.WxMaUtil;
import java.math.BigDecimal;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 微信支付服务 整合模拟支付和真实支付的统一服务 根据环境配置自动切换支付方式
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
@Service
public class WxPaymentService {

  @Value("${spring.profiles.active:dev}")
  private String activeProfile;

  @Autowired private MallConfigProperties mallConfigProperties;

  @Autowired private PaymentConfig paymentConfig;

  @Autowired private OrderInfoService orderInfoService;

  @Autowired(required = false)
  private MockPaymentService mockPaymentService;

  @Autowired private OrderItemService orderItemService;

  /**
   * 统一下单接口 根据环境配置选择使用模拟支付或真实支付
   *
   * @param request HTTP请求
   * @param orderInfo 订单信息
   * @return 支付结果
   */
  public AjaxResult unifiedOrder(HttpServletRequest request, OrderInfo orderInfo) {
    log.info(
        "[PAYMENT] 统一下单开始 - 订单号: {}, 金额: {}, 环境: {}",
        orderInfo.getOrderNo(),
        orderInfo.getPaymentPrice(),
        activeProfile);

    try {
      // 卫语句：订单基本验证
      AjaxResult validationResult = validateOrderForPayment(orderInfo);
      if (validationResult.get(AjaxResult.CODE_TAG) != null
          && !Integer.valueOf(200).equals(validationResult.get(AjaxResult.CODE_TAG))) {
        return validationResult;
      }

      // 0元订单直接处理
      if (orderInfo.getPaymentPrice().compareTo(BigDecimal.ZERO) == 0) {
        log.info("[PAYMENT] 0元订单直接处理 - 订单号: {}", orderInfo.getOrderNo());
        orderInfoService.processZeroPaymentOrder(orderInfo);
        return AjaxResult.success();
      }

      // 根据环境和配置选择支付方式
      if (shouldUseMockPayment()) {
        return handleMockPayment(orderInfo);
      } else {
        return handleRealPayment(request, orderInfo);
      }

    } catch (Exception e) {
      log.error("[PAYMENT] 统一下单异常 - 订单号: {}, 错误: {}", orderInfo.getOrderNo(), e.getMessage(), e);
      return AjaxResult.error("支付服务异常，请稍后重试");
    }
  }

  /**
   * 支付回调处理 统一处理微信支付的回调通知
   *
   * @param xmlData 回调数据
   * @return 回调响应
   */
  public String handlePaymentNotify(String xmlData) {
    log.info("[PAYMENT] 支付回调开始 - 数据长度: {}", xmlData != null ? xmlData.length() : 0);

    try {
      // 卫语句：回调数据验证
      if (xmlData == null || xmlData.trim().isEmpty()) {
        log.error("[PAYMENT] 支付回调数据为空");
        return createFailResponse("回调数据为空");
      }

      // 解析回调数据
      WxPayService wxPayService = WxPayConfiguration.getPayService();
      WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);

      log.info(
          "[PAYMENT] 支付回调解析完成 - 订单号: {}, 交易号: {}, 金额: {}",
          notifyResult.getOutTradeNo(),
          notifyResult.getTransactionId(),
          notifyResult.getTotalFee());

      // 查找对应订单
      OrderInfo orderInfo =
          orderInfoService.getOne(
              com.baomidou.mybatisplus.core.toolkit.Wrappers.<OrderInfo>lambdaQuery()
                  .eq(OrderInfo::getOrderNo, notifyResult.getOutTradeNo()));
      if (orderInfo == null) {
        log.error("[PAYMENT] 订单不存在 - 订单号: {}", notifyResult.getOutTradeNo());
        return createFailResponse("无此订单");
      }

      // 验证支付金额
      int orderAmountInFen = orderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue();
      if (orderAmountInFen != notifyResult.getTotalFee()) {
        log.error(
            "[PAYMENT] 支付金额不匹配 - 订单金额: {}, 回调金额: {}", orderAmountInFen, notifyResult.getTotalFee());
        return createFailResponse("付款金额与订单金额不等");
      }

      // 处理支付成功逻辑
      orderInfo.setTransactionId(notifyResult.getTransactionId());
      orderInfo.setPaymentTime(DateUtils.getNowDate());
      orderInfoService.notifyOrder(orderInfo);

      log.info("[PAYMENT] 支付回调处理完成 - 订单号: {}", notifyResult.getOutTradeNo());
      return createSuccessResponse("成功");

    } catch (WxPayException e) {
      log.error("[PAYMENT] 支付回调解析异常: {}", e.getMessage(), e);
      return createFailResponse("回调数据解析失败");
    } catch (Exception e) {
      log.error("[PAYMENT] 支付回调处理异常: {}", e.getMessage(), e);
      return createFailResponse("回调处理失败");
    }
  }

  /**
   * 退款处理 根据环境配置选择使用模拟退款或真实退款
   *
   * @param orderItemQuery 订单项
   * @return 退款结果
   */
  @Transactional(rollbackFor = Exception.class)
  public AjaxResult processRefund(OrderItemQuery orderItemQuery) {
    log.info("[PAYMENT] 退款处理开始 - 订单项ID: {}, 环境: {}", orderItemQuery.getId(), activeProfile);

    try {
      if (ObjectUtil.isNull(orderItemQuery.getId())) {
        throw new CustomException("请传递订单详情ID");
      }

      OrderItem orderItem = orderItemService.getById(orderItemQuery.getId());
      log.info("[PAYMENT] 订单项信息 - ID: {}，订单信息{}", orderItemQuery.getId(), orderItem);

      if (ObjectUtil.isNull(orderItem)) {
        log.warn("[PAYMENT] 未找到订单项 - ID: {}", orderItemQuery.getId());
        return AjaxResult.error("未找到订单项");
      }

      if (OrderInfoEnum.STATUS_0.getValue().equals(orderItem.getStatus())) {
        log.warn("[PAYMENT] 订单项属于待付款，不符合退款条件 - ID: {}", orderItemQuery.getId());
        return AjaxResult.error(" 订单项属于待付款状态，不符合退款条件");
      }

      // 退款条件验证, 没有退款，不是待使用或者已完成 就可以发起退款申请
      boolean canRefund =
          CommonConstants.NO.equals(orderItem.getIsRefund())
              && (OrderInfoEnum.STATUS_2.getValue().equals(orderItem.getStatus())
                  || OrderInfoEnum.STATUS_3.getValue().equals(orderItem.getStatus()));

      if (!canRefund) {
        log.warn("[PAYMENT] 订单项不符合退款条件 - ID: {}", orderItemQuery.getId());
        return AjaxResult.error("订单不符合退款条件");
      }

      // 根据环境选择退款方式
      AjaxResult result;
      if (shouldUseMockPayment()) {
        result = handleMockRefund(orderItem);
      } else {
        result = handleRealRefund(orderItem);
      }

      // 退款成功后，同步订单状态
      if (HttpStatus.SUCCESS == (Integer) result.get(AjaxResult.CODE_TAG)) {
        // 重新从数据库查询最新的 orderItem
        OrderItem latestOrderItem = orderItemService.getById(orderItem.getId());
        orderInfoService.syncOrderStatusWithOrderItem(latestOrderItem.getOrderId(), latestOrderItem.getStatus());
      }
      return result;
    } catch (Exception e) {
      log.error("[PAYMENT] 退款处理异常 - 订单项ID: {}, 错误: {}", orderItemQuery.getId(), e.getMessage(), e);
      return AjaxResult.error("退款服务异常，请稍后重试");
    }
  }

  /**
   * 退款回调处理 统一处理微信退款的回调通知
   *
   * @param xmlData 回调数据
   * @return 回调响应
   */
  public String handleRefundNotify(String xmlData) {
    log.info("[PAYMENT] 退款回调开始 - 数据长度: {}", xmlData != null ? xmlData.length() : 0);

    try {
      // 卫语句：回调数据验证
      if (xmlData == null || xmlData.trim().isEmpty()) {
        log.error("[PAYMENT] 退款回调数据为空");
        return createFailResponse("退款回调数据为空");
      }

      WxPayService wxPayService = WxPayConfiguration.getPayService();
      WxPayRefundNotifyResult notifyResult = wxPayService.parseRefundNotifyResult(xmlData);

      orderInfoService.notifyRefunds(notifyResult);

      log.info("[PAYMENT] 退款回调处理完成 - 退款单号: {}", notifyResult.getReqInfo().getOutRefundNo());
      return createSuccessResponse("成功");

    } catch (Exception e) {
      log.error("[PAYMENT] 退款回调处理异常: {}", e.getMessage(), e);
      return createFailResponse(e.getMessage());
    }
  }

  /** 验证订单是否可以支付 */
  private AjaxResult validateOrderForPayment(OrderInfo orderInfo) {
    // 卫语句：订单存在性检查
    if (orderInfo == null) {
      log.error("[PAYMENT] 订单不存在");
      return AjaxResult.error(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
    }

    // 卫语句：支付状态检查
    if (!CommonConstants.NO.equals(orderInfo.getIsPay())) {
      log.error("[PAYMENT] 订单已支付 - 订单号: {}", orderInfo.getOrderNo());
      return AjaxResult.error(MyReturnCode.ERR_70004.getCode(), MyReturnCode.ERR_70004.getMsg());
    }

    // 卫语句：订单状态检查
    if (orderInfo.getStatus() != null) {
      String status = orderInfo.getStatus();
      // 检查不允许支付的订单状态
      if (OrderInfoEnum.STATUS_2.getValue().equals(status)) {
        log.error("[PAYMENT] 订单已支付，无法重复支付 - 订单号: {}, 状态: {}", orderInfo.getOrderNo(), status);
        return AjaxResult.error("订单已支付，无法重复支付");
      }
      if (OrderInfoEnum.STATUS_4.getValue().equals(status)) {
        log.error("[PAYMENT] 订单退款中，无法支付 - 订单号: {}, 状态: {}", orderInfo.getOrderNo(), status);
        return AjaxResult.error("订单退款中，无法重复支付");
      }
      if (OrderInfoEnum.STATUS_5.getValue().equals(status)) {
        log.error("[PAYMENT] 订单已取消，无法支付 - 订单号: {}, 状态: {}", orderInfo.getOrderNo(), status);
        return AjaxResult.error("订单已取消，无法支付");
      }
      if (OrderInfoEnum.STATUS_3.getValue().equals(status)) {
        log.error("[PAYMENT] 订单已完成，无法支付 - 订单号: {}, 状态: {}", orderInfo.getOrderNo(), status);
        return AjaxResult.error("订单已完成，无法支付");
      }
      if (OrderInfoEnum.STATUS_6.getValue().equals(status)) {
        log.error("[PAYMENT] 订单已退款，无法支付 - 订单号: {}, 状态: {}", orderInfo.getOrderNo(), status);
        return AjaxResult.error("订单已退款，无法支付");
      }
      // 允许支付的状态：STATUS_0(待付款)等
    }

    // 卫语句：金额有效性检查
    if (orderInfo.getPaymentPrice() == null
        || orderInfo.getPaymentPrice().compareTo(BigDecimal.ZERO) < 0) {
      log.error(
          "[PAYMENT] 订单金额无效 - 订单号: {}, 金额: {}",
          orderInfo.getOrderNo(),
          orderInfo.getPaymentPrice());
      return AjaxResult.error("订单金额无效");
    }

    return AjaxResult.success();
  }

  /** 判断是否应该使用模拟支付 策略：生产环境强制禁用模拟支付，非生产环境根据配置决定 */
  private boolean shouldUseMockPayment() {
    // 生产环境强制禁用模拟支付（安全第一）
    if ("prod".equals(activeProfile)) {
      log.info("[PAYMENT] 生产环境，强制使用真实支付");
      return false;
    }

    // 非生产环境根据配置文件决定
    boolean mockEnabled = paymentConfig.isMockEnabled();
    log.info(
        "[PAYMENT] 环境: {}, 模拟支付配置: {}, 最终使用: {}",
        activeProfile,
        mockEnabled,
        mockEnabled ? "模拟支付" : "真实支付");

    return mockEnabled;
  }

  /** 处理模拟支付 */
  private AjaxResult handleMockPayment(OrderInfo orderInfo) {
    if (mockPaymentService == null) {
      log.error("[PAYMENT] 模拟支付服务未启用");
      return AjaxResult.error("模拟支付服务未配置");
    }

    if (!mockPaymentService.validatePaymentOrder(orderInfo)) {
      return AjaxResult.error("订单验证失败");
    }

    Map<String, Object> mockResult = mockPaymentService.mockUnifiedOrder(orderInfo);
    log.info("[PAYMENT] 模拟支付完成 - 订单号: {}", orderInfo.getOrderNo());

    return AjaxResult.success(mockResult);
  }

  /** 处理真实支付 */
  private AjaxResult handleRealPayment(HttpServletRequest request, OrderInfo orderInfo)
      throws WxPayException {
    String appId = WxMaUtil.getAppId(request);

    WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();
    wxPayUnifiedOrderRequest.setAppid(appId);

    String body = orderInfo.getName();
    body = body.length() > 40 ? body.substring(0, 39) : body;
    wxPayUnifiedOrderRequest.setBody(body);

    wxPayUnifiedOrderRequest.setOutTradeNo(orderInfo.getOrderNo());
    wxPayUnifiedOrderRequest.setTotalFee(
        orderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue());
    wxPayUnifiedOrderRequest.setTradeType("JSAPI");
    wxPayUnifiedOrderRequest.setNotifyUrl(
        mallConfigProperties.getNotifyHost() + "/weixin/api/ma/orderinfo/notify-order");
    wxPayUnifiedOrderRequest.setSpbillCreateIp("127.0.0.1");

    // 这里需要从request中获取openId，可能需要调整
    wxPayUnifiedOrderRequest.setOpenid(ThirdSessionHolder.getThirdSession().getOpenId());

    WxPayService wxPayService = WxPayConfiguration.getPayService();
    Object result = wxPayService.createOrder(wxPayUnifiedOrderRequest);

    log.info("[PAYMENT] 真实支付下单完成 - 订单号: {}", orderInfo.getOrderNo());
    return AjaxResult.success(JSONUtil.parse(result));
  }

  /** 处理模拟退款 修改：模拟退款也走相同的申请流程，而不是直接退款 */
  private AjaxResult handleMockRefund(OrderItem orderItem) {
    // 和真实退款一样，先发起退款申请
    orderInfoService.saveRefunds(orderItem);
    log.info("[PAYMENT] 模拟退款申请完成 - 订单项ID: {}, 状态已变更为退款中", orderItem.getId());

    return AjaxResult.success("退款申请已提交，请等待管理员审批");
  }

  /** 处理真实退款 */
  private AjaxResult handleRealRefund(OrderItem orderItem) {
    // 这里调用真实的退款逻辑
    orderInfoService.saveRefunds(orderItem);
    log.info("[PAYMENT] 真实退款申请完成 - 订单项ID: {}", orderItem.getId());

    return AjaxResult.success();
  }

  /** 创建成功响应 */
  private String createSuccessResponse(String message) {
    return String.format(
        "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[%s]]></return_msg></xml>",
        message);
  }

  /** 创建失败响应 */
  private String createFailResponse(String message) {
    return String.format(
        "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[%s]]></return_msg></xml>",
        message);
  }

  /**
   * 取消支付 当用户取消支付时调用此方法
   *
   * @param orderNo 订单号
   * @return 取消结果
   */
  public AjaxResult cancelPayment(String orderNo) {
    log.info("[PAYMENT] 开始取消支付 - 订单号: {}", orderNo);

    try {
      // 根据环境选择取消方式
      if (shouldUseMockPayment()) {
        return cancelMockPayment(orderNo);
      } else {
        return cancelRealPayment(orderNo);
      }
    } catch (Exception e) {
      log.error("[PAYMENT] 取消支付异常 - 订单号: {}, 错误: {}", orderNo, e.getMessage(), e);
      return AjaxResult.error("取消支付失败");
    }
  }

  /** 取消模拟支付 */
  private AjaxResult cancelMockPayment(String orderNo) {
    log.info(
        "[MOCK_PAYMENT] 尝试取消支付任务 - 订单号: {}, 当前任务数: {}",
        orderNo,
        mockPaymentService.getPendingPaymentTaskCount());

    log.info(
        "[MOCK_PAYMENT] 任务存在性检查 - 订单号: {}, 任务数量: {}",
        orderNo,
        mockPaymentService.getPendingPaymentTaskCount());

    if (mockPaymentService == null) {
      log.warn("[PAYMENT] 模拟支付服务未启用，无法取消支付任务");
      return AjaxResult.success("模拟支付服务未启用");
    }

    boolean cancelled = mockPaymentService.cancelPaymentTask(orderNo);
    if (cancelled) {
      log.info("[PAYMENT] 模拟支付取消成功 - 订单号: {}", orderNo);
      return AjaxResult.success("支付已取消");
    } else {
      log.warn("[PAYMENT] 模拟支付取消失败 - 订单号: {}", orderNo);
      return AjaxResult.error("支付取消失败，任务可能已执行");
    }
  }

  /** 取消真实支付 */
  private AjaxResult cancelRealPayment(String orderNo) {
    // 真实支付的取消逻辑
    // 微信支付一旦发起，无法在客户端取消，只能等待超时
    log.info("[PAYMENT] 真实支付无法取消，等待超时处理 - 订单号: {}", orderNo);
    return AjaxResult.success("支付请求已发起，请等待超时处理");
  }

  /** 获取当前支付环境信息 */
  public Map<String, Object> getPaymentEnvironmentInfo() {
    Map<String, Object> envInfo = new java.util.HashMap<>();
    envInfo.put("environment", activeProfile);
    envInfo.put("useMockPayment", shouldUseMockPayment());
    envInfo.put(
        "mockPaymentEnabled",
        mockPaymentService != null && mockPaymentService.isMockPaymentEnabled());

    if (mockPaymentService != null) {
      envInfo.put("mockConfig", mockPaymentService.getMockConfig());
      envInfo.put("pendingTaskCount", mockPaymentService.getPendingPaymentTaskCount());
    }

    return envInfo;
  }

  /**
   * 用户取消退款申请
   * 将订单项状态从"退款中"恢复到申请退款前的状态
   *
   * @param orderItemQuery 订单项查询参数
   * @return 取消结果
   */
  @Transactional(rollbackFor = Exception.class)
  public AjaxResult cancelRefundApplication(OrderItemQuery orderItemQuery) {
    log.info("[PAYMENT] 取消退款申请开始 - 订单项ID: {}", orderItemQuery.getId());

    try {
      if (ObjectUtil.isNull(orderItemQuery.getId())) {
        throw new CustomException("请传递订单详情ID");
      }

      // 调用业务层处理取消退款申请
      orderInfoService.cancelRefundApplication(orderItemQuery.getId());

      log.info("[PAYMENT] 取消退款申请成功 - 订单项ID: {}", orderItemQuery.getId());
      return AjaxResult.success("退款申请已取消");

    } catch (CustomException e) {
      log.error("[PAYMENT] 取消退款申请业务异常 - 订单项ID: {}, 错误: {}", orderItemQuery.getId(), e.getMessage());
      return AjaxResult.error(e.getMessage());
    } catch (Exception e) {
      log.error("[PAYMENT] 取消退款申请异常 - 订单项ID: {}, 错误: {}", orderItemQuery.getId(), e.getMessage(), e);
      return AjaxResult.error("取消退款申请失败，请稍后重试");
    }
  }
}
