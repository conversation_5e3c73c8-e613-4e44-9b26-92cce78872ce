/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.joolun.common.annotation.Excel;
import com.joolun.framework.config.typehandler.ArrayStringTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 商城订单详情
 *
 * <AUTHOR>
 * @date 2019-09-10 15:31:40
 */
@Data
@TableName("order_item")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "商城订单详情")
public class OrderItem extends Model<OrderItem> {
	private static final long serialVersionUID = 1L;

	/**
	 * PK
	 */
	@ApiModelProperty(value = "PK")
	@TableId(type = IdType.ASSIGN_ID)
	private String id;
	/**
	 * 逻辑删除标记（0：显示；1：隐藏）
	 */
	@ApiModelProperty(value = "逻辑删除标记")
	private String delFlag;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**
	 * 最后更新时间
	 */
	@ApiModelProperty(value = "最后更新时间")
	private Date updateTime;
	/**
	 * 订单编号
	 */
	@ApiModelProperty(value = "订单编号")
	private String orderId;
	/**
	 * 商品Id
	 */
	@ApiModelProperty(value = "商品Id")
	private String spuId;
	/**
	 * SKU ID (新增字段 - SKU迁移)
	 */
	@ApiModelProperty(value = "SKU ID")
	private String skuId;
	/**
	 * 商品名
	 */
	@ApiModelProperty(value = "商品名")
	private String spuName;
	/**
	 * SKU套餐名称 (新增字段 - SKU迁移)
	 */
	@ApiModelProperty(value = "SKU套餐名称")
	private String skuName;
	/**
	 * 图片
	 */
	@ApiModelProperty(value = "图片")
	private String picUrl;
	/**
	 * 商品数量
	 */
	@ApiModelProperty(value = "商品数量")
	private Integer quantity;
	/**
	 * 购买单价
	 */
	@ApiModelProperty(value = "购买单价")
	private BigDecimal salesPrice;
	/**
	 * 运费金额
	 */
	@ApiModelProperty(value = "运费金额")
	private BigDecimal freightPrice;
	/**
	 * 支付金额（购买单价*商品数量+运费金额）
	 */
	@ApiModelProperty(value = "支付金额（购买单价*商品数量+运费金额）")
	private BigDecimal paymentPrice;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 订单状态:0-待付款;1-待发货;2-待使用;3-已完成;4-退款中;5-已取消;6-已退款;7-同意退款;8-拒绝退款;
	 */
	@ApiModelProperty(value = "订单状态:0-待付款;1-待发货;2-待使用;3-已完成;4-退款中;5-已取消;6-已退款;7-同意退款;8-拒绝退款;")
	private String status;
	/**
	 * 是否退款0:否 1：是
	 */
	@ApiModelProperty(value = "是否退款0:否 1：是")
	private String isRefund;
	/**
	 * 同行人
	 */
	@ApiModelProperty(value = "同行人")
	@TableField(typeHandler = ArrayStringTypeHandler.class, jdbcType = JdbcType.VARCHAR)
	private String[] joinUser;

	/***
	 * 佣金
	 */
	private BigDecimal commission;

	/***
	 * 佣金受益人
	 */
	private String beneficiary;

	/***
	 * 详细线路id - 价格日历ID
	 */
	private String detailId;

	/**
	 * 成人数量
	 */
	@ApiModelProperty(value = "成人数量")
	private Integer adultQuantity;

	/**
	 * 老年人数量
	 */
	@ApiModelProperty(value = "老年人数量")
	private Integer elderlyQuantity;

	/** 大童数量 */
	@ApiModelProperty(value = "大童数量")
	private Integer olderChildQuantity;

	/** 小童数量 */
	@ApiModelProperty(value = "小童数量")
	private Integer youngChildQuantity;

	/** 7天退款比例 */
	@Excel(name = "7天退款比例")
	private BigDecimal sevenDayRefund;

	/** 15天退款比例 */
	@Excel(name = "15天退款比例")
	private BigDecimal fifteenDayRefun;

	/** 48小时退款比例 */
	@Excel(name = "48小时退款比例")
	private BigDecimal fortyEightHoursRefun;

	/** 24小时退款比例 */
	@Excel(name = "24小时退款比例")
	private BigDecimal twentyFourHoursRefun;
	/**
	 * 出发日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date departureDate;

	/**
	 * 退款申请时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date refundApplyTime;

	/**
	 * 申请退款前的订单状态（用于取消退款申请时恢复状态）
	 */
	@ApiModelProperty(value = "申请退款前的订单状态")
	private String previousStatus;

	/**
	 * 商品规格id
	 */
	private Integer specs;	/**
	 * 商品规格id
	 */
	@TableField(exist = false)
	private String specsName;

}
