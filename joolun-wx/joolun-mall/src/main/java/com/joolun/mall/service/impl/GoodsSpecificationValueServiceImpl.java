package com.joolun.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.common.exception.CustomException;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.mall.dto.GoodsSpecsDTO;
import com.joolun.mall.entity.GoodsSpecificationValue;
import com.joolun.mall.mapper.GoodsSpecsMapper;
import com.joolun.mall.service.GoodsSpecificationValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoodsSpecificationValueServiceImpl extends ServiceImpl<GoodsSpecsMapper, GoodsSpecificationValue>
    implements GoodsSpecificationValueService {

  @Override
  public boolean batchSaveByAttr(List<GoodsSpecificationValue> list, String attrId) {
    log.debug("batchSaveByAttr list:{}", list);
    if (ObjectUtil.isEmpty(list)) {
      return Boolean.TRUE;
    }

    // 批量查询过滤出已经存在的规格数据，然后对比后再插入
    List<GoodsSpecificationValue> gsList =
        this.list(initQueryWrapper(GoodsSpecsDTO.builder().attrId(attrId).build()));
    Map<String, GoodsSpecificationValue> gsMap =
        gsList.stream()
            .collect(Collectors.toMap(GoodsSpecificationValue::getAttrValues, a -> a, (k1, k2) -> k1));
    for (GoodsSpecificationValue temp : list) {
      if (ObjectUtil.isNotNull(temp.getId())) {
        LambdaUpdateWrapper<GoodsSpecificationValue> set =
            Wrappers.<GoodsSpecificationValue>lambdaUpdate()
                .set(GoodsSpecificationValue::getAttrValues, temp.getAttrValues())
                .eq(GoodsSpecificationValue::getId, temp.getId())
                .eq(GoodsSpecificationValue::getDeleteFlag, Boolean.FALSE);
        this.update(set);
        continue;
      }

      if (ObjectUtil.isNotNull(gsMap.get(temp.getAttrValues()))) {
        continue;
      }
      temp.setAttrId(attrId);
      temp.setTravelId(SecurityUtils.getLoginUser().getUser().getTravelId());
      this.save(temp);
    }
    return Boolean.TRUE;
  }

  @Override
  public boolean batchDelete(GoodsSpecsDTO dto) {
    log.debug("batchDelete ids:{}", dto.getIds());
    if (ObjectUtil.isEmpty(dto.getIds())) {
      return Boolean.TRUE;
    }
    LambdaUpdateWrapper<GoodsSpecificationValue> set =
            Wrappers.<GoodsSpecificationValue>lambdaUpdate()
                    .set(GoodsSpecificationValue::getDeleteFlag, Boolean.TRUE)
                    .in(GoodsSpecificationValue::getId, dto.getIds());
    return this.update(set);
  }

  /**
   * 初始化查询条件
   *
   * @param dto
   * @return
   */
  private LambdaQueryWrapper<GoodsSpecificationValue> initQueryWrapper(GoodsSpecsDTO dto) {
    LambdaQueryWrapper<GoodsSpecificationValue> wrapper = new LambdaQueryWrapper<>();
    wrapper
//        .eq(ObjectUtil.isNotNull(dto.getGoodsId()), GoodsSpecificationValue::getGoodsId, dto.getGoodsId())
        .eq(ObjectUtil.isNotNull(dto.getAttrId()), GoodsSpecificationValue::getAttrId, dto.getAttrId())
        .eq(
            ObjectUtil.isNotNull(dto.getAttrValues()),
            GoodsSpecificationValue::getAttrValues,
            dto.getAttrValues())
        .eq(GoodsSpecificationValue::getDeleteFlag, Boolean.FALSE)
        .orderByAsc(GoodsSpecificationValue::getSortOrder);
    return wrapper;
  }

  @Override
  public List<GoodsSpecificationValue> selectByDto(GoodsSpecsDTO dto) {
    return this.list(initQueryWrapper(dto));
  }

  @Override
  public List<GoodsSpecificationValue> selectList(GoodsSpecsDTO dto) {
    return this.list(initQueryWrapper(dto));
  }

  @Override
  public boolean insertAttrValues(GoodsSpecsDTO dto) {
    if (ObjectUtil.isNull(dto.getAttrId())) {
      throw new CustomException("请选择规格名");
    }
    if (ObjectUtil.isNull(dto.getGoodsId())) {
      throw new CustomException("未关联商品");
    }
    if (ObjectUtil.isNull(dto.getAttrValues())) {
      throw new CustomException("请填写规格值");
    }
    GoodsSpecificationValue goodsSpecificationValue = BeanUtil.copyProperties(dto, GoodsSpecificationValue.class);
    return this.save(goodsSpecificationValue);
  }
}
