package com.joolun.mall.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.common.core.domain.TreeSelect;
import com.joolun.common.core.domain.entity.TyCity;
import com.joolun.common.core.domain.query.TyCityQuery;
import com.joolun.common.utils.StringUtils;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.entity.CityTree;
import com.joolun.mall.service.ITyCityService;
import com.joolun.mall.util.TreeUtil;
import com.joolun.system.mapper.TyCityMapper;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * cityService业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-18
 */
@Service
public class TyCityServiceImpl extends ServiceImpl<TyCityMapper, TyCity> implements ITyCityService {
  @Autowired private TyCityMapper tyCityMapper;

  /**
   * 查询city
   *
   * @param id cityID
   * @return city
   */
  @Override
  public TyCity selectTyCityById(Long id) {
    return tyCityMapper.selectTyCityById(id);
  }

  /**
   * 查询city列表
   *
   * @param tyCity city
   * @return city
   */
  @Override
  public List<TyCity> selectTyCityList(TyCity tyCity) {
    return tyCityMapper.selectTyCityList(tyCity);
  }

  /**
   * 新增city
   *
   * @param tyCity city
   * @return 结果
   */
  @Override
  public int insertTyCity(TyCity tyCity) {
    return tyCityMapper.insertTyCity(tyCity);
  }

  /**
   * 修改city
   *
   * @param tyCity city
   * @return 结果
   */
  @Override
  public int updateTyCity(TyCity tyCity) {
    return tyCityMapper.updateTyCity(tyCity);
  }

  /**
   * 批量删除city
   *
   * @param ids 需要删除的cityID
   * @return 结果
   */
  @Override
  public int deleteTyCityByIds(Long[] ids) {
    return tyCityMapper.deleteTyCityByIds(ids);
  }

  /**
   * 删除city信息
   *
   * @param id cityID
   * @return 结果
   */
  @Override
  public int deleteTyCityById(Long id) {
    return tyCityMapper.deleteTyCityById(id);
  }

  @Override
  public List<TyCity> buildCityTree(List<TyCity> citys) {
    List<TyCity> returnList = new ArrayList<TyCity>();
    List<Long> tempList = new ArrayList<Long>();
    for (TyCity city : citys) {
      tempList.add(city.getId());
    }
    for (Iterator<TyCity> iterator = citys.iterator(); iterator.hasNext(); ) {
      TyCity city = iterator.next();
      // 如果是顶级节点, 遍历该父节点的所有子节点
      if (!tempList.contains(city.getPid())) {
        recursionFn(citys, city);
        returnList.add(city);
      }
    }
    if (returnList.isEmpty()) {
      returnList = citys;
    }
    return returnList;
  }

  public List<TreeSelect> buildCityTreeSelect(List<TyCity> citys) {
    List<TyCity> cityTree = buildCityTree(citys);
    //        return new ArrayList<>(TreeSelect);
    return cityTree.stream().map(TreeSelect::new).collect(Collectors.toList());
  }

  /** 递归列表 */
  private void recursionFn(List<TyCity> list, TyCity t) {
    // 得到子节点列表
    List<TyCity> childList = getChildList(list, t);
    t.setChildren(childList);
    for (TyCity tChild : childList) {
      if (hasChild(list, tChild)) {
        recursionFn(list, tChild);
      }
    }
  }

  /** 得到子节点列表 */
  private List<TyCity> getChildList(List<TyCity> list, TyCity t) {
    List<TyCity> tlist = new ArrayList<TyCity>();
    Iterator<TyCity> it = list.iterator();
    while (it.hasNext()) {
      TyCity n = it.next();
      if (StringUtils.isNotNull(n.getPid()) && n.getPid().longValue() == t.getId().longValue()) {
        tlist.add(n);
      }
    }
    return tlist;
  }

  /** 判断是否有子节点 */
  private boolean hasChild(List<TyCity> list, TyCity t) {
    return getChildList(list, t).size() > 0;
  }

  @Override
  public List<TyCity> selectDest() {
    return tyCityMapper.selectDest();
  }

  @Override
  public List<TyCity> selectHostDest() {
    return tyCityMapper.selectHostDest();
  }

  @Override
  public List<CityTree> selectTree(TyCity city) {
    return getTree(this.list(Wrappers.lambdaQuery(city)));
  }

  /**
   * 构建树
   *
   * @param entitys
   * @return
   */
  private List<CityTree> getTree(List<TyCity> entitys) {
    List<CityTree> treeList =
        entitys.stream()
            .filter(entity -> !entity.getId().equals(entity.getPid()))
            //                .sorted(Comparator.comparingInt(GoodsCategory::getSort))
            .map(
                entity -> {
                  CityTree node = new CityTree();
                  node.setParentId(entity.getPid().toString());
                  node.setName(entity.getName());
                  node.setId(entity.getId().toString());
                  return node;
                })
            .collect(Collectors.toList());
    return TreeUtil.build(treeList, CommonConstants.PARENT_ID);
  }

  @Override
  public List<TyCity> selectTyCityListNotZero() {
    return tyCityMapper.selectTyCityListNotZero();
  }

  @Override
  public int setHotCities(TyCityQuery query) {
    // 先将所有城市设置为非热门
    tyCityMapper.updateCityHotStatus(null, "0");
    // 再将指定城市设置为热门
    if (ObjectUtil.isNotEmpty(query.getCityIds())) {
      return tyCityMapper.updateCityHotStatus(query, "1");
    }
    return 1;
  }

  @Override
  public List<TyCity> selectHotCities() {
    List<TyCity> tyCities = tyCityMapper.selectHotCities();
    for (TyCity tyCity : tyCities) {
      tyCity.setName(tyCity.getName().replaceAll("市",""));
    }
    return tyCities;
  }
}
