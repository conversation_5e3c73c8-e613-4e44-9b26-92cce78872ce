package com.joolun.mall.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.joolun.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 商城订单详情
 *
 * <AUTHOR>
 * @date 2019-09-10 15:31:40
 */
@Data
@ApiModel(description = "商城订单详情")
public class OrderItemTravelersAppDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** $column.columnComment */
  private String id;

  /** 名称 */
  @Excel(name = "名称")
  @ApiModelProperty(value = "名称")
  private String name;

  /** 证件类型:1-身份证;2-护照;3-港澳通行证; */
  @Excel(name = "证件类型")
  @ApiModelProperty(value = "证件类型")
  private String certType;

  /** 证件号 */
  @Excel(name = "证件号")
  @ApiModelProperty(value = "证件号")
  private String certId;

  /** 用户类型:0-儿童;1-成人; */
  @Excel(name = "用户类型")
  @ApiModelProperty(value = "用户类型")
  private String userType;

  @Excel(name = "用户性别")
  @ApiModelProperty(value = "用户性别")
  private String sex;

  @Excel(name = "手机号")
  @ApiModelProperty(value = "手机号")
  private String phone;

  /** 订单id 用于匹配订单出行人时使用 */
  @TableField(exist = false)
  private String orderId;
}
