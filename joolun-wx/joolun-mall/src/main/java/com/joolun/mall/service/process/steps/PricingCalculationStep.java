package com.joolun.mall.service.process.steps;

import com.joolun.mall.dto.OrderCreationContext;
import com.joolun.mall.entity.GoodsSkuCalendarPrice;
import com.joolun.mall.entity.GoodsSpuDetail;
import com.joolun.mall.entity.OrderItem;
import com.joolun.mall.service.process.OrderProcessStep;
import com.joolun.mall.service.process.ProcessResult;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;

/**
 * 价格计算步骤
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PricingCalculationStep implements OrderProcessStep {
    
    @Override
    public ProcessResult execute(OrderCreationContext context) {
        log.info("开始执行价格计算步骤");
        
        // 卫语句：订单项为空
        if (CollectionUtils.isEmpty(context.getOrderItems())) {
            return ProcessResult.failure("订单项不能为空");
        }
        
        BigDecimal totalOrderPrice = BigDecimal.ZERO;
        
        for (OrderItem orderItem : context.getOrderItems()) {
            PricingResult result = calculateItemPricing(context, orderItem);
            
            // 卫语句：价格计算失败
            if (result == null) {
                return ProcessResult.failure("商品价格计算失败");
            }
            
            orderItem.setPaymentPrice(result.getTotalPrice());
            orderItem.setSalesPrice(result.getTotalPrice());
            totalOrderPrice = totalOrderPrice.add(result.getTotalPrice());
            
            log.info("商品价格计算完成, 商品ID: {}, 成人价格: {}, 老人价格: {}, 儿童价格: {}, 房差: {}, 总价: {}", 
                orderItem.getSpuId(), result.getAdultPrice(), result.getElderlyPrice(), 
                result.getChildPrice(), result.getRoomPrice(), result.getTotalPrice());
        }
        
        // 设置订单总价
        context.getOrderInfo().setPaymentPrice(totalOrderPrice);
        context.getOrderInfo().setSalesPrice(totalOrderPrice);

        log.info("订单总价计算完成, 总金额: {}", totalOrderPrice);
        return ProcessResult.success();
    }
    
    /**
     * 计算单个商品的价格
     */
    private PricingResult calculateItemPricing(OrderCreationContext context, OrderItem orderItem) {
        GoodsSkuCalendarPrice goodsSkuCalendarPrice = context.getGoodsSkuCalendarPrice();

        // 卫语句：商品详情为空
        if (goodsSkuCalendarPrice == null) {
            log.error("商品详情为空，无法计算价格");
            return null;
        }
        
        // 计算各类型人员价格: 老年人按成人价计算
        BigDecimal adultPrice = goodsSkuCalendarPrice.getAdultPrice()
            .multiply(new BigDecimal(context.getConfig().getAdultQuantity()));

        // 老年人按成人价计算（删除老年价）
        BigDecimal elderlyPrice = goodsSkuCalendarPrice.getAdultPrice()
            .multiply(new BigDecimal(context.getConfig().getElderlyQuantity()));
            
        // 业务规则：大童和小童都按儿童价格计算
        BigDecimal childPrice = goodsSkuCalendarPrice.getChildPrice()
            .multiply(new BigDecimal(context.getConfig().getTotalChildQuantity()));
            
        // 房差计算
        BigDecimal roomPrice = BigDecimal.ZERO;
        if (context.getConfig().hasRoomSupplement()) {
            // 从第一个商品获取房差标准
            if (!CollectionUtils.isEmpty(context.getGoodsSpus())) {
                roomPrice = context.getConfig().getReplenishRoomNum()
                    .multiply(context.getGoodsSpus().get(0).getRoomAllowance());
            }
        }
        
        // 计算总价
        BigDecimal totalPrice = adultPrice.add(elderlyPrice).add(childPrice).add(roomPrice);
        
        return new PricingResult(adultPrice, elderlyPrice, childPrice, roomPrice, totalPrice);
    }
    
    @Override
    public String getStepName() {
        return "价格计算";
    }
    
    @Override
    public int getOrder() {
        return 4;
    }
    
    /**
     * 价格计算结果
     */
    @Data
    private static class PricingResult {
        private final BigDecimal adultPrice;
        private final BigDecimal elderlyPrice;
        private final BigDecimal childPrice;
        private final BigDecimal roomPrice;
        private final BigDecimal totalPrice;
        
        public PricingResult(BigDecimal adultPrice, BigDecimal elderlyPrice, 
                           BigDecimal childPrice, BigDecimal roomPrice, BigDecimal totalPrice) {
            this.adultPrice = adultPrice;
            this.elderlyPrice = elderlyPrice;
            this.childPrice = childPrice;
            this.roomPrice = roomPrice;
            this.totalPrice = totalPrice;
        }
    }
} 