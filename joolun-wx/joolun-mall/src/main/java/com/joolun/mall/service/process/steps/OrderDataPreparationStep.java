package com.joolun.mall.service.process.steps;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.joolun.common.utils.DateUtils;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.dto.OrderCreationConfig;
import com.joolun.mall.dto.OrderCreationContext;
import com.joolun.mall.dto.PlaceOrderDTO;
import com.joolun.mall.entity.GoodsSku;
import com.joolun.mall.entity.GoodsSkuCalendarPrice;
import com.joolun.mall.entity.OrderInfo;
import com.joolun.mall.enums.OrderInfoEnum;
import com.joolun.mall.service.IGoodsSkuCalendarPriceService;
import com.joolun.mall.service.IGoodsSkuService;
import com.joolun.mall.service.process.OrderProcessStep;
import com.joolun.mall.service.process.ProcessResult;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/** 订单数据准备步骤 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderDataPreparationStep implements OrderProcessStep {

  private final IGoodsSkuService goodsSkuService;
  private final IGoodsSkuCalendarPriceService goodsSkuCalendarPriceService;

  @Override
  public ProcessResult execute(OrderCreationContext context) {
    log.info("开始执行订单数据准备步骤");

    // 卫语句：验证输入数据
    ProcessResult validationResult = validateInputData(context);
    if (validationResult.isFailure()) {
      return validationResult;
    }

    // 初始化订单基础信息
    OrderInfo orderInfo = initializeOrderInfo(context.getPlaceOrderDTO());
    context.setOrderInfo(orderInfo);

    // 缓存数量配置
    OrderCreationConfig config = new OrderCreationConfig(context.getPlaceOrderDTO());
    context.setConfig(config);

    // 初始化集合
    context.setOrderItems(new ArrayList<>());
    context.setTravelers(new ArrayList<>());
    context.setGoodsSpus(new ArrayList<>());

    // 补全 goodsSku 和 goodsSkuCalendarPrice
    ProcessResult skuResult = prepareSkuData(context);
    if (skuResult.isFailure()) {
      return skuResult;
    }

    log.info(
        "订单数据准备完成, 订单号: {}, 商品GscPriceId: {}, 总人数: {}",
        orderInfo.getOrderNo(),
        context.getPlaceOrderDTO().getDetailId(),
        config.getTotalQuantity());

    return ProcessResult.success();
  }

  /** 准备SKU相关数据 */
  private ProcessResult prepareSkuData(OrderCreationContext context) {
    PlaceOrderDTO dto = context.getPlaceOrderDTO();

    // 获取SKU ID - 优先从skus列表中获取，其次使用detailId推导
    String skuId = dto.getSkuId();
    if (StringUtils.isBlank(skuId)) {
      return ProcessResult.failure("无法确定SKU ID");
    }

    // 获取 GoodsSku
    GoodsSku goodsSku = goodsSkuService.getById(dto.getSkuId());
    if (ObjectUtil.isNull(goodsSku)) {
      log.warn("SKU不存在:{}",skuId);
      return ProcessResult.failure("套餐信息未查询到");
    }
    context.setGoodsSku(goodsSku);

    // 查询对应日期的价格库存
    GoodsSkuCalendarPrice calendarPrice = goodsSkuCalendarPriceService.getById(dto.getDetailId());
    if (ObjectUtil.isNull(calendarPrice)) {
      return ProcessResult.failure("未找到SKU在指定日期的价格库存信息");
    }
    context.setGoodsSkuCalendarPrice(calendarPrice);

    log.info(
        "SKU数据准备完成: skuId={}, 目标日期={}, 成人价={}, 儿童价={}",
        skuId,
        calendarPrice.getCalendarDate(),
        calendarPrice.getAdultPrice(),
        calendarPrice.getChildPrice());

    return ProcessResult.success();
  }

  /** 验证输入数据（使用卫语句） */
  private ProcessResult validateInputData(OrderCreationContext context) {
    PlaceOrderDTO dto = context.getPlaceOrderDTO();

    // 卫语句：DTO为空
    if (dto == null) {
      return ProcessResult.failure("订单数据不能为空");
    }

    // 卫语句：商品详情ID为空
    if (StringUtils.isBlank(dto.getDetailId())) {
      return ProcessResult.failure("商品详情ID不能为空");
    }

    if (StringUtils.isBlank(dto.getSkuId())) {
      return ProcessResult.failure("SkuId不能为空");
    }

    // 卫语句：联系人信息为空
    if (StringUtils.isBlank(dto.getOrderContactName())) {
      return ProcessResult.failure("联系人姓名不能为空");
    }

    if (StringUtils.isBlank(dto.getOrderContactPhone())) {
      return ProcessResult.failure("联系人电话不能为空");
    }

    // 卫语句：人数为0
    // 使用 Optional.ofNullable 安全地处理 getter 可能返回 null 的情况，
    // 然后如果值为 null，则将其映射为 0。
    int totalPeople = Optional.ofNullable(dto.getAdultQuantity()).orElse(0)
            + Optional.ofNullable(dto.getElderlyQuantity()).orElse(0)
            + Optional.ofNullable(dto.getOlderChildQuantity()).orElse(0)
            + Optional.ofNullable(dto.getYoungChildQuantity()).orElse(0);

    if (totalPeople <= 0) {
      return ProcessResult.failure("出行人数不能为0");
    }

    // 卫语句：用户信息为空
    if (context.getWxUser() == null) {
      return ProcessResult.failure("用户信息不能为空");
    }

    return ProcessResult.success();
  }

  /** 初始化订单信息 */
  private OrderInfo initializeOrderInfo(PlaceOrderDTO dto) {
    OrderInfo orderInfo = new OrderInfo();
    BeanUtil.copyProperties(dto, orderInfo);
    orderInfo.setIsPay(CommonConstants.NO);
    orderInfo.setOrderNo(IdUtil.getSnowflake(0, 0).nextIdStr());
    orderInfo.setSalesPrice(BigDecimal.ZERO);
    orderInfo.setFreightPrice(BigDecimal.ZERO);
    orderInfo.setStatus(OrderInfoEnum.STATUS_0.getValue());
    orderInfo.setCreateTime(DateUtils.getNowDate());
    orderInfo.setOrderContactName(dto.getOrderContactName());
    orderInfo.setOrderContactPhone(dto.getOrderContactPhone());
    return orderInfo;
  }

  @Override
  public String getStepName() {
    return "订单数据准备";
  }

  @Override
  public int getOrder() {
    return 1;
  }
}
