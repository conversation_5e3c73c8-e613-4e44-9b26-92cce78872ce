package com.joolun.mall.entity.query;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;

import lombok.*;

/**
 * 商城订单详情查询参数
 *
 * <AUTHOR>
 * @date 2025-07-11 15:31:40
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ApiModel(description = "商城订单详情查询参数")
public class OrderItemQuery implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;
}
