package com.joolun.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.joolun.common.annotation.Excel;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("goods_specification_value")
public class GoodsSpecificationValue implements Serializable {

  private static final long serialVersionUID = 1L;

  /** $column.columnComment */
  @TableId(type = IdType.ASSIGN_ID)
  @Excel(name = "规格值ID")
  private String id;

  /** $column.columnComment */
  @Excel(name = "规格值名称")
  @TableField(value = "name")
  private String name;

  /** 规格值 */
  @Excel(name = "规格值")
  @TableField(value = "attr_values")
  @Deprecated
  private String attrValues;

//  /** $column.columnComment */
//  @Excel(name = "商品ID")
//  @TableField(value = "goods_id")
//  private String goodsId;

  /** $column.columnComment */
  @Excel(name = "规格ID")
  @TableField(value = "attr_id")
  private String attrId;

  /** 旅行社id */
  @Excel(name = "旅行社id")
  private String travelId;

  /** 删除标记：0-未删除;1-已删除; */
  @TableField(value = "delete_flag")
  private Boolean deleteFlag;

  /** 排序 */
  @TableField(value = "sort_order")
  private Integer sortOrder;

  @TableField(value = "create_by", fill = FieldFill.INSERT)
  private String createBy;

  @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
  private String updateBy;

  /** 创建时间 */
  @TableField(value = "create_time", fill = FieldFill.INSERT)
  private Date createTime;

  /** 修改时间 */
  @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
  private Date updateTime;

  public static final String COL_SORT_ORDER = "sort_order";
}
