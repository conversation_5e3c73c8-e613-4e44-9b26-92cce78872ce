package com.joolun.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.common.core.domain.entity.SysDictData;
import com.joolun.common.core.redis.RedisCache;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.common.utils.StringUtils;
import com.joolun.common.utils.http.HttpUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.common.utils.uuid.UUID;
import com.joolun.mall.dto.GoodsSpuDTO;
import com.joolun.mall.entity.*;
import com.joolun.mall.dto.GoodsSpuHomeAppDTO;
import com.joolun.mall.mapper.GoodsSpuMapper;
import com.joolun.mall.service.*;
import com.joolun.system.service.IFileService;
import com.joolun.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * spu商品
 *
 * <AUTHOR>
 * @date 2019-08-12 16:25:10
 */
@Slf4j
@Service
public class GoodsSpuServiceImpl extends ServiceImpl<GoodsSpuMapper, GoodsSpu> implements GoodsSpuService {

  @Resource private GoodsSpuMapper goodsSpuMapper;
  @Resource private RedisCache redisCache;
  @Resource private IFileService fileService;
  @Resource private GoodsSpuUploadFileService goodsSpuUploadFileService;
  @Resource private TravelAgencyCommonConfigService travelAgencyCommonConfigService;
  @Resource private IGoodsSkuSpecificationValueService goodsSkuSpecificationValueService;
  @Resource private IGoodsSkuCalendarPriceService goodsSkuCalendarPriceService;
  @Resource private IGoodsSkuService goodsSkuService;
  @Resource private ISysDictTypeService dictTypeService;
  @Resource private IGoodsCostDetailService goodsCostDetailService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeById(Serializable id) {
		super.removeById(id);
		return true;
	}

  @Override
  public IPage<GoodsSpuHomeAppDTO> selectByPageForApp(IPage<GoodsSpu> page, GoodsSpu goodsSpu) {
    IPage<GoodsSpu> result = goodsSpuMapper.selectPage(page, this.initQueryWrapper(goodsSpu));
    return processGoodsSpuDataForApp(result);
  }

  @Override
  public IPage<GoodsSpu> page1(IPage<GoodsSpu> page, GoodsSpu goodsSpu) {
    List<SysDictData> data = dictTypeService.selectDictDataByType("lv_routes_type");
    List<SysDictData> specificationTypeDict =
        dictTypeService.selectDictDataByType("lv_specification_type");
    IPage<GoodsSpu> result = goodsSpuMapper.selectPage(page, this.initQueryWrapper(goodsSpu));
    
    // 过滤掉null元素，避免NullPointerException
    List<String> goodsIds = result.getRecords().stream()
        .filter(Objects::nonNull)
        .map(GoodsSpu::getId)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
    
    Map<String, Map<String, BigDecimal>> minMaxPriceMap =
        goodsSkuCalendarPriceService.getMinMaxPriceByGoodsIds(goodsIds);
    
    for (GoodsSpu record : result.getRecords()) {
      if (record == null) {
        continue;
      }
      
      // 获取商品的封面图
      List<GoodsSpuUploadFile> list = getGoodsCoverImage(record.getId());
      if (ObjectUtil.isNotEmpty(list)) {
        record.setPicUrls(list);
      }

      for (SysDictData datum : data) {
        if (record.getCategoryFirst() != null && record.getCategoryFirst().equals(datum.getDictValue())) {
          record.setCategoryFirstName(datum.getDictLabel());
        }
      }

      if (ObjectUtil.isNotEmpty(specificationTypeDict)) {
        for (SysDictData datum : specificationTypeDict) {
          if (record.getSpecificationType() != null && record.getSpecificationType().equals(datum.getDictValue())) {
            record.setSpecificationTypeName(datum.getDictLabel());
          }
        }
      }

      if (minMaxPriceMap.containsKey(record.getId())) {
        Map<String, BigDecimal> priceRange = minMaxPriceMap.get(record.getId());
        record.setMinPrice(priceRange.get("minPrice"));
        record.setMaxPrice(priceRange.get("maxPrice"));
      }
    }
    return result;
  }

	@Override
	public IPage<GoodsSpu> pageAuth(IPage<GoodsSpu> page, GoodsSpu goodsSpu) {

		return baseMapper.selectPage1(page, goodsSpu);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean save1(GoodsSpu goodsSpu) {
		baseMapper.insert(goodsSpu);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateById1(GoodsSpu goodsSpu) {
		baseMapper.updateById(goodsSpu);
		return true;
	}

	@Override
	public GoodsSpu getById1(String id) {
		return baseMapper.selectById1(id);
	}

	@Override
	public GoodsSpu getById2(String id) {
		return baseMapper.selectById2(id);
	}

  /**
   * 查询商品
   *
   * @param id 商品ID
   * @return 商品
   */
  @Override
  public GoodsSpuDTO selectGoodsSpuById(String id) {
    GoodsSpu goodsSpu = goodsSpuMapper.selectGoodsSpuById(id);
    if (ObjectUtil.isNull(goodsSpu)) {
      return null;
    }
    GoodsSpuDTO goodsSpuDTO = BeanUtil.copyProperties(goodsSpu, GoodsSpuDTO.class);
    if (StrUtil.isNotBlank(goodsSpu.getStartingPoint())) {
      goodsSpuDTO.setStartingPoint(this.convertToLongList(goodsSpu.getStartingPoint()));
    }
    if (StrUtil.isNotBlank(goodsSpu.getDest())) {
      goodsSpuDTO.setDest(this.convertToLongList(goodsSpu.getDest()));
    }

    // 查询所有类型的图片，包括封面图、轮播图、线路介绍图
    List<GoodsSpuUploadFile> list =
        goodsSpuUploadFileService.list(
            Wrappers.<GoodsSpuUploadFile>lambdaQuery()
                .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
                .eq(GoodsSpuUploadFile::getObjectId, goodsSpu.getId())
                .orderByAsc(GoodsSpuUploadFile::getObjectType)
                .orderByAsc(GoodsSpuUploadFile::getFileSort)
                .orderByAsc(GoodsSpuUploadFile::getCreateTime));
    if (ObjectUtil.isNotEmpty(list)) {
      goodsSpuDTO.setPicUrls(list);
    }

    // 查询费用详情
    List<GoodsCostDetail> costDetails = goodsCostDetailService.selectCostDetailsByGoodsId(goodsSpu.getId());
    if (ObjectUtil.isNotEmpty(costDetails)) {
      goodsSpuDTO.setCostDetails(costDetails);
    }

//    List<GoodsSpecsAttrTagsDTO> attrTags =
//        goodsSpecsAttrService.selectByGoodsIdReturnAttrTagsDTO(
//            GoodsSpecsAttrDTO.builder().goodsId(goodsSpu.getId()).build());
//    if (ObjectUtil.isNotEmpty(attrTags)) {
//      goodsSpuDTO.setTags(attrTags);
//    }

    return goodsSpuDTO;
  }

  private List<Long> convertToLongList(String strArr) {
    return Arrays.stream(strArr.split(","))
        .map(String::trim)
        .filter(s -> !s.isEmpty())
        .map(Long::valueOf)
        .collect(Collectors.toList());
  }

	/**
	 * 查询商品列表
	 *
	 * @param goodsSpu 商品
	 * @return 商品
	 */
	@Override
	public List<GoodsSpu> selectGoodsSpuList(GoodsSpu goodsSpu)
	{
		return goodsSpuMapper.selectGoodsSpuList(goodsSpu);
	}

  /**
   * 新增商品
   *
   * @param goodsSpu 商品
   * @return 结果
   */
  @Transactional
  @Override
  public int insertGoodsSpu(GoodsSpu goodsSpu) {
    goodsSpu.setCreateTime(DateUtils.getNowDate());
    goodsSpu.setTravelId(SecurityUtils.getLoginUser().getUser().getTravelId());
//        goodsSpu.setTravelId("95bea32779c94864ad24aa1c43f165c0");
    //    		int rows = goodsSpuMapper.insertGoodsSpu(goodsSpu);
    int insert = goodsSpuMapper.insert(goodsSpu);
    this.insertGoodsSpuDetail(goodsSpu);
    this.updateGoodsSpuUploadFileObjectId(goodsSpu);
    return insert;
  }

  /**
   * 修改商品
   *
   * @param goodsSpu 商品
   * @return 结果
   */
  @Transactional
  @Override
  public int updateGoodsSpu(GoodsSpu goodsSpu) {
    goodsSpu.setUpdateTime(DateUtils.getNowDate());
    //    goodsSpuMapper.deleteGoodsSpuDetailByGoodsId(goodsSpu.getId());
    //    goodsSpuMapper.deleteGoodsSpecsByGoodsId(goodsSpu.getId());
    //    insertGoodsSpuDetail(goodsSpu);
    //    insertGoodsSpecs(goodsSpu);
    this.updateGoodsSpuUploadFileObjectId(goodsSpu);
    return goodsSpuMapper.updateGoodsSpu(goodsSpu);
  }

  /**
   * 批量删除商品
   *
   * @param ids 需要删除的商品ID
   * @return 结果
   */
  @Transactional
  @Override
  public int deleteGoodsSpuByIds(String[] ids) {
    // 删除商品明细
    goodsSpuMapper.deleteGoodsSpuDetailByGoodsIds(ids);
    int delSkuSpecValCount =
        goodsSkuSpecificationValueService.deleteByGoodsIds(
            Arrays.stream(ids).collect(Collectors.toSet()));
    log.info("删除商品sku规格值数量:{}", delSkuSpecValCount);
    int delSkuCount =
        goodsSkuService.deleteGoodsSkuByGoodsIds(Arrays.stream(ids).collect(Collectors.toSet()));
    log.info("删除商品sku数量:{}", delSkuCount);
    // 删除商品
    return goodsSpuMapper.deleteGoodsSpuByIds(ids);
  }

	/**
	 * 删除商品信息
	 *
	 * @param id 商品ID
	 * @return 结果
	 */
	@Override
	public int deleteGoodsSpuById(String id)
	{
		goodsSpuMapper.deleteGoodsSpuDetailByGoodsId(id);
		return goodsSpuMapper.deleteGoodsSpuById(id);
	}

	/**
	 * 新增商品明细列表信息
	 *
	 * @param goodsSpu 商品对象
	 */
	public void insertGoodsSpuDetail(GoodsSpu goodsSpu)
	{
		List<GoodsSpuDetail> goodsSpuDetailList = goodsSpu.getDetailList();
		if (StringUtils.isNotNull(goodsSpuDetailList))
		{
			List<GoodsSpuDetail> list = new ArrayList<GoodsSpuDetail>();
			for (GoodsSpuDetail goodsSpuDetail : goodsSpuDetailList)
			{
				goodsSpuDetail.setId(IdUtils.simpleUUID());
				goodsSpuDetail.setGoodsId(goodsSpu.getId());
				goodsSpuDetail.setCreateBy(SecurityUtils.getUsername());
				goodsSpuDetail.setCreateTime(DateUtils.getNowDate());
				list.add(goodsSpuDetail);
			}
			if (list.size() > 0)
			{
				goodsSpuMapper.batchGoodsSpuDetail(list);
			}
		}
	}

  @Transactional
  public void updateGoodsSpuUploadFileObjectId(GoodsSpu goodsSpu) {
    log.info("更新商品图片关联信息, 商品ID: {}", goodsSpu.getId());
    String goodsSpuId = goodsSpu.getId();
    String currentUserId = SecurityUtils.getUsername(); // 获取当前用户

    // 前端已经通过批量更新接口处理了图片关联，
    // 这里提供安全的兜底逻辑：只处理当前用户最近5分钟内上传的未关联图片
    
    // 计算5分钟前的时间
    Date fiveMinutesAgo = new Date(System.currentTimeMillis() - 5 * 60 * 1000);
    
    // 查找当前用户最近5分钟内上传的未关联图片
    List<GoodsSpuUploadFile> unlinkedFiles = goodsSpuUploadFileService
        .lambdaQuery()
        .and(wrapper -> wrapper
            .isNull(GoodsSpuUploadFile::getObjectId)
            .or()
            .eq(GoodsSpuUploadFile::getObjectId, "")
        )
        .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
        .eq(GoodsSpuUploadFile::getCreateBy, currentUserId) // 只处理当前用户的图片
        .ge(GoodsSpuUploadFile::getCreateTime, fiveMinutesAgo) // 只处理5分钟内的图片
        .orderByDesc(GoodsSpuUploadFile::getCreateTime)
        .last("LIMIT 10") // 额外保险：限制数量
        .list();

    if (CollectionUtil.isNotEmpty(unlinkedFiles)) {
      log.info("发现当前用户 {} 最近5分钟内的 {} 个未关联图片，将关联到商品: {}", 
          currentUserId, unlinkedFiles.size(), goodsSpuId);
      
      unlinkedFiles.forEach(file -> {
        file.setObjectId(goodsSpuId);
        file.setDeleteFlag(false);
      });
      
      goodsSpuUploadFileService.updateBatchById(unlinkedFiles);
      log.info("已将 {} 个图片安全关联到商品: {}", unlinkedFiles.size(), goodsSpuId);
    } else {
      log.info("未发现当前用户的待关联图片");
    }

    // 验证当前商品关联的图片数量
    long totalImages = goodsSpuUploadFileService
        .lambdaQuery()
        .eq(GoodsSpuUploadFile::getObjectId, goodsSpuId)
        .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
        .count();
    
    log.info("商品 {} 当前关联的图片总数: {}", goodsSpuId, totalImages);
    
    // 🔒 【最安全方案】完全移除兜底逻辑的版本：
    // 如果你希望最高安全性，可以用下面的简化版本替换上面的兜底逻辑：
    /*
    log.info("图片关联完全由前端批量更新接口处理，跳过兜底逻辑");
    
    // 仅验证当前商品关联的图片数量
    long totalImages = goodsSpuUploadFileService
        .lambdaQuery()
        .eq(GoodsSpuUploadFile::getObjectId, goodsSpuId)
        .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
        .count();
    
    log.info("商品 {} 当前关联的图片总数: {}", goodsSpuId, totalImages);
    */
  }

//	/**
//	 * 新增商品规格列表信息
//	 *
//	 * @param goodsSpu 商品对象
//	 */
//	public void insertGoodsSpecs(GoodsSpu goodsSpu)
//	{
//		List<GoodsSpecificationValue> goodsSpecs = goodsSpu.getGoodsSpecificationValue();
//		if (StringUtils.isNotNull(goodsSpecs))
//		{
//			List<GoodsSpecificationValue> list = new ArrayList<GoodsSpecificationValue>();
//			for (GoodsSpecificationValue specs : goodsSpecs)
//			{
//				specs.setGoodsId(goodsSpu.getId());
//				specs.setCreateBy(SecurityUtils.getUsername());
//				specs.setCreateTime(DateUtils.getNowDate());
//				list.add(specs);
//			}
//			if (list.size() > 0)
//			{
//				goodsSpuMapper.batchGoodsSpecs(list);
//			}
//		}
//	}


	/**
	 * 新增商品明细列表信息
	 *
	 * @param id detailId
	 */
	public GoodsSpuDetail getGoodsSpuDetailById(String id)
	{
		return goodsSpuMapper.getGoodsSpuDetailById(id);
	}


	/**
	 * 修改商品
	 *
	 * @param goodsSpuDetail 商品
	 * @return 结果
	 */
	@Transactional
	@Override
	public int updateGoodsSpuDetail(GoodsSpuDetail goodsSpuDetail)
	{
			return 	goodsSpuMapper.updateGoodsSpuDetail(goodsSpuDetail);
	}


	@Override
	public IPage<GoodsSpu> selectCollectPageByUserId(IPage<GoodsSpu> page, String userId) {
		return goodsSpuMapper.selectCollectPageByUserId(page,userId);
	}

	@Override
	public IPage<GoodsSpu> selectBrowsePageByUserId(IPage<GoodsSpu> page, String userId) {
		return goodsSpuMapper.selectBrowsePageByUserId(page,userId);
	}

	/**
	 * 分页查询用户收藏商品（返回App格式数据）
	 */
	@Override
	public IPage<GoodsSpuHomeAppDTO> selectCollectPageByUserIdForApp(IPage<GoodsSpu> page, String userId) {
		// 获取基础数据
		IPage<GoodsSpu> result = goodsSpuMapper.selectCollectPageByUserId(page, userId);
		
		// 复用 selectByPageForApp 的数据处理逻辑
		return processGoodsSpuDataForApp(result);
	}

	/**
	 * 分页查询用户浏览商品（返回App格式数据）
	 */
	@Override
	public IPage<GoodsSpuHomeAppDTO> selectBrowsePageByUserIdForApp(IPage<GoodsSpu> page, String userId) {
		// 获取基础数据
		IPage<GoodsSpu> result = goodsSpuMapper.selectBrowsePageByUserId(page, userId);
		
		// 复用 selectByPageForApp 的数据处理逻辑
		return processGoodsSpuDataForApp(result);
	}

	/**
	 * 抽取的公共数据处理方法，处理商品数据为App格式
	 */
	private IPage<GoodsSpuHomeAppDTO> processGoodsSpuDataForApp(IPage<GoodsSpu> result) {
		List<SysDictData> data = dictTypeService.selectDictDataByType("lv_routes_type");
		List<SysDictData> specificationTypeDict =
				dictTypeService.selectDictDataByType("lv_specification_type");
		
		// 过滤掉null元素，避免NullPointerException
		List<String> goodsIds = result.getRecords().stream()
				.filter(Objects::nonNull)
				.map(GoodsSpu::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
		
		Map<String, Map<String, BigDecimal>> minMaxPriceMap =
				goodsSkuCalendarPriceService.getMinMaxPriceByGoodsIds(goodsIds);

		IPage<GoodsSpuHomeAppDTO> voPage = new Page<>();
		BeanUtils.copyProperties(result, voPage, "records");

		// 过滤掉null元素进行处理
		for (GoodsSpu record : result.getRecords()) {
			if (record == null) {
				continue;
			}
			
			// 获取商品的封面图
			List<GoodsSpuUploadFile> list = getGoodsCoverImage(record.getId());
			if (ObjectUtil.isNotEmpty(list)) {
				record.setPicUrls(list);
			}

			for (SysDictData datum : data) {
				if (record.getCategoryFirst() != null && record.getCategoryFirst().equals(datum.getDictValue())) {
					record.setCategoryFirstName(datum.getDictLabel());
				}
			}

			if (ObjectUtil.isNotEmpty(specificationTypeDict)) {
				for (SysDictData datum : specificationTypeDict) {
					if (record.getSpecificationType() != null && record.getSpecificationType().equals(datum.getDictValue())) {
						record.setSpecificationTypeName(datum.getDictLabel());
					}
				}
			}

			if (minMaxPriceMap.containsKey(record.getId())) {
				Map<String, BigDecimal> priceRange = minMaxPriceMap.get(record.getId());
				record.setMinPrice(priceRange.get("minPrice"));
				record.setMaxPrice(priceRange.get("maxPrice"));
			}
		}
		// 过滤掉null元素后再转换
		List<GoodsSpu> validRecords = result.getRecords().stream()
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
		voPage.setRecords(BeanUtil.copyToList(validRecords, GoodsSpuHomeAppDTO.class));
		return voPage;
	}

  /** 生成小程序码 */
  //	@Override
  //	public String getRQCode(String page,String scene) {
  //		try {
  //			String token = getToken();
  //			//获取小程序码，适用于需要的码数量较少的业务场景。通过该接口生成的小程序码，永久有效，有数量限制
  ////            URL url = new URL("https://api.weixin.qq.com/wxa/getwxacode?access_token=" +
  // accessToken);
  //			//获取小程序码，适用于需要的码数量极多的业务场景。通过该接口生成的小程序码，永久有效，数量暂无限制。
  //			URL url = new URL("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + token);
  //
  //			HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
  //			httpURLConnection.setRequestMethod("POST");// 提交模式
  //			// 发送POST请求必须设置如下两行
  //			httpURLConnection.setDoOutput(true);
  //			httpURLConnection.setDoInput(true);
  //			// 获取URLConnection对象对应的输出流
  //			PrintWriter printWriter = new PrintWriter(httpURLConnection.getOutputStream());
  //			// 发送请求参数
  //			cn.hutool.json.JSONObject paramJson = new cn.hutool.json.JSONObject();
  //			paramJson.put("path", page);
  //			paramJson.put("scene", scene);
  //			paramJson.put("width", 430);
  //			paramJson.put("auto_color", true);
  //			//设置小程序码版本
  //			//paramJson.put("env_version","release"); 默认正式
  //			//paramJson.put("env_version","trial"); 体验版
  //			//paramJson.put("env_version","develop"); 开发版
  //
  //			printWriter.write(paramJson.toString());
  //			// flush输出流的缓冲
  //			printWriter.flush();
  //			String contentType = httpURLConnection.getContentType();
  //			if (contentType.contains("json")) {
  //				System.out.println("调用微信小程序生成接口出错,token失效");
  //				return null;
  //			} else {
  //				//开始获取数据
  //				InputStream is = httpURLConnection.getInputStream();
  //				String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();
  //				return fileService.upload(is, "RQCode", wxUserId+".png");
  //			}
  //		} catch (Exception e) {
  //			e.printStackTrace();
  //			return null;
  //		}
  //	}

  @Override
  public String getRQCode(String page, String scene) {
    try {
      String token = getToken();
      // 获取小程序码，适用于需要的码数量较少的业务场景。通过该接口生成的小程序码，永久有效，有数量限制
      URL url = new URL("https://api.weixin.qq.com/wxa/getwxacode?access_token=" + token);
      // 获取小程序码，适用于需要的码数量极多的业务场景。通过该接口生成的小程序码，永久有效，数量暂无限制。
      //			URL url = new URL("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" +
      // token);

      HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
      httpURLConnection.setRequestMethod("POST"); // 提交模式
      // 发送POST请求必须设置如下两行
      httpURLConnection.setDoOutput(true);
      httpURLConnection.setDoInput(true);
      // 获取URLConnection对象对应的输出流
      PrintWriter printWriter = new PrintWriter(httpURLConnection.getOutputStream());
      // 发送请求参数
      cn.hutool.json.JSONObject paramJson = new cn.hutool.json.JSONObject();
      paramJson.put("path", page);
      //			paramJson.put("scene", scene);
      paramJson.put("width", 430);
      paramJson.put("auto_color", true);
      // 设置小程序码版本
      // paramJson.put("env_version","release"); 默认正式
      // paramJson.put("env_version","trial"); 体验版
      // paramJson.put("env_version","develop"); 开发版

      printWriter.write(paramJson.toString());
      // flush输出流的缓冲
      printWriter.flush();
      String contentType = httpURLConnection.getContentType();
      if (contentType.contains("json")) {
        System.out.println("调用微信小程序生成接口出错,token失效");
        return null;
      } else {
        // 开始获取数据
        InputStream is = httpURLConnection.getInputStream();
        //				String wxUserId = ThirdSessionHolder.getThirdSession().getWxUserId();

        return fileService.upload(
            is,
            "RQCode",
            UUID.randomUUID() + ".png",
            SecurityUtils.getLoginUser().getUser().getTravelId());
      }
    } catch (Exception e) {
      log.error("获取小程序码发生异常:{}", e.getMessage(), e);
      return null;
    }
  }

	//获取接口调用token
	private String getToken() {
		Object mpAccessKey = redisCache.getCacheObject("mp_access_key212");
		String token=null;
		if(null==mpAccessKey){
			JSONObject jsonobject = new JSONObject();
			jsonobject.put("grant_type", "client_credential");
			jsonobject.put("appid", "wxd5b9c838bcfddbda");
			jsonobject.put("secret", "6c49b12a6554ae1f32e25cf127349b81");

			String result = HttpUtils.sendSSLPostAndJson("https://api.weixin.qq.com/cgi-bin/stable_token",jsonobject.toString());
			JSONObject object = JSONObject.parseObject(result);
			Object accessToken = object.get("access_token");
			token=accessToken.toString();
			if(null!=accessToken){
				redisCache.setCacheObject("mp_access_key212", token, 7200, TimeUnit.SECONDS);
			}
		}else {
			token=mpAccessKey.toString();
		}
		return token;
	}


	@Override
	public int updateGoodsSpuRQCode(String id, String path) {
		return goodsSpuMapper.updateGoodsSpuRQCode(id,path);
	}

  @Override
  public int updateSpecificationTypeIsZero(String id) {
    return goodsSpuMapper.update(
        null,
        Wrappers.lambdaUpdate(GoodsSpu.class)
            .set(GoodsSpu::getSpecificationType, "0")
            .eq(GoodsSpu::getId, id));
  }

  @Override
  public int updateSpecificationTypeIsOne(String id) {
    return goodsSpuMapper.update(
        null,
        Wrappers.lambdaUpdate(GoodsSpu.class)
            .set(GoodsSpu::getSpecificationType, "1")
            .eq(GoodsSpu::getId, id));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public int toggleShelfStatus(String id) {
    // 先查询当前状态
    GoodsSpu goodsSpu = goodsSpuMapper.selectById(id);
    if (ObjectUtil.isNull(goodsSpu)) {
      throw new RuntimeException("商品不存在");
    }
    
    // 切换状态：0->1, 1->0
    String newShelfStatus = "1".equals(goodsSpu.getShelf()) ? "0" : "1";
    
    return goodsSpuMapper.update(
        null,
        Wrappers.lambdaUpdate(GoodsSpu.class)
            .set(GoodsSpu::getShelf, newShelfStatus)
            .set(GoodsSpu::getUpdateTime, DateUtils.getNowDate())
            .eq(GoodsSpu::getId, id));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public int toggleShowStatus(String id) {
    // 先查询当前状态
    GoodsSpu goodsSpu = goodsSpuMapper.selectById(id);
    if (ObjectUtil.isNull(goodsSpu)) {
      throw new RuntimeException("商品不存在");
    }
    
    // 切换状态：0->1, 1->0
    String newShowStatus = "1".equals(goodsSpu.getIsShow()) ? "0" : "1";
    
    return goodsSpuMapper.update(
        null,
        Wrappers.lambdaUpdate(GoodsSpu.class)
            .set(GoodsSpu::getIsShow, newShowStatus)
            .set(GoodsSpu::getUpdateTime, DateUtils.getNowDate())
            .eq(GoodsSpu::getId, id));
  }

  /**
   * 初始化查询条件
   *
   * @param query
   * @return
   */
  private LambdaQueryWrapper<GoodsSpu> initQueryWrapper(GoodsSpu query) {
    return Wrappers.<GoodsSpu>lambdaQuery()
        .eq(query.getShelf() != null, GoodsSpu::getShelf, query.getShelf())
        .eq(query.getIsShow() != null, GoodsSpu::getIsShow, query.getIsShow())
        .eq(query.getCategoryFirst() != null, GoodsSpu::getCategoryFirst, query.getCategoryFirst())
        .eq(
            query.getCategorySecond() != null,
            GoodsSpu::getCategorySecond,
            query.getCategorySecond())
        .and(query.getName() != null, wrapper -> 
            wrapper.like(GoodsSpu::getSpotName, query.getName())      // 景区名称
                   .or().like(GoodsSpu::getName, query.getName())      // 商品名称  
                   .or().like(GoodsSpu::getDestCity, query.getName())  // 目的地城市
        )
        .eq(query.getSpuCode() != null, GoodsSpu::getSpuCode, query.getSpuCode())
        .eq(
            ObjectUtil.isNotEmpty(query.getStartProvince()),
            GoodsSpu::getStartProvince,
            query.getStartProvince())
        .eq(
            ObjectUtil.isNotEmpty(query.getStartCity()),
            GoodsSpu::getStartCity,
            query.getStartCity())
        .eq(
            ObjectUtil.isNotEmpty(query.getStartRegion()),
            GoodsSpu::getStartRegion,
            query.getStartRegion())
        .eq(
            ObjectUtil.isNotEmpty(query.getDestProvince()),
            GoodsSpu::getDestProvince,
            query.getDestProvince())
        .eq(ObjectUtil.isNotEmpty(query.getDestCity()), GoodsSpu::getDestCity, query.getDestCity())
        .eq(
            ObjectUtil.isNotEmpty(query.getDestRegion()),
            GoodsSpu::getDestRegion,
            query.getDestRegion())
        .eq(ObjectUtil.isNotEmpty(query.getTravelId()), GoodsSpu::getTravelId, query.getTravelId())
        .eq(GoodsSpu::getDelFlag, Boolean.FALSE)
        .orderByDesc(GoodsSpu::getCreateTime);
  }

  /**
   * 获取商品的封面图，如果没有封面图则获取其他类型的图片
   *
   * @param goodsId 商品ID
   * @return 图片列表
   */
  private List<GoodsSpuUploadFile> getGoodsCoverImage(String goodsId) {
    // 优先查询封面图
    List<GoodsSpuUploadFile> list = goodsSpuUploadFileService.list(
        Wrappers.<GoodsSpuUploadFile>lambdaQuery()
            .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
            .eq(GoodsSpuUploadFile::getObjectId, goodsId)
            .eq(GoodsSpuUploadFile::getObjectType, "0") // 优先查询封面图
            .orderByAsc(GoodsSpuUploadFile::getFileSort)
            .orderByAsc(GoodsSpuUploadFile::getCreateTime)
            .last("LIMIT 1"));
    
    // 如果没有封面图，则查询其他类型的图片
    if (ObjectUtil.isEmpty(list)) {
      list = goodsSpuUploadFileService.list(
          Wrappers.<GoodsSpuUploadFile>lambdaQuery()
              .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
              .eq(GoodsSpuUploadFile::getObjectId, goodsId)
              .orderByAsc(GoodsSpuUploadFile::getObjectType) // 按类型排序，封面图优先
              .orderByAsc(GoodsSpuUploadFile::getFileSort)
              .orderByAsc(GoodsSpuUploadFile::getCreateTime)
              .last("LIMIT 1"));
    }
    
    return list;
  }

}
