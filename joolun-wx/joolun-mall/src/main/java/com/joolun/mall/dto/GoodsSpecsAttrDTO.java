package com.joolun.mall.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.joolun.common.annotation.Excel;
import com.joolun.mall.entity.GoodsSpecificationValue;
import lombok.*;

import java.util.List;
import java.util.stream.Collectors;

/** 商品规格名 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsSpecsAttrDTO {
  @Excel(name = "规格ID")
  private String id;

  @Excel(name = "规格名称")
  private String attrName;

  @Excel(name = "商品ID")
  private String goodsId;

  /** 是否套餐 */
  @Excel(name = "规格类型", readConverterExp = "Y=套餐,N=普通规格")
  private String isPackage;

  /** 是否共有属性: 0-不是;1-是; */
  @Excel(name = "属性类型", readConverterExp = "true=共有属性,false=私有属性")
  private Boolean commonAttr;

  /** 删除标记：0-未删除;1-已删除; */
  private Boolean deleteFlag;

  private String createBy;
  private String updateBy;

  private List<GoodsSpecificationValue> goodsSpecificationValueList;

  @Excel(name = "规格值信息")
  private String specsValues;

  private Integer specsValuesCount;

  private Integer sortOrder;

  public void setGoodsSpecificationValueList(List<GoodsSpecificationValue> goodsSpecificationValueList) {
    this.goodsSpecificationValueList = goodsSpecificationValueList;
    if (goodsSpecificationValueList == null || goodsSpecificationValueList.isEmpty()) {
      this.specsValues = "";
    } else {
      this.specsValues = goodsSpecificationValueList.stream()
          .map(spec -> spec.getName() != null ? spec.getName() : spec.getAttrValues())
          .collect(Collectors.joining("、"));
    }
  }
}
