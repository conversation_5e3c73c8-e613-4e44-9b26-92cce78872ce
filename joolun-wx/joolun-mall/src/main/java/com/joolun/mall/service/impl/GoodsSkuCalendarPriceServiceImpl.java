package com.joolun.mall.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.common.utils.validation.CheckUtils;
import com.joolun.mall.dto.GoodsSkuCalendarPriceDTO;
import com.joolun.mall.dto.GoodsSkuCalendarPriceResponse;
import com.joolun.mall.entity.*;
import com.joolun.mall.mapper.GoodsSkuCalendarPriceMapper;
import com.joolun.mall.service.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * SKU 日历价格库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Slf4j
@Service
public class GoodsSkuCalendarPriceServiceImpl
    extends ServiceImpl<GoodsSkuCalendarPriceMapper, GoodsSkuCalendarPrice>
    implements IGoodsSkuCalendarPriceService {
  @Resource private GoodsSkuCalendarPriceMapper goodsSkuCalendarPriceMapper;
  @Resource private IGoodsSkuService goodsSkuService;
  @Resource private GoodsSpuService goodsSpuService;
  @Resource private GoodsSpecificationValueService specValueService;
  @Resource private GoodsSpecificationAttributeService specAttrService;
  @Resource private IGoodsSkuSpecificationValueService skuSpecService;
  @Resource private IGoodsSkuSpecificationValueService goodsSkuSpecificationValueService;
  @Resource private CheckUtils checkUtils;

  /**
   * 查询SKU 日历价格库存
   *
   * @param id SKU 日历价格库存ID
   * @return SKU 日历价格库存
   */
  @Override
  public GoodsSkuCalendarPrice selectGoodsSkuCalendarPriceById(Long id) {
    return goodsSkuCalendarPriceMapper.selectGoodsSkuCalendarPriceById(id);
  }

  /**
   * 查询SKU 日历价格库存列表
   *
   * @param goodsSkuCalendarPrice SKU 日历价格库存
   * @return SKU 日历价格库存
   */
  @Override
  public List<GoodsSkuCalendarPrice> selectGoodsSkuCalendarPriceList(
      GoodsSkuCalendarPrice goodsSkuCalendarPrice) {
    return goodsSkuCalendarPriceMapper.selectGoodsSkuCalendarPriceList(goodsSkuCalendarPrice);
  }

  /**
   * 新增SKU 日历价格库存
   *
   * @param goodsSkuCalendarPrice SKU 日历价格库存
   * @return 结果
   */
  @Override
  public int insertGoodsSkuCalendarPrice(GoodsSkuCalendarPrice goodsSkuCalendarPrice) {
    goodsSkuCalendarPrice.setCreateTime(DateUtils.getNowDate());
    return goodsSkuCalendarPriceMapper.insertGoodsSkuCalendarPrice(goodsSkuCalendarPrice);
  }

  /**
   * 修改SKU 日历价格库存
   *
   * @param goodsSkuCalendarPrice SKU 日历价格库存
   * @return 结果
   */
  @Override
  public int updateGoodsSkuCalendarPrice(GoodsSkuCalendarPrice goodsSkuCalendarPrice) {
    goodsSkuCalendarPrice.setUpdateTime(DateUtils.getNowDate());
    return goodsSkuCalendarPriceMapper.updateGoodsSkuCalendarPrice(goodsSkuCalendarPrice);
  }

  /**
   * 批量删除SKU 日历价格库存
   *
   * @param ids 需要删除的SKU 日历价格库存ID
   * @return 结果
   */
  @Override
  public int deleteGoodsSkuCalendarPriceByIds(Long[] ids) {
    return goodsSkuCalendarPriceMapper.deleteGoodsSkuCalendarPriceByIds(ids);
  }

  /**
   * 删除SKU 日历价格库存信息
   *
   * @param id SKU 日历价格库存ID
   * @return 结果
   */
  @Override
  public int deleteGoodsSkuCalendarPriceById(Long id) {
    return goodsSkuCalendarPriceMapper.deleteGoodsSkuCalendarPriceById(id);
  }

  /** 统一规格：批量保存日历价格库存 切换到统一规格时，清除之前的多规格数据 */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public int batchSaveForSpecType0(GoodsSkuCalendarPriceDTO dto) {
    // 校验 SPU
    GoodsSpu spu = goodsSpuService.getById(dto.getGoodsId());
    checkUtils.throwIfNull(spu, "商品 SPU [{}] 不存在", dto.getGoodsId());
    goodsSpuService.updateSpecificationTypeIsZero(dto.getGoodsId());

    // 删除之前的多规格 SKU 及其日历数据
    List<GoodsSku> oldMulti =
        goodsSkuService.list(
            new QueryWrapper<GoodsSku>()
                .eq("goods_id", dto.getGoodsId())
                .eq("has_specifications", 1));
    if (!oldMulti.isEmpty()) {
      List<String> ids = oldMulti.stream().map(GoodsSku::getId).collect(Collectors.toList());
      // 删除多规格的日历记录
      this.remove(new QueryWrapper<GoodsSkuCalendarPrice>().in("sku_id", ids));
      // 删除多规格 SKU
      goodsSkuService.removeByIds(ids);
    }

    // 获取或创建统一规格 SKU（确保只有一个）
    String skuId = dto.getSkuId();
    if (StrUtil.isBlank(skuId)) {
      // 先查找是否已存在统一规格的 SKU
      List<GoodsSku> existingUnifiedSkus = goodsSkuService.list(
          new QueryWrapper<GoodsSku>()
              .eq("goods_id", dto.getGoodsId())
              .eq("has_specifications", 0)
              .eq("delete_flag", false));
      
      if (!existingUnifiedSkus.isEmpty()) {
        // 如果已存在统一规格 SKU，使用第一个
        skuId = existingUnifiedSkus.get(0).getId();
      } else {
        // 如果不存在，创建新的统一规格 SKU
        GoodsSku sku =
            GoodsSku.builder()
                .goodsId(dto.getGoodsId())
                .hasSpecifications(0)
                .deleteFlag(false)
                .skuCode(CharSequenceUtil.format("SKU-{}", dto.getGoodsId()))
                .skuName(dto.getSkuName() != null ? dto.getSkuName() : "默认规格")
                .build();
        goodsSkuService.save(sku);
        skuId = sku.getId();
      }
      dto.setSkuId(skuId);
    }
    
    // 无论是否传入了skuId，都要检查并更新SKU名称
    if (dto.getSkuName() != null && !dto.getSkuName().trim().isEmpty()) {
      GoodsSku existingSku = goodsSkuService.getById(skuId);
      if (existingSku != null && !dto.getSkuName().equals(existingSku.getSkuName())) {
        existingSku.setSkuName(dto.getSkuName());
        goodsSkuService.updateById(existingSku);
      }
    }

    // 处理日历价格数据：先查询已存在的记录，然后决定更新还是插入
    String finalSkuId = skuId;
    List<GoodsSkuCalendarPrice> toSaveOrUpdate = new ArrayList<>();
    
    for (GoodsSkuCalendarPrice item : dto.getPriceList()) {
      item.setSkuId(finalSkuId);
      item.setGoodsId(dto.getGoodsId());
      item.setDeleteFlag(false);
      
      // 查询是否已存在相同SKU和日期的记录
      GoodsSkuCalendarPrice existing = this.getOne(
          new QueryWrapper<GoodsSkuCalendarPrice>()
              .eq("sku_id", finalSkuId)
              .eq("calendar_date", item.getCalendarDate())
              .eq("delete_flag", false)
      );
      
      if (existing != null) {
        // 如果存在，更新现有记录
        existing.setAdultPrice(item.getAdultPrice());
        existing.setChildPrice(item.getChildPrice());
        existing.setCommission(item.getCommission());
        existing.setStock(item.getStock());
        existing.setStatus(item.getStatus());
        existing.setUpdateTime(DateUtils.getNowDate());
        toSaveOrUpdate.add(existing);
      } else {
        // 如果不存在，添加新记录
        item.setCreateTime(DateUtils.getNowDate());
        toSaveOrUpdate.add(item);
      }
    }
    
    boolean ok = this.saveOrUpdateBatch(toSaveOrUpdate, 200);
    checkUtils.throwIf(!ok, "批量保存统一规格日历价格库存失败 for SPU [{}]", dto.getGoodsId());
    return dto.getPriceList().size();
  }

  /**
   * 多规格：批量保存日历价格库存
   * 切换到多规格时，清除之前的统一规格数据
   *
   * @param dto 日历价格库存数据传输对象
   * @return 保存的记录数
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public int batchSaveForSpecType1(GoodsSkuCalendarPriceDTO dto) {
    log.info("开始批量保存多规格日历价格库存，商品ID: {}", dto.getGoodsId());
    
    // 1. 校验SPU并更新规格类型
    validateAndUpdateSpu(dto.getGoodsId());
    
    // 2. 处理规格值变更
    handleSpecValueChanges(dto);
    
    // 3. 早期返回：如果没有价格数据，直接返回
    if (ObjectUtil.isEmpty(dto.getPriceList())) {
      log.info("价格列表为空，操作完成，商品ID: {}", dto.getGoodsId());
      return 1;
    }
    
    // 4. 删除统一规格SKU及其日历数据
    removeUnifiedSpecSkus(dto.getGoodsId());
    
    // 5. 处理价格项SKU创建
    processSkuCreationForPriceItems(dto);
    
    // 6. 保存或更新日历价格
    int savedCount = saveOrUpdateCalendarPrices(dto);
    
    log.info("批量保存多规格日历价格库存完成，商品ID: {}, 保存记录数: {}", dto.getGoodsId(), savedCount);
    return savedCount;
  }

  /**
   * 校验SPU并更新规格类型为多规格
   *
   * @param goodsId 商品ID
   */
  private void validateAndUpdateSpu(String goodsId) {
    log.debug("校验SPU并更新规格类型，商品ID: {}", goodsId);
    
    GoodsSpu spu = goodsSpuService.getById(goodsId);
    checkUtils.throwIfNull(spu, "商品 SPU [{}] 不存在", goodsId);
    
    goodsSpuService.updateSpecificationTypeIsOne(goodsId);
    log.debug("SPU规格类型已更新为多规格，商品ID: {}", goodsId);
  }

  /**
   * 处理规格值变更（删除和新增）
   *
   * @param dto 日历价格库存数据传输对象
   */
  private void handleSpecValueChanges(GoodsSkuCalendarPriceDTO dto) {
    if (dto.getSpecChanges() == null) {
      log.debug("无规格变更，跳过处理，商品ID: {}", dto.getGoodsId());
      return;
    }
    
    log.info("开始处理规格值变更，商品ID: {}", dto.getGoodsId());
    
    if (!CollectionUtils.isEmpty(dto.getSpecChanges().getSpecValueChanges())) {
      // 处理规格值删除
      handleRemovedSpecValues(dto);
      
      // 注意：新增的规格值不会自动生成SKU组合
      // SKU的创建只在用户提供具体价格数据时进行
      log.debug("规格值变更处理：已处理删除操作，新增规格值将在价格项处理时按需创建SKU");
    }
    
    log.info("规格值变更处理完成，商品ID: {}", dto.getGoodsId());
  }

  /**
   * 处理被删除的规格值
   * 
   * 智能删除逻辑：
   * 1. 收集本次请求中price中使用的所有规格值
   * 2. 只删除那些在removedValues中且本次请求不再使用的规格值相关SKU
   * 3. 如果某个规格值在removedValues中，但本次请求中仍有价格数据在使用，则不删除
   * 
   * 这样可以避免误删历史SKU，保证业务连续性
   *
   * @param dto 日历价格库存数据传输对象
   */
  private void handleRemovedSpecValues(GoodsSkuCalendarPriceDTO dto) {
    log.debug("开始处理被删除的规格值，商品ID: {}", dto.getGoodsId());
    
    dto.getSpecChanges().getSpecValueChanges().forEach(change -> {
      if (!CollectionUtils.isEmpty(change.getRemovedValues())) {
        List<String> removedValueIds = change.getRemovedValues().stream()
            .map(GoodsSkuCalendarPriceDTO.SpecValue::getId)
            .collect(Collectors.toList());
        
        log.debug("检查删除规格值: {}, 商品ID: {}", removedValueIds, dto.getGoodsId());
        
        // 收集本次请求中所有使用的规格值（从价格数据中）
        Set<String> usedSpecValueIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(dto.getPriceList())) {
          dto.getPriceList().forEach(priceItem -> {
            if (!CollectionUtils.isEmpty(priceItem.getSpecValueIds())) {
              usedSpecValueIds.addAll(priceItem.getSpecValueIds());
            }
          });
        }
        
        // 过滤出真正需要删除的规格值（不在本次使用列表中的）
        List<String> actuallyRemovedValueIds = removedValueIds.stream()
            .filter(valueId -> !usedSpecValueIds.contains(valueId))
            .collect(Collectors.toList());
        
        if (!actuallyRemovedValueIds.isEmpty()) {
          log.debug("实际需要删除的规格值: {}, 商品ID: {}", actuallyRemovedValueIds, dto.getGoodsId());
          
          // 获取所有包含真正被删除规格值的SKU
          List<GoodsSkuSpecificationValue> skuSpecValues = skuSpecService.list(
              new QueryWrapper<GoodsSkuSpecificationValue>()
                  .eq("goods_id", dto.getGoodsId())
                  .in("spec_value_id", actuallyRemovedValueIds)
          );

          if (!skuSpecValues.isEmpty()) {
            List<String> skuIds = skuSpecValues.stream()
                .map(GoodsSkuSpecificationValue::getSkuId)
                .distinct()
                .collect(Collectors.toList());

            log.info("删除包含已删除规格值的SKU，SKU数量: {}, 规格值: {}, 商品ID: {}", 
                     skuIds.size(), actuallyRemovedValueIds, dto.getGoodsId());
            
            // 删除相关的日历价格数据
            this.remove(new QueryWrapper<GoodsSkuCalendarPrice>().in("sku_id", skuIds));
            
            // 删除SKU规格值关联
            skuSpecService.remove(new QueryWrapper<GoodsSkuSpecificationValue>().in("sku_id", skuIds));
            
            // 删除SKU
            goodsSkuService.removeByIds(skuIds);
          }
        } else {
          log.debug("所有被标记删除的规格值在本次请求中仍在使用，跳过删除操作，商品ID: {}", dto.getGoodsId());
        }
      }
    });
  }

  /**
   * 删除统一规格SKU及其相关数据
   *
   * @param goodsId 商品ID
   */
  private void removeUnifiedSpecSkus(String goodsId) {
    log.debug("开始删除统一规格SKU，商品ID: {}", goodsId);
    
    List<GoodsSku> oldUni = goodsSkuService.list(
        new QueryWrapper<GoodsSku>()
            .eq("goods_id", goodsId)
            .eq("has_specifications", 0)
    );
    
    if (!oldUni.isEmpty()) {
      List<String> ids = oldUni.stream().map(GoodsSku::getId).collect(Collectors.toList());
      log.info("删除统一规格SKU及其日历数据，SKU数量: {}, 商品ID: {}", ids.size(), goodsId);
      
      // 删除日历价格数据
      this.remove(new QueryWrapper<GoodsSkuCalendarPrice>().in("sku_id", ids));
      
      // 删除SKU
      goodsSkuService.removeByIds(ids);
    } else {
      log.debug("未找到统一规格SKU，跳过删除，商品ID: {}", goodsId);
    }
  }

  /**
   * 处理价格项的SKU创建和关联
   *
   * @param dto 日历价格库存数据传输对象
   */
  private void processSkuCreationForPriceItems(GoodsSkuCalendarPriceDTO dto) {
    log.debug("开始处理价格项SKU创建，价格项数量: {}, 商品ID: {}", dto.getPriceList().size(), dto.getGoodsId());
    
    dto.getPriceList().forEach(item -> {
      String skuId = item.getSkuId();
      
      // 如果没有指定skuId或SKU不存在，尝试根据规格值组合查找现有SKU
      if (CharSequenceUtil.isBlank(skuId) || goodsSkuService.getById(skuId) == null) {
        if (item.getSpecValueIds() != null && !item.getSpecValueIds().isEmpty()) {
          // 对specValueIds进行升序排序，保证查找一致性
          List<String> sortedSpecValueIds = new ArrayList<>(item.getSpecValueIds());
          Collections.sort(sortedSpecValueIds);
          // 根据规格值组合查找现有SKU
          skuId = findSkuBySpecCombination(dto.getGoodsId(), sortedSpecValueIds);
        }
        
        // 如果仍然没有找到SKU，创建新的
        if (CharSequenceUtil.isBlank(skuId)) {
          skuId = createSkuWithSpecValues(dto.getGoodsId(), item);
        }
        
        item.setSkuId(skuId);
      }
      
      // 更新SKU名称
      updateSkuNameIfNeeded(item.getSkuId(), item.getSkuName(), dto.getSkuName());
    });
    
    log.debug("价格项SKU创建处理完成，商品ID: {}", dto.getGoodsId());
  }

  /**
   * 根据规格值创建SKU
   *
   * @param goodsId 商品ID
   * @param item 价格项
   * @return SKU ID
   */
  private String createSkuWithSpecValues(String goodsId, GoodsSkuCalendarPrice item) {
    log.debug("创建新SKU，商品ID: {}", goodsId);
    
    // 根据规格值生成默认SKU名称
    String defaultSkuName = generateDefaultSkuName(item.getSpecValueIds());
    
    // 如果价格项中包含skuName，优先使用
    if (item.getSkuName() != null && !item.getSkuName().trim().isEmpty()) {
      defaultSkuName = item.getSkuName();
    }
    
    GoodsSku newSku = GoodsSku.builder()
        .goodsId(goodsId)
        .hasSpecifications(1)
        .deleteFlag(false)
        .skuCode(CharSequenceUtil.format("SKU-{}-{}", goodsId, UUID.randomUUID()))
        .skuName(defaultSkuName)
        .build();
    goodsSkuService.save(newSku);
    
    // 关联规格值
    if (item.getSpecValueIds() != null) {
      List<GoodsSkuSpecificationValue> rels = item.getSpecValueIds().stream()
          .map(valId -> {
            GoodsSpecificationValue v = specValueService.getById(valId);
            checkUtils.throwIfNull(v, "规格值 [{}] 不存在", valId);
            return GoodsSkuSpecificationValue.builder()
                .skuId(newSku.getId())
                .goodsId(goodsId)
                .specValueId(v.getId())
                .attrId(v.getAttrId())
                .travelId(SecurityUtils.getLoginUser().getUser().getTravelId())
                .build();
          })
          .collect(Collectors.toList());
      skuSpecService.saveBatch(rels);
    }
    
    log.debug("新SKU创建完成，SKU ID: {}, SKU名称: {}", newSku.getId(), defaultSkuName);
    return newSku.getId();
  }

  /**
   * 生成默认SKU名称
   *
   * @param specValueIds 规格值ID列表
   * @return 默认SKU名称
   */
  private String generateDefaultSkuName(List<String> specValueIds) {
    if (specValueIds == null || specValueIds.isEmpty()) {
      return "默认规格";
    }
    
    List<String> specValueNames = specValueIds.stream()
        .map(specValueId -> {
          GoodsSpecificationValue specValue = specValueService.getById(specValueId);
          return specValue != null ? specValue.getName() : "";
        })
        .filter(name -> !name.isEmpty())
        .collect(Collectors.toList());
    
    return specValueNames.isEmpty() ? "默认规格" : String.join("-", specValueNames);
  }

  /**
   * 如果需要，更新SKU名称
   *
   * @param skuId SKU ID
   * @param itemSkuName 价格项中的SKU名称
   * @param dtoSkuName DTO中的SKU名称
   */
  private void updateSkuNameIfNeeded(String skuId, String itemSkuName, String dtoSkuName) {
    String targetSkuName = null;
    
    // 优先使用价格项中的skuName
    if (itemSkuName != null && !itemSkuName.trim().isEmpty()) {
      targetSkuName = itemSkuName;
    } else if (dtoSkuName != null && !dtoSkuName.trim().isEmpty()) {
      // 如果价格项中没有skuName，但DTO中有，则使用DTO中的skuName
      targetSkuName = dtoSkuName;
    }
    
    if (targetSkuName != null) {
      GoodsSku existingSku = goodsSkuService.getById(skuId);
      if (existingSku != null && !targetSkuName.equals(existingSku.getSkuName())) {
        log.debug("更新SKU名称，SKU ID: {}, 原名称: {}, 新名称: {}", 
                 skuId, existingSku.getSkuName(), targetSkuName);
        existingSku.setSkuName(targetSkuName);
        goodsSkuService.updateById(existingSku);
      }
    }
  }

  /**
   * 保存或更新日历价格数据
   *
   * @param dto 日历价格库存数据传输对象
   * @return 保存的记录数
   */
  private int saveOrUpdateCalendarPrices(GoodsSkuCalendarPriceDTO dto) {
    log.debug("开始保存或更新日历价格数据，价格项数量: {}, 商品ID: {}", dto.getPriceList().size(), dto.getGoodsId());
    
    List<GoodsSkuCalendarPrice> toSaveOrUpdate = new ArrayList<>();
    int updateCount = 0;
    int insertCount = 0;
    
    for (GoodsSkuCalendarPrice item : dto.getPriceList()) {
      item.setGoodsId(dto.getGoodsId());
      item.setDeleteFlag(false);
      
      // 查询是否已存在相同SKU和日期的记录
      GoodsSkuCalendarPrice existing = this.getOne(
          new QueryWrapper<GoodsSkuCalendarPrice>()
              .eq("sku_id", item.getSkuId())
              .eq("calendar_date", item.getCalendarDate())
              .eq("delete_flag", false)
      );
      
      if (existing != null) {
        // 如果存在，更新现有记录
        existing.setAdultPrice(item.getAdultPrice());
        existing.setChildPrice(item.getChildPrice());
        existing.setCommission(item.getCommission());
        existing.setStock(item.getStock());
        existing.setStatus(item.getStatus());
        existing.setUpdateTime(DateUtils.getNowDate());
        toSaveOrUpdate.add(existing);
        updateCount++;
      } else {
        // 如果不存在，添加新记录
        item.setCreateTime(DateUtils.getNowDate());
        toSaveOrUpdate.add(item);
        insertCount++;
      }
    }
    
    boolean ok = this.saveOrUpdateBatch(toSaveOrUpdate, 200);
    checkUtils.throwIf(!ok, "批量保存多规格日历价格库存失败 for SPU [{}]", dto.getGoodsId());
    
    log.info("日历价格数据保存完成，商品ID: {}, 新增: {}, 更新: {}, 总计: {}", 
             dto.getGoodsId(), insertCount, updateCount, toSaveOrUpdate.size());
    
    return toSaveOrUpdate.size();
  }

  @Override
  public List<GoodsSkuCalendarPriceResponse> getCalendarPriceInfo(String goodsId, String specType) {
    // 1. 构建规格维度及值列表
    List<GoodsSkuCalendarPriceResponse.AttrListItem> attrList = new ArrayList<>();
    if ("1".equals(specType)) {
      List<GoodsSkuSpecificationValue> list =
          goodsSkuSpecificationValueService.list(
              new QueryWrapper<GoodsSkuSpecificationValue>()
                  .eq("goods_id", goodsId)
                  .eq("delete_flag", 0));

      if (!list.isEmpty()) {
        // 查询所有规格名
        List<GoodsSpecificationAttribute> attrs =
            specAttrService.list(
                new QueryWrapper<GoodsSpecificationAttribute>()
                    .in(
                        "id",
                        list.stream()
                            .map(GoodsSkuSpecificationValue::getAttrId)
                            .collect(Collectors.toSet()))
                    .eq("delete_flag", 0)
                    .orderByAsc("id"));

        List<GoodsSpecificationValue> values =
            specValueService.list(
                new QueryWrapper<GoodsSpecificationValue>()
                    .in(
                        "id",
                        list.stream()
                            .map(GoodsSkuSpecificationValue::getSpecValueId)
                            .collect(Collectors.toSet()))
                    .eq("delete_flag", 0)
                    .orderByAsc("id"));

        for (GoodsSpecificationAttribute attr : attrs) {
          List<GoodsSkuCalendarPriceResponse.AttrValue> vals =
              values.stream()
                  .filter(v -> v.getAttrId().equals(attr.getId()))
                  .map(
                      v -> {
                        GoodsSkuCalendarPriceResponse.AttrValue av =
                            new GoodsSkuCalendarPriceResponse.AttrValue();
                        av.setAttrId(String.valueOf(v.getId()));
                        av.setAttrValue(v.getName());
                        return av;
                      })
                  .collect(Collectors.toList());
          GoodsSkuCalendarPriceResponse.AttrListItem item =
              new GoodsSkuCalendarPriceResponse.AttrListItem();
          item.setAttrTitle(attr.getAttrName());
          item.setAttrValues(vals);
          attrList.add(item);
        }
      }
    }

    // 2. 构建 attrInfo 映射
    Map<String, GoodsSkuCalendarPriceResponse.AttrInfoItem> attrInfo = new LinkedHashMap<>();
    List<GoodsSku> skus =
        goodsSkuService.list(
            new QueryWrapper<GoodsSku>()
                .eq("goods_id", goodsId)
                .eq("delete_flag", 0)
                .eq("has_specifications", specType)
                .orderByAsc("id"));
    for (GoodsSku sku : skus) {
      // 获取规格值关联
      List<GoodsSkuSpecificationValue> rels =
          skuSpecService.list(
              new QueryWrapper<GoodsSkuSpecificationValue>()
                  .eq("sku_id", sku.getId())
                  .eq("goods_id", goodsId)
                  .orderByAsc("attr_id"));
      List<String> valIds =
          rels.stream()
              .map(GoodsSkuSpecificationValue::getSpecValueId)
              .collect(Collectors.toList());
      // 对valIds进行升序排序，保证key一致性
      Collections.sort(valIds);
      String key = "1".equals(specType) ? Joiner.on("-").skipNulls().join(valIds) : sku.getId();

      // 查询日历价格库存
      List<GoodsSkuCalendarPrice> cps =
          this.list(
              new QueryWrapper<GoodsSkuCalendarPrice>()
                  .eq("goods_id", goodsId)
                  .eq("sku_id", sku.getId())
                  .eq("delete_flag", 0)
                  .orderByAsc("calendar_date"));
      // 组装 CalendarPrice
      List<GoodsSkuCalendarPriceResponse.CalendarPrice> cpResp =
          cps.stream()
              .map(
                  cp -> {
                    GoodsSkuCalendarPriceResponse.CalendarPrice cpItem =
                        new GoodsSkuCalendarPriceResponse.CalendarPrice();
                    cpItem.setDate(DateUtil.formatDate(cp.getCalendarDate()));
                    GoodsSkuCalendarPriceResponse.PriceDetail pd =
                        new GoodsSkuCalendarPriceResponse.PriceDetail();
                    pd.setPrice(cp.getAdultPrice());
                    pd.setChildPrice(cp.getChildPrice());
                    pd.setCommission(cp.getCommission());
                    cpItem.setPrices(Collections.singletonList(pd));
                    cpItem.setStatus(cp.getStatus());
                    cpItem.setStock(cp.getStock().intValue());
                    cpItem.setStockId(String.valueOf(cp.getId()));
                    return cpItem;
                  })
              .collect(Collectors.toList());

      // 计算默认价格与总库存
      String defaultPrice = cps.isEmpty() ? "" : cps.get(0).getAdultPrice().toString();
      String totalStock =
          String.valueOf(cps.stream().mapToLong(GoodsSkuCalendarPrice::getStock).sum());

      GoodsSkuCalendarPriceResponse.AttrInfoItem ai =
          new GoodsSkuCalendarPriceResponse.AttrInfoItem();
      ai.setId(sku.getId());
      ai.setSkuName(sku.getSkuName() != null ? sku.getSkuName() : "");
      ai.setPrice(defaultPrice);
      ai.setStock(totalStock);
      ai.setCalendarPrice(cpResp);
      attrInfo.put(key, ai);
    }

    // 3. 构建全局日历区间
    List<Date> allDates =
        this.list(
                new QueryWrapper<GoodsSkuCalendarPrice>()
                    .eq("goods_id", goodsId)
                    .eq("delete_flag", 0))
            .stream()
            .map(GoodsSkuCalendarPrice::getCalendarDate)
            .collect(Collectors.toList());
    long start = allDates.stream().mapToLong(Date::getTime).min().orElse(0);
    long end = allDates.stream().mapToLong(Date::getTime).max().orElse(0);
    GoodsSkuCalendarPriceResponse.CalendarInfo calInfo =
        new GoodsSkuCalendarPriceResponse.CalendarInfo();
    calInfo.setStartDate(start);
    calInfo.setEndDate(end);

    // 4. 组装响应对象
    GoodsSkuCalendarPriceResponse resp = new GoodsSkuCalendarPriceResponse();
    resp.setAttrList(attrList);
    resp.setAttrInfo(attrInfo);
    resp.setCalendarInfo(calInfo);
    return Collections.singletonList(resp);
  }

  @Override
  public Map<String, Map<String, BigDecimal>> getMinMaxPriceByGoodsIds(List<String> goodsIds) {
    if (CollectionUtils.isEmpty(goodsIds)) {
      return Collections.emptyMap();
    }

    // 查询所有商品的价格数据
    List<GoodsSkuCalendarPrice> prices =
        this.list(
            new QueryWrapper<GoodsSkuCalendarPrice>()
                .in("goods_id", goodsIds)
                .eq("delete_flag", false)
                .orderByAsc("goods_id")
                .orderByAsc("adult_price"));

    // 按商品ID分组
    Map<String, List<GoodsSkuCalendarPrice>> pricesByGoodsId = prices.stream()
        .collect(Collectors.groupingBy(GoodsSkuCalendarPrice::getGoodsId));

    // 计算每个商品的最低和最高价
    Map<String, Map<String, BigDecimal>> result = new HashMap<>();
    for (Map.Entry<String, List<GoodsSkuCalendarPrice>> entry : pricesByGoodsId.entrySet()) {
      String goodsId = entry.getKey();
      List<GoodsSkuCalendarPrice> goodsPrices = entry.getValue();

      if (!goodsPrices.isEmpty()) {
        Map<String, BigDecimal> priceRange = new HashMap<>();
        // 获取最低价
        BigDecimal minPrice = goodsPrices.stream()
            .map(GoodsSkuCalendarPrice::getAdultPrice)
            .min(BigDecimal::compareTo)
            .orElse(BigDecimal.ZERO);
        // 获取最高价
        BigDecimal maxPrice = goodsPrices.stream()
            .map(GoodsSkuCalendarPrice::getAdultPrice)
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.ZERO);

        priceRange.put("minPrice", minPrice);
        priceRange.put("maxPrice", maxPrice);
        result.put(goodsId, priceRange);
      }
    }

    return result;
  }

  /**
   * 生成规格值组合的笛卡尔积
   */
  @Override
  public List<List<GoodsSkuCalendarPriceDTO.SpecValue>> generateSpecCombinations(
      Map<String, List<GoodsSkuCalendarPriceDTO.SpecValue>> specValuesByAttr) {
    List<List<GoodsSkuCalendarPriceDTO.SpecValue>> result = new ArrayList<>();
    List<List<GoodsSkuCalendarPriceDTO.SpecValue>> attrValuesList = new ArrayList<>(specValuesByAttr.values());
    
    if (attrValuesList.isEmpty()) {
      return result;
    }
    
    generateCombinationsRecursive(attrValuesList, 0, new ArrayList<>(), result);
    return result;
  }

  /**
   * 递归生成组合
   */
  private void generateCombinationsRecursive(
      List<List<GoodsSkuCalendarPriceDTO.SpecValue>> attrValuesList,
      int currentAttrIndex,
      List<GoodsSkuCalendarPriceDTO.SpecValue> currentCombination,
      List<List<GoodsSkuCalendarPriceDTO.SpecValue>> result) {
    
    if (currentAttrIndex == attrValuesList.size()) {
      result.add(new ArrayList<>(currentCombination));
      return;
    }
    
    List<GoodsSkuCalendarPriceDTO.SpecValue> currentAttrValues = attrValuesList.get(currentAttrIndex);
    for (GoodsSkuCalendarPriceDTO.SpecValue value : currentAttrValues) {
      currentCombination.add(value);
      generateCombinationsRecursive(attrValuesList, currentAttrIndex + 1, currentCombination, result);
      currentCombination.remove(currentCombination.size() - 1);
    }
  }

  /**
   * 检查是否已存在相同规格组合的SKU
   */
  @Override
  public boolean checkSkuExistsForSpecCombination(String goodsId, List<String> specValueIds) {
    if (CollectionUtils.isEmpty(specValueIds)) {
      return false;
    }
    
    // 查询所有该商品的多规格SKU
    List<GoodsSku> skus = goodsSkuService.list(
        new QueryWrapper<GoodsSku>()
            .eq("goods_id", goodsId)
            .eq("has_specifications", 1)
            .eq("delete_flag", false)
    );
    
    for (GoodsSku sku : skus) {
      // 查询该SKU关联的规格值
      List<GoodsSkuSpecificationValue> skuSpecs = skuSpecService.list(
          new QueryWrapper<GoodsSkuSpecificationValue>()
              .eq("sku_id", sku.getId())
              .eq("goods_id", goodsId)
      );
      
      List<String> skuSpecValueIds = skuSpecs.stream()
          .map(GoodsSkuSpecificationValue::getSpecValueId)
          .sorted()
          .collect(Collectors.toList());
      
      List<String> targetSpecValueIds = new ArrayList<>(specValueIds);
      targetSpecValueIds.sort(String::compareTo);
      
      // 如果规格值ID列表完全匹配，说明SKU已存在
      if (skuSpecValueIds.equals(targetSpecValueIds)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 根据规格值组合查找对应的SKU ID
   */
  @Override
  public String findSkuBySpecCombination(String goodsId, List<String> specValueIds) {
    if (CollectionUtils.isEmpty(specValueIds)) {
      return null;
    }
    
    // 查询所有该商品的多规格SKU
    List<GoodsSku> skus = goodsSkuService.list(
        new QueryWrapper<GoodsSku>()
            .eq("goods_id", goodsId)
            .eq("has_specifications", 1)
            .eq("delete_flag", false)
    );
    
    for (GoodsSku sku : skus) {
      // 查询该SKU关联的规格值
      List<GoodsSkuSpecificationValue> skuSpecs = skuSpecService.list(
          new QueryWrapper<GoodsSkuSpecificationValue>()
              .eq("sku_id", sku.getId())
              .eq("goods_id", goodsId)
      );
      
      List<String> skuSpecValueIds = skuSpecs.stream()
          .map(GoodsSkuSpecificationValue::getSpecValueId)
          .sorted()
          .collect(Collectors.toList());
      
      List<String> targetSpecValueIds = new ArrayList<>(specValueIds);
      targetSpecValueIds.sort(String::compareTo);
      
      // 如果规格值ID列表完全匹配，返回该SKU的ID
      if (skuSpecValueIds.equals(targetSpecValueIds)) {
        return sku.getId();
      }
    }
    
    return null;
  }
}
