/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joolun.mall.dto.OrderInfoDTO;
import com.joolun.mall.entity.OrderInfo;
import com.joolun.mall.entity.OrderTravelers;
import com.joolun.mall.entity.TravelOrderStat;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
public interface OrderInfoMapper extends BaseMapper<OrderInfo> {

	IPage<OrderInfo> selectPage1(IPage<OrderInfo> page, @Param("query") OrderInfo orderInfo);

	IPage<OrderInfo> selectPage2(IPage<OrderInfo> page, @Param("query") OrderInfo orderInfo);

	IPage<OrderInfo> selectByPage(IPage<OrderInfo> page, @Param("query") OrderInfo orderInfo);

	OrderInfo selectById2(Serializable id);

	/**
	 * 查询订单出行人
	 *
	 * @param id 订单出行人ID
	 * @return 订单出行人
	 */
	public OrderTravelers selectOrderTravelersById(String id);

	/**
	 * 查询订单出行人列表
	 *
	 * @param orderTravelers 订单出行人
	 * @return 订单出行人集合
	 */
	public List<OrderTravelers> selectOrderTravelersList(OrderTravelers orderTravelers);

	/**
	 * 新增订单出行人
	 *
	 * @param orderTravelers 订单出行人
	 * @return 结果
	 */
	public int insertOrderTravelers(OrderTravelers orderTravelers);

	/**
	 * 修改订单出行人
	 *
	 * @param orderTravelers 订单出行人
	 * @return 结果
	 */
	public int updateOrderTravelers(OrderTravelers orderTravelers);

	/**
	 * 删除订单出行人
	 *
	 * @param id 订单出行人ID
	 * @return 结果
	 */
	public int deleteOrderTravelersById(String id);

	/**
	 * 批量删除订单出行人
	 *
	 * @param ids 需要删除的数据ID
	 * @return 结果
	 */
	public int deleteOrderTravelersByIds(String[] ids);

	/**
	 * 批量删除订单出行人
	 *
	 * @param ids 需要删除的数据orderID
	 * @return 结果
	 */
	public int deleteOrderTravelersByOrderIds(String[] ids);

	/**
	 * 批量新增订单出行人
	 *
	 * @param orderTravelersList 订单出行人列表
	 * @return 结果
	 */
	public int batchOrderTravelers(List<OrderTravelers> orderTravelersList);


	/**
	 * 通过订单ID删除订单出行人信息
	 *
	 * @param id ID
	 * @return 结果
	 */
	public int deleteOrderTravelersByOrderId(String id);

	IPage<TravelOrderStat> selectOrderStatPage(IPage<TravelOrderStat> page, @Param("query") OrderInfo orderInfo);


	List<OrderInfoDTO> getOrderInfoList(@Param("query")OrderInfo orderInfo);


}
