package com.joolun.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.*;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.joolun.common.annotation.Excel;
import com.joolun.common.exception.CustomException;
import com.joolun.common.utils.DateUtils;
import com.joolun.mall.dto.OrderCreationContext;
import com.joolun.mall.service.process.OrderCreationPipeline;
import com.joolun.common.utils.sms.SMSUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.config.MallConfigProperties;
import com.joolun.mall.constant.MallConstants;
import com.joolun.mall.dto.*;
import com.joolun.mall.entity.*;
import com.joolun.mall.enums.OrderInfoEnum;
import com.joolun.mall.enums.OrderInfoEvaluateEnum;
import com.joolun.mall.enums.OrderLogisticsEnum;
import com.joolun.mall.enums.WalletEnum;
import com.joolun.mall.mapper.OrderInfoMapper;
import com.joolun.mall.service.*;
import com.joolun.system.service.ISysConfigService;
import com.joolun.weixin.config.WxPayConfiguration;
import com.joolun.weixin.entity.WxUser;
import com.joolun.weixin.entity.WxUserLevel;
import com.joolun.weixin.service.IWxUserLevelService;
import com.joolun.weixin.service.WxUserService;
import com.joolun.framework.utils.ThirdSessionHolder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderInfoServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfo> implements OrderInfoService {

	// 常量定义
	private static final BigDecimal COMMISSION_RATE_MULTIPLIER = new BigDecimal("0.1");
	private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	private static final String DEFAULT_CREATOR = "admin";
	private static final long DEFAULT_ORDER_TIMEOUT_MINUTES = MallConstants.ORDER_TIME_OUT_0;

	private final GoodsSpuService goodsSpuService;
	private final ShoppingCartService shoppingCartService;
	private final UserAddressService userAddressService;
	private final RedisTemplate<String, String> redisTemplate;

	private final OrderItemService orderItemService;
	private final OrderLogisticsService orderLogisticsService;
	private final MallConfigProperties mallConfigProperties;
	private final WxUserService wxUserService;
	private final ISysUserWalletService sysUserWalletService;

	private final ISysConfigService sysConfigService;
	private final IUserCertInfoService userCertInfoService;

	@Resource
	private final OrderInfoMapper orderInfoMapper;

	private final IWxUserLevelService wxUserLevelService;
	private final OrderCreationPipeline orderCreationPipeline;
	private final IGoodsSkuCalendarPriceService goodsSkuCalendarPriceService;
	private final IGoodsSkuService goodsSkuService;
	@Autowired
    private OrderItemServiceImpl orderItemServiceImpl;


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateById(OrderInfo entity) {
		if(StrUtil.isNotBlank(entity.getLogistics()) && StrUtil.isNotBlank(entity.getLogisticsNo())){//发货。更新快递单号
			entity.setDeliveryTime(DateUtils.getNowDate());
			OrderLogistics orderLogistics = orderLogisticsService.getOne(Wrappers.<OrderLogistics>lambdaQuery()
					.eq(OrderLogistics::getId,entity.getLogisticsId()));
			//第一次发货调起收到倒计时
			boolean sendRedis = false;
			if(StrUtil.isBlank(orderLogistics.getLogistics()) && StrUtil.isBlank(orderLogistics.getLogisticsNo())){
				sendRedis = true;
			}
			orderLogistics.setLogistics(entity.getLogistics());
			orderLogistics.setLogisticsNo(entity.getLogisticsNo());
			orderLogistics.setStatus(OrderLogisticsEnum.STATUS_1.getValue());
			orderLogisticsService.updateById(orderLogistics);
			if(sendRedis){
				//加入redis，7天后自动确认收货
				String keyRedis = String.valueOf(StrUtil.format("{}:{}",MallConstants.REDIS_ORDER_KEY_STATUS_2,entity.getId()));
				redisTemplate.opsForValue().set(keyRedis, entity.getOrderNo() , MallConstants.ORDER_TIME_OUT_2, TimeUnit.DAYS);//设置过期时间
			}
		}
		return super.updateById(entity);
	}

  @Override
  public IPage<OrderInfo> page1(IPage<OrderInfo> page, Wrapper<OrderInfo> queryWrapper) {
	  OrderInfo orderInfo = queryWrapper.getEntity();
	  // 预处理多状态查询参数
	  preprocessMultiStatusQuery(orderInfo);
    IPage<OrderInfo> orderInfoIPage = baseMapper.selectPage1(page, orderInfo);
    orderInfoIPage
        .getRecords()
        .forEach(
            item -> {
              // 安全处理：检查orderTravelersList是否为null
              if (item.getOrderTravelersList() != null) {
                item.setAdultQuantity(
                    (int)
                        item.getOrderTravelersList().stream()
                            .filter(Objects::nonNull) // 过滤null元素
                            .filter(user -> "1".equals(user.getUserType()))
                            .count());

                item.setChildrenQuantity(
                    (int)
                        item.getOrderTravelersList().stream()
                            .filter(Objects::nonNull) // 过滤null元素
                            .filter(user -> "0".equals(user.getUserType()))
                            .count());
              } else {
                // 如果orderTravelersList为null，设置默认值
                item.setAdultQuantity(0);
                item.setChildrenQuantity(0);
              }
            });
    return orderInfoIPage;
  }

  @Override
  public IPage<OrderInfoAppDTO> selectByPageForApp(IPage<OrderInfo> page, OrderInfo orderInfo) {
    // 预处理多状态查询参数
    preprocessMultiStatusQuery(orderInfo);
    IPage<OrderInfo> result = baseMapper.selectByPage(page, orderInfo);
    IPage<OrderInfoAppDTO> voPage = new Page<>();
    BeanUtils.copyProperties(result, voPage, "records");
    if (ObjectUtil.isEmpty(result.getRecords())) {
      return voPage;
    }

    List<OrderItem> orderItems =
        orderItemService.listByOrderIds(
            result.getRecords().stream().map(OrderInfo::getId).collect(Collectors.toList()));

    Map<String, OrderItem> orderItemsMap =
        orderItems.stream()
            .collect(Collectors.toMap(OrderItem::getOrderId, a -> a, (k1, k2) -> k1));

    List<OrderInfoAppDTO> orderInfoAppDTOS =
        BeanUtil.copyToList(result.getRecords(), OrderInfoAppDTO.class);

    orderInfoAppDTOS.forEach(
        item -> {
          if (ObjectUtil.isNotEmpty(orderItemsMap.get(item.getId()))) {
            OrderItem orderItem = orderItemsMap.get(item.getId());
            item.setAdultQuantity(orderItem.getAdultQuantity());
            item.setChildrenQuantity(orderItem.getYoungChildQuantity());
            item.setPicUrl(orderItem.getPicUrl());
            item.setDepartureDate(orderItem.getDepartureDate());
          }
        });
    voPage.setRecords(orderInfoAppDTOS);
    return voPage;
  }

  /**
   * 预处理多状态查询参数
   * 将逗号分隔的状态值（如："0,1,2"）转换为状态列表
   * 
   * @param orderInfo 订单查询条件
   */
  private void preprocessMultiStatusQuery(OrderInfo orderInfo) {
    String status = orderInfo.getStatus();
    
    // 卫语句：如果status为空或不包含逗号，直接返回
    if (StrUtil.isBlank(status) || !status.contains(",")) {
      return;
    }
    
    log.info("[MULTI_STATUS_QUERY] 检测到多状态查询: status={}", status);
    
    // 解析逗号分隔的状态值
    String[] statusArray = status.split(",");
    List<String> statusList = new ArrayList<>();
    
    for (String statusItem : statusArray) {
      String trimmedStatus = statusItem.trim();
      if (StrUtil.isNotBlank(trimmedStatus)) {
        statusList.add(trimmedStatus);
      }
    }
    
    // 设置状态列表到orderInfo中，供XML mapper使用
    if (!statusList.isEmpty()) {
      orderInfo.setStatusList(statusList);
      // 清空原status字段，避免XML中的单状态逻辑被触发
      orderInfo.setStatus(null);
      log.info("[MULTI_STATUS_QUERY] 状态列表已设置: statusList={}", statusList);
    }
  }

	@Override
	public IPage<OrderInfo> page2(IPage<OrderInfo> page, OrderInfo orderInfo) {
		return baseMapper.selectPage2(page,orderInfo);
	}

	@Deprecated
	@Override
	public OrderInfo getById2(Serializable id) {
		log.info("[ORDER_QUERY] 开始查询订单详情, orderId: {}", id);

		OrderInfo orderInfo = baseMapper.selectById2(id);
		if(orderInfo != null){
			// 增强订单项信息 - 适配新的SKU结构
			enhanceOrderItemsWithSkuInfo(orderInfo);

			String keyRedis = null;
			//获取自动取消倒计时
			if(CommonConstants.NO.equals(orderInfo.getIsPay())){
				keyRedis = String.valueOf(StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_IS_PAY_0,orderInfo.getId()));
			}
//			//获取自动收货倒计时
//			if(OrderInfoEnum.STATUS_2.getValue().equals(orderInfo.getStatus())){
//				keyRedis = String.valueOf(StrUtil.format("{}:{}",MallConstants.REDIS_ORDER_KEY_STATUS_2,orderInfo.getId()));
//			}
			if(keyRedis != null){
				Long outTime = redisTemplate.getExpire(keyRedis);
				if(outTime != null && outTime > 0){
					orderInfo.setOutTime(outTime);
				} else {
					// 检查并处理超时订单状态
					handleTimeoutOrderStatus(orderInfo);
				}
			}

			log.info("[ORDER_QUERY] 订单查询完成, orderId: {}, orderItems: {}",
				id, orderInfo.getListOrderItem() != null ? orderInfo.getListOrderItem().size() : 0);
		} else {
			log.warn("[ORDER_QUERY] 订单不存在, orderId: {}", id);
		}
		return orderInfo;
	}

  @Override
  public OrderItemAppDTO getOrderItemAppByOrderId(Serializable id) {
    log.info("[ORDER_QUERY] 开始查询订单详情, orderId: {}", id);

    OrderInfo orderInfo = baseMapper.selectById2(id);
    if (ObjectUtil.isNull(orderInfo)) {
      log.warn("[ORDER_QUERY] 订单不存在, orderId: {}", id);
      throw new CustomException("订单不存在");
    }

    OrderItem orderItem = orderItemService.selectOneByOrderId(orderInfo.getId());
    if (ObjectUtil.isNull(orderItem)) {
      log.warn("[ORDER_ITEM_QUERY] 订单详情不存在, orderId: {}", id);
      throw new CustomException("订单详情不存在");
    }
    OrderItemAppDTO orderItemAppDTO = BeanUtil.copyProperties(orderInfo, OrderItemAppDTO.class);
    orderItemAppDTO.setId(orderItem.getId());
	orderItemAppDTO.setOrderId(orderItem.getOrderId());
    orderItemAppDTO.setQuantity(orderItem.getQuantity());
    orderItemAppDTO.setAdultQuantity(orderItem.getAdultQuantity());
    orderItemAppDTO.setChildQuantity(orderItem.getYoungChildQuantity());
    orderItemAppDTO.setPicUrl(orderItem.getPicUrl());
    orderItemAppDTO.setDepartureDate(orderItem.getDepartureDate());
	orderItemAppDTO.setSkuId(orderItem.getSkuId());
	orderItemAppDTO.setSkuName(orderItem.getSkuName());
	orderItemAppDTO.setSpuId(orderItem.getSpuId());
	orderItemAppDTO.setSpuName(orderItem.getSpuName());
	orderItemAppDTO.setSevenDayRefund(orderItem.getSevenDayRefund());
	orderItemAppDTO.setFifteenDayRefun(orderItem.getFifteenDayRefun());
	orderItemAppDTO.setFortyEightHoursRefun(orderItem.getFortyEightHoursRefun());
	orderItemAppDTO.setTwentyFourHoursRefun(orderItem.getTwentyFourHoursRefun());

    // 设置出发日期对应的周几
    if (orderItem.getDepartureDate() != null) {
      orderItemAppDTO.setDayOfWeek(DateUtils.getDayOfWeek(orderItem.getDepartureDate()));
    }

    // 获取商品卖点信息
    if (orderItem.getSpuId() != null) {
      GoodsSpu goodsSpu = goodsSpuService.getById(orderItem.getSpuId());
      if (goodsSpu != null) {
        orderItemAppDTO.setSellPoint(goodsSpu.getSellPoint());
      }
    }

    Map<String, List<UserCertInfo>> userCertInfoMap =
        userCertInfoService.selectOrderTravelersByOrderIds(
            Collections.singletonList(orderInfo.getId()));
    orderItemAppDTO.setOrderItemTravelersAppDTO(
        BeanUtil.copyToList(
            userCertInfoMap.get(orderInfo.getId()), OrderItemTravelersAppDTO.class));

    String keyRedis = null;
    // 获取自动取消倒计时
    if (CommonConstants.NO.equals(orderInfo.getIsPay())) {
      keyRedis =
          String.valueOf(
              StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_IS_PAY_0, orderInfo.getId()));
    }
    if (keyRedis != null) {
      Long outTime = redisTemplate.getExpire(keyRedis);
      if (outTime > 0) {
        orderItemAppDTO.setOutTime(outTime);
      } else {
        // 检查并处理超时订单状态
        handleTimeoutOrderStatus(orderInfo);
        // 如果订单状态被更新，需要重新设置到DTO中
        orderItemAppDTO.setStatus(orderInfo.getStatus());
        orderItemAppDTO.setStatusDesc(orderInfo.getStatusDesc());
        orderItemAppDTO.setOutTime(orderInfo.getOutTime());
      }
    }

    log.info("[ORDER_QUERY] 订单查询完成");
    return orderItemAppDTO;
  }

  /**
   * 增强订单项信息 - 适配新的SKU结构
   *
   * @param orderInfo 订单信息
   */
  private void enhanceOrderItemsWithSkuInfo(OrderInfo orderInfo) {
		// 卫语句：检查订单项列表
		if (orderInfo.getListOrderItem() == null || orderInfo.getListOrderItem().isEmpty()) {
			return;
		}
		
		orderInfo.getListOrderItem().forEach(this::enhanceSingleOrderItem);
	}
	
	/**
	 * 增强单个订单项的SKU信息
	 */
	private void enhanceSingleOrderItem(OrderItem orderItem) {
		log.debug("[ORDER_QUERY] 处理订单项: itemId={}, skuId={}, spuId={}", 
			orderItem.getId(), orderItem.getSkuId(), orderItem.getSpuId());
		
		// 卫语句：订单项没有SKU信息
		if (orderItem.getSkuId() == null) {
			log.debug("[ORDER_QUERY] 订单项缺少SKU信息: itemId={}, 可能是历史订单", orderItem.getId());
			return;
		}
		
		try {
			// 获取并设置SKU基本信息
			enhanceSkuBasicInfo(orderItem);
			
			// 获取并验证SKU价格信息
			enhanceSkuPriceInfo(orderItem);
			
		} catch (Exception e) {
			log.error("[ORDER_QUERY] 获取SKU信息失败: skuId={}, error={}", 
				orderItem.getSkuId(), e.getMessage(), e);
		}
	}
	
	/**
	 * 增强SKU基本信息
	 */
	private void enhanceSkuBasicInfo(OrderItem orderItem) {
		GoodsSku goodsSku = goodsSkuService.getById(orderItem.getSkuId());
		
		// 卫语句：SKU不存在
		if (goodsSku == null) {
			log.warn("[ORDER_QUERY] SKU不存在: skuId={}", orderItem.getSkuId());
			return;
		}
		
		// 补全SKU名称
		if (orderItem.getSkuName() == null) {
			orderItem.setSkuName(goodsSku.getSkuName());
		}
		
		log.debug("[ORDER_QUERY] SKU信息已补全: skuId={}, skuName={}", 
			goodsSku.getId(), goodsSku.getSkuName());
	}
	
	/**
	 * 增强SKU价格信息并进行价格验证
	 */
	private void enhanceSkuPriceInfo(OrderItem orderItem) {
		// 卫语句：没有出发日期
		if (orderItem.getDepartureDate() == null) {
			return;
		}
		
		List<GoodsSkuCalendarPrice> calendarPrices = goodsSkuCalendarPriceService.list(
			Wrappers.<GoodsSkuCalendarPrice>lambdaQuery()
				.eq(GoodsSkuCalendarPrice::getSkuId, orderItem.getSkuId())
				.eq(GoodsSkuCalendarPrice::getCalendarDate, orderItem.getDepartureDate())
				.eq(GoodsSkuCalendarPrice::getDeleteFlag, false)
		);
		
		// 卫语句：未找到价格信息
		if (calendarPrices.isEmpty()) {
			log.debug("[ORDER_QUERY] 未找到SKU日历价格信息: skuId={}, date={}", 
				orderItem.getSkuId(), orderItem.getDepartureDate());
			return;
		}
		
		GoodsSkuCalendarPrice calendarPrice = calendarPrices.get(0);
		log.debug("[ORDER_QUERY] 找到SKU日历价格信息: skuId={}, date={}, stock={}, adultPrice={}", 
			orderItem.getSkuId(), orderItem.getDepartureDate(), 
			calendarPrice.getStock(), calendarPrice.getAdultPrice());
		
		// 价格差异检测
		validatePriceDifference(orderItem, calendarPrice);
	}
	
	/**
	 * 验证价格差异
	 */
	private void validatePriceDifference(OrderItem orderItem, GoodsSkuCalendarPrice calendarPrice) {
		// 卫语句：价格信息不完整
		if (orderItem.getSalesPrice() == null || calendarPrice.getAdultPrice() == null) {
			return;
		}
		
		if (orderItem.getSalesPrice().compareTo(calendarPrice.getAdultPrice()) != 0) {
			log.info("[ORDER_QUERY] 价格差异检测: itemId={}, 订单价格={}, SKU价格={}", 
				orderItem.getId(), orderItem.getSalesPrice(), calendarPrice.getAdultPrice());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void orderCancel(OrderInfo orderInfo) {
		log.info("[ORDER_CANCEL] 开始取消订单, orderId: {}, isPay: {}, status: {}", 
			orderInfo.getId(), orderInfo.getIsPay(), orderInfo.getStatus());
		
		// 卫语句：检查订单是否可以取消
		if (!CommonConstants.NO.equals(orderInfo.getIsPay())) {
			log.warn("[ORDER_CANCEL] 订单已支付，无法取消: orderId={}, isPay={}", 
				orderInfo.getId(), orderInfo.getIsPay());
			return;
		}
		
		if (OrderInfoEnum.STATUS_5.getValue().equals(orderInfo.getStatus())) {
			log.warn("[ORDER_CANCEL] 订单已是取消状态: orderId={}, status={}", 
				orderInfo.getId(), orderInfo.getStatus());
			return;
		}
		
		// 设置订单状态为已取消
		orderInfo.setStatus(OrderInfoEnum.STATUS_5.getValue());
		
		// 获取订单项并回滚库存
		List<OrderItem> listOrderItem = orderItemService.list(Wrappers.<OrderItem>lambdaQuery()
				.eq(OrderItem::getOrderId, orderInfo.getId()));
		
		log.info("[ORDER_CANCEL] 找到 {} 个订单项需要回滚库存", listOrderItem.size());
		
		// 处理每个订单项的库存回滚
		listOrderItem.forEach(this::rollbackOrderItemStock);
		
		// 更新所有订单项状态为已取消
		listOrderItem.forEach(orderItem -> {
			orderItem.setStatus(OrderInfoEnum.STATUS_5.getValue());
		});
		orderItemService.updateBatchById(listOrderItem);
		log.info("[ORDER_CANCEL] 订单项状态已更新为已取消: orderId={}, itemCount={}", 
			orderInfo.getId(), listOrderItem.size());
		
		// 更新订单状态
		baseMapper.updateById(orderInfo);
		
		// 删除Redis中的订单超时键，避免重复触发取消逻辑
		String redisKey = String.valueOf(StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_IS_PAY_0, orderInfo.getId()));
		try {
			Boolean deleted = redisTemplate.delete(redisKey);
			log.info("[ORDER_CANCEL] Redis键删除结果: key={}, deleted={}", redisKey, deleted);
		} catch (Exception e) {
			log.warn("[ORDER_CANCEL] Redis键删除失败: key={}, error={}", redisKey, e.getMessage());
			// 不抛异常，避免影响主流程
		}
		
		log.info("[ORDER_CANCEL] 订单取消完成: orderId={}", orderInfo.getId());
	}
	
	/**
	 * 回滚单个订单项的库存
	 */
	private void rollbackOrderItemStock(OrderItem orderItem) {
		log.info("[ORDER_CANCEL] 处理订单项: itemId={}, skuId={}, spuId={}, quantity={}, departureDate={}", 
			orderItem.getId(), orderItem.getSkuId(), orderItem.getSpuId(), 
			orderItem.getQuantity(), orderItem.getDepartureDate());
		
		// 卫语句：优先尝试SKU库存回滚
		if (orderItem.getSkuId() != null && orderItem.getDepartureDate() != null) {
			if (rollbackSkuStock(orderItem)) {
				return; // SKU库存回滚成功，直接返回
			}
		}
		
		// 降级处理：使用SPU库存回滚
		log.info("[ORDER_CANCEL] 使用SPU库存回滚: itemId={}", orderItem.getId());
		fallbackToSpuStockRollback(orderItem);
	}
	
	/**
	 * 回滚SKU库存
	 * @return true-成功, false-失败
	 */
	private boolean rollbackSkuStock(OrderItem orderItem) {
		try {
			List<GoodsSkuCalendarPrice> calendarPrices = goodsSkuCalendarPriceService.list(
				Wrappers.<GoodsSkuCalendarPrice>lambdaQuery()
					.eq(GoodsSkuCalendarPrice::getSkuId, orderItem.getSkuId())
					.eq(GoodsSkuCalendarPrice::getCalendarDate, orderItem.getDepartureDate())
					.eq(GoodsSkuCalendarPrice::getDeleteFlag, false)
			);
			
			// 卫语句：未找到对应的SKU日历价格记录
			if (calendarPrices.isEmpty()) {
				log.warn("[ORDER_CANCEL] 未找到对应的SKU日历价格记录: skuId={}, date={}", 
					orderItem.getSkuId(), orderItem.getDepartureDate());
				return false;
			}
			
			// 回滚库存
			GoodsSkuCalendarPrice calendarPrice = calendarPrices.get(0);
			Integer originalStock = calendarPrice.getStock();
			calendarPrice.setStock(originalStock + orderItem.getQuantity());
			
			goodsSkuCalendarPriceService.updateById(calendarPrice);
			
			log.info("[ORDER_CANCEL] SKU库存回滚成功: skuId={}, date={}, 库存 {} -> {}", 
				orderItem.getSkuId(), orderItem.getDepartureDate(), 
				originalStock, calendarPrice.getStock());
			
			return true;
			
		} catch (Exception e) {
			log.error("[ORDER_CANCEL] SKU库存回滚失败: skuId={}, error={}", 
				orderItem.getSkuId(), e.getMessage(), e);
			return false;
		}
	}
	
	/**
	 * 降级处理：回滚到商品SPU库存
	 * @param orderItem 订单项
	 */
	private void fallbackToSpuStockRollback(OrderItem orderItem) {
		try {
			GoodsSpu goodsSpu = goodsSpuService.getById(orderItem.getSpuId());
			if (goodsSpu != null) {
				Integer originalStock = goodsSpu.getStock();
				goodsSpu.setStock(originalStock + orderItem.getQuantity());
				goodsSpuService.updateById(goodsSpu);
				
				log.info("[ORDER_CANCEL] SPU库存回滚成功: spuId={}, 库存 {} -> {}", 
					orderItem.getSpuId(), originalStock, goodsSpu.getStock());
			} else {
				log.error("[ORDER_CANCEL] 未找到对应的商品SPU: spuId={}", orderItem.getSpuId());
			}
		} catch (Exception e) {
			log.error("[ORDER_CANCEL] SPU库存回滚失败: spuId={}, error={}", 
				orderItem.getSpuId(), e.getMessage(), e);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void orderReceive(OrderInfo orderInfo) {
		orderInfo.setStatus(OrderInfoEnum.STATUS_3.getValue());
		orderInfo.setEvaluateFlag(OrderInfoEvaluateEnum.STATUS_1.getValue());
		orderInfo.setReceiverTime(DateUtils.getNowDate());
		
		// 更新所有订单项状态为已完成
		List<OrderItem> listOrderItem = orderItemService.list(Wrappers.<OrderItem>lambdaQuery()
				.eq(OrderItem::getOrderId, orderInfo.getId()));
		listOrderItem.forEach(orderItem -> {
			orderItem.setStatus(OrderInfoEnum.STATUS_3.getValue());
		});
		orderItemService.updateBatchById(listOrderItem);
		log.info("[ORDER_RECEIVE] 订单项状态已更新为已完成: orderId={}, itemCount={}", 
			orderInfo.getId(), listOrderItem.size());
		
		SysUserWallet sysUserWallet = new SysUserWallet();
		sysUserWallet.setOrderId(orderInfo.getId());
		sysUserWallet.setStatus(WalletEnum.STATUS_1.getValue());
		sysUserWallet.setUpdateTime(DateUtils.getNowDate());
		sysUserWalletService.updateSysUserWalletStatus(sysUserWallet);
		baseMapper.updateById(orderInfo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeById(Serializable id) {
		return super.removeById(id);
	}

  @Override
  @Transactional(rollbackFor = Exception.class)
  public OrderInfo orderSub(PlaceOrderDTO placeOrderDTO) {
    
    // 1. 创建处理上下文
    OrderCreationContext context = createOrderContext(placeOrderDTO);
    
    // 2. 执行处理流程
    return orderCreationPipeline.execute(context);
  }
  
  /**
   * 创建订单处理上下文
   */
  private OrderCreationContext createOrderContext(PlaceOrderDTO placeOrderDTO) {
    String openId = ThirdSessionHolder.getThirdSession().getOpenId();
    WxUser wxUser = wxUserService.getByOpenId(openId);
    
    OrderCreationContext context = new OrderCreationContext();
    context.setPlaceOrderDTO(placeOrderDTO);
    context.setWxUser(wxUser);
    
    return context;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void notifyOrder(OrderInfo orderInfo) {
    log.info("[ORDER_NOTIFY] 开始处理订单支付回调 - 订单号: {}, 当前支付状态: {}", 
        orderInfo.getOrderNo(), orderInfo.getIsPay());
    
    // 卫语句：重复支付检查（强制从数据库重新查询最新状态）
    OrderInfo latestOrderInfo = baseMapper.selectById(orderInfo.getId());
    if (latestOrderInfo == null) {
        log.error("[ORDER_NOTIFY] 订单不存在 - 订单ID: {}", orderInfo.getId());
        throw new RuntimeException("订单不存在");
    }
    
    if (!CommonConstants.NO.equals(latestOrderInfo.getIsPay())) {
        log.warn("[ORDER_NOTIFY] 订单已支付，跳过重复处理 - 订单号: {}, 支付状态: {}", 
            latestOrderInfo.getOrderNo(), latestOrderInfo.getIsPay());
        return;
    }
    
    // 验证订单状态是否可以进行支付处理
    if (latestOrderInfo.getStatus() != null && 
        !OrderInfoEnum.STATUS_0.getValue().equals(latestOrderInfo.getStatus()) &&
        !OrderInfoEnum.STATUS_5.getValue().equals(latestOrderInfo.getStatus())) {
        log.error("[ORDER_NOTIFY] 订单状态不允许支付 - 订单号: {}, 当前状态: {}", 
            latestOrderInfo.getOrderNo(), latestOrderInfo.getStatus());
        throw new RuntimeException("订单状态不允许支付");
    }
    
    // 使用最新的订单信息进行处理
    orderInfo = latestOrderInfo;
    
    // 创建final引用以在lambda表达式中使用
    final OrderInfo finalOrderInfo = orderInfo;
    
    if (CommonConstants.NO.equals(orderInfo.getIsPay())) { // 只有未支付订单能操作
      log.info("[ORDER_NOTIFY] 更新订单支付状态 - 订单号: {}", orderInfo.getOrderNo());
      orderInfo.setIsPay(CommonConstants.YES);
      orderInfo.setStatus(OrderInfoEnum.STATUS_2.getValue());
      List<OrderItem> listOrderItem =
          orderItemService.list(
              Wrappers.<OrderItem>lambdaQuery().eq(OrderItem::getOrderId, orderInfo.getId()));
      Map<String, List<OrderItem>> resultList =
          listOrderItem.stream().collect(Collectors.groupingBy(OrderItem::getSpuId));
      List<GoodsSpu> listGoodsSpu = goodsSpuService.listByIds(resultList.keySet());
		listGoodsSpu.forEach(goodsSpu -> {
			resultList.get(goodsSpu.getId()).forEach(orderItem -> {
				//更新销量
				goodsSpu.setSaleNum(goodsSpu.getSaleNum()+orderItem.getQuantity());
				
				// 更新订单项状态为已支付
				orderItem.setStatus(OrderInfoEnum.STATUS_2.getValue());
				orderItemService.updateById(orderItem);
				
	//					GoodsSpuDetail goodsSpuDetail = goodsSpuService.getGoodsSpuDetailById(orderItem.getDetailId());
	//					goodsSpuService.updateGoodsSpuDetail(goodsSpuDetail);
				String[] param = new String[4];
				param[0]=orderItem.getSpuName();
				param[1]=orderItem.getAdultQuantity()==null?null:orderItem.getAdultQuantity().toString();
				Integer i = orderItem.getYoungChildQuantity() + orderItem.getOlderChildQuantity();
				param[2]=i==null?null:i.toString();
				param[3]=orderItem.getElderlyQuantity()==null?null:orderItem.getElderlyQuantity().toString();
				String[] userParam = new String[4];
				userParam[0]=orderItem.getSpuName();
				userParam[1]=DateUtil.format(orderItem.getDepartureDate(), DatePattern.NORM_DATE_FORMAT);
				userParam[2]=goodsSpu.getPhone();
				SMSUtils.sendMessage("1400841079","友同小程序","1871823",param,new String[]{goodsSpu.getPhone()});
				SMSUtils.sendMessage("1400841079","友同小程序","2119272",userParam,new String[]{finalOrderInfo.getOrderContactPhone()});
			});
			goodsSpuService.updateById(goodsSpu);
			baseMapper.updateById(finalOrderInfo);//更新订单

			log.info("[ORDER_NOTIFY] 更新商品销量成功 - 商品ID: {}, 新销量: {}", 
			    goodsSpu.getId(), goodsSpu.getSaleNum());
		});
		
		// 删除Redis中的订单超时键，订单已支付无需再监听超时
		String redisKey = String.valueOf(StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_IS_PAY_0, orderInfo.getId()));
		try {
			Boolean deleted = redisTemplate.delete(redisKey);
			log.info("[ORDER_NOTIFY] Redis键删除结果: key={}, deleted={}", redisKey, deleted);
		} catch (Exception e) {
			log.warn("[ORDER_NOTIFY] Redis键删除失败: key={}, error={}", redisKey, e.getMessage());
			// 不抛异常，避免影响主流程
		}
		
		log.info("[ORDER_NOTIFY] 订单支付回调处理完成 - 订单号: {}, 最终状态: 已支付", 
		    orderInfo.getOrderNo());
		} else {
		    log.warn("[ORDER_NOTIFY] 订单状态异常，无法处理支付回调 - 订单号: {}, 支付状态: {}", 
		        orderInfo.getOrderNo(), orderInfo.getIsPay());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void processZeroPaymentOrder(OrderInfo orderInfo) {
		log.info("[ZERO_PAYMENT] 开始处理0元订单支付: orderId={}", orderInfo.getId());
		
		// 设置支付时间并立即保存到数据库
		orderInfo.setPaymentTime(DateUtils.getNowDate());
		baseMapper.updateById(orderInfo);
		
		// 调用订单回调处理逻辑
		notifyOrder(orderInfo);
		
		log.info("[ZERO_PAYMENT] 0元订单处理完成: orderId={}", orderInfo.getId());
	}

  @Override
  public void saveRefunds(OrderItem orderItem) {
    orderItem = orderItemService.getById(orderItem.getId());
    if (orderItem != null
        && CommonConstants.NO.equals(orderItem.getIsRefund())
        && (OrderInfoEnum.STATUS_2.getValue().equals(orderItem.getStatus())
            || OrderInfoEnum.STATUS_3.getValue().equals(orderItem.getStatus()))) { // 只有未退款的订单才能发起退款
      // 保存申请退款前的状态
      String currentStatus = orderItem.getStatus();
      orderItem.setPreviousStatus(currentStatus);
      
      // 修改订单详情状态为退款中
      orderItem.setStatus(OrderInfoEnum.STATUS_4.getValue());
      orderItem.setRefundApplyTime(DateUtils.getNowDate());
      orderItemService.updateById(orderItem);
      
      log.info("[REFUND_APPLY] 保存退款申请前状态 - 订单项ID: {}, 原状态: {}, 当前状态: {}", 
          orderItem.getId(), currentStatus, OrderInfoEnum.STATUS_4.getValue());
    }
  }

	@Override
	public void doOrderRefunds(OrderItem orderItem) {
		OrderItem orderItem2 = orderItemService.getById(orderItem.getId());
		OrderInfo orderInfo = baseMapper.selectById(orderItem2.getOrderId());
		if(orderItem2 != null){
			if(OrderInfoEnum.STATUS_7.getValue().equals(orderItem.getStatus())){//同意退款
				BigDecimal fifteenDayRefun = orderItem2.getFifteenDayRefun();
				BigDecimal sevenDayRefund = orderItem2.getSevenDayRefund();
				BigDecimal fortyEightHoursRefun = orderItem2.getFortyEightHoursRefun();
				BigDecimal twentyFourHoursRefun = orderItem2.getTwentyFourHoursRefun();
				Date departureDate = orderItem2.getDepartureDate();
				Date nowDate = DateUtils.getNowDate();
				log.info("现在的日期: " + nowDate);

				long diffMinutes = DateUtil.between(nowDate, departureDate, DateUnit.MINUTE);

				log.info(
						"orderId:{},当前日期:{},出发日期:{},当前日期与出发日期的差异（分钟）: {}",
						orderItem2.getOrderId(),
						DateUtil.format(nowDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN),
						DateUtil.format(departureDate, DatePattern.NORM_DATETIME_MINUTE_PATTERN),
						diffMinutes);

				//发起微信退款申请
				WxPayRefundRequest request = new WxPayRefundRequest();
				request.setTransactionId(orderInfo.getTransactionId());
				request.setOutRefundNo(orderItem2.getId());
				request.setTotalFee(orderItem2.getPaymentPrice().multiply(new BigDecimal(100)).intValue());

				if (diffMinutes <= 1440) {
					// 24小时内
					int twentyFourHoursRefundFee = orderItem2.getPaymentPrice().multiply(twentyFourHoursRefun.divide(new BigDecimal(100))).multiply(new BigDecimal(100)).intValue();
					log.info("（24小时内）退款比例: {},退款金额: {}", twentyFourHoursRefun, twentyFourHoursRefundFee);
					request.setRefundFee(twentyFourHoursRefundFee);
				} else if (diffMinutes <= 2880) {
					// 48小时内
					int fortyEightHoursRefundFee = orderItem2.getPaymentPrice().multiply(fortyEightHoursRefun.divide(new BigDecimal(100))).multiply(new BigDecimal(100)).intValue();
					log.info("（48小时内）退款比例: {},退款金额: {} ", fortyEightHoursRefun, fortyEightHoursRefundFee);
					request.setRefundFee(fortyEightHoursRefundFee);
				} else if (diffMinutes <= 10080) {
					// 7天内
					int sevenDayRefundFee = orderItem2.getPaymentPrice().multiply(sevenDayRefund.divide(new BigDecimal(100))).multiply(new BigDecimal(100)).intValue();
					log.info("（7天内）退款比例: {},退款金额: {}", sevenDayRefund, sevenDayRefundFee);
					request.setRefundFee(sevenDayRefundFee);
				} else if (diffMinutes <= 21600) {
					// 15天内
					int fifteenDayRefundFee = orderItem2.getPaymentPrice().multiply(fifteenDayRefun.divide(new BigDecimal(100))).multiply(new BigDecimal(100)).intValue();
					log.info("（15天内）退款比例: {},退款金额: {}", fifteenDayRefun, fifteenDayRefundFee);
					request.setRefundFee(fifteenDayRefundFee);
				} else {
					// 超过15天
					int totalRefundFee = orderItem2.getPaymentPrice().multiply(new BigDecimal(100)).intValue();
					log.info("（超过15天）退款比例: 全额退款,退款金额: {}", totalRefundFee);
					request.setRefundFee(totalRefundFee);
				}

				request.setNotifyUrl(mallConfigProperties.getNotifyHost()+"/weixin/api/ma/orderinfo/notify-refunds");
				WxPayService wxPayService = WxPayConfiguration.getPayService();
				try {
					wxPayService.refund(request);
					orderItem2.setStatus(orderItem.getStatus());
					orderItemService.updateById(orderItem2);
					
					// 同步更新订单状态为已退款
					syncOrderStatusWithOrderItem(orderItem2.getOrderId(), OrderInfoEnum.STATUS_6.getValue());
					
					SysUserWallet sysUserWallet = new SysUserWallet();
					sysUserWallet.setOrderId(orderInfo.getId());
					sysUserWallet.setStatus(WalletEnum.STATUS_2.getValue());
					sysUserWallet.setUpdateTime(DateUtils.getNowDate());
					sysUserWalletService.updateSysUserWalletStatus(sysUserWallet);
				} catch (WxPayException e) {
					e.printStackTrace();
					throw new RuntimeException(e.getReturnMsg() + "" + e.getCustomErrorMsg() + "" + e.getErrCodeDes());
				}
			}else if(OrderInfoEnum.STATUS_8.getValue().equals(orderItem.getStatus())){//拒绝退款
				orderItem2.setStatus(orderItem.getStatus());
				// 保存拒绝退款原因到备注字段
				if (StrUtil.isNotBlank(orderItem.getRemark())) {
					String originalRemark = StrUtil.isNotBlank(orderItem2.getRemark()) ? orderItem2.getRemark() : "";
					String rejectReasonText = "拒绝退款原因：" + orderItem.getRemark();
					
					// 如果原备注不为空，则追加；否则直接设置拒绝原因
					if (StrUtil.isNotBlank(originalRemark)) {
						orderItem2.setRemark(originalRemark + "；" + rejectReasonText);
					} else {
						orderItem2.setRemark(rejectReasonText);
					}
					
					log.info("[REFUND_REJECT] 保存拒绝退款原因 - 订单项ID: {}, 原因: {}", 
						orderItem2.getId(), orderItem.getRemark());
				}
				orderItemService.updateById(orderItem2);
				
				// 同步更新订单状态为拒绝退款
				syncOrderStatusWithOrderItem(orderItem2.getOrderId(), OrderInfoEnum.STATUS_8.getValue());
			}
		}
	}

  /**
   * 统一退款入口 - 自动判断模拟或真实退款
   */
  @Override
  public void processOrderRefunds(OrderItem orderItem) {
    OrderItem orderItem2 = orderItemService.getById(orderItem.getId());
    if (ObjectUtil.isNull(orderItem2)) {
      throw new CustomException("订单明细项没有查询到");
    }
    
    OrderInfo orderInfo = baseMapper.selectById(orderItem2.getOrderId());
    
    // 检查是否为模拟支付订单
    boolean isMockPayment = StrUtil.isBlank(orderInfo.getTransactionId()) || 
            orderInfo.getTransactionId().startsWith("MOCK");
    
    if (isMockPayment) {
      log.info("[REFUND_ROUTER] 路由到模拟退款处理 - 订单项ID: {}", orderItem.getId());
      doOrderRefundsForMock(orderItem);
    } else {
      log.info("[REFUND_ROUTER] 路由到真实退款处理 - 订单项ID: {}", orderItem.getId());
      doOrderRefunds(orderItem);
    }
  }

  @Override
  public void doOrderRefundsForMock(OrderItem orderItem) {
    OrderItem orderItem2 = orderItemService.getById(orderItem.getId());
    OrderInfo orderInfo = baseMapper.selectById(orderItem2.getOrderId());
    if (ObjectUtil.isNull(orderItem2)) {
      throw new CustomException("订单明细项没有查询到");
    }
    
    if (OrderInfoEnum.STATUS_7.getValue().equals(orderItem.getStatus())) {
      // 同意退款 - 模拟退款直接成功
      log.info("[MOCK_REFUND] 模拟退款审批通过 - 订单项ID: {}, 直接标记为退款成功", orderItem2.getId());
      orderItem2.setStatus(orderItem.getStatus());
      orderItem2.setIsRefund(CommonConstants.YES);
      orderItemService.updateById(orderItem2);

      // 同步更新订单状态为已退款
      syncOrderStatusWithOrderItem(orderItem2.getOrderId(), OrderInfoEnum.STATUS_6.getValue());

      // 更新钱包状态
      SysUserWallet sysUserWallet = new SysUserWallet();
      sysUserWallet.setOrderId(orderInfo.getId());
      sysUserWallet.setStatus(WalletEnum.STATUS_2.getValue());
      sysUserWallet.setUpdateTime(DateUtils.getNowDate());
      sysUserWalletService.updateSysUserWalletStatus(sysUserWallet);

      log.info("[MOCK_REFUND] 模拟退款处理完成 - 订单项ID: {}", orderItem2.getId());
    } else if (OrderInfoEnum.STATUS_8.getValue().equals(orderItem.getStatus())) {
      // 拒绝退款
      log.info("[MOCK_REFUND] 模拟退款审批拒绝 - 订单项ID: {}", orderItem2.getId());
      orderItem2.setStatus(orderItem.getStatus());
      // 保存拒绝退款原因到备注字段
      if (StrUtil.isNotBlank(orderItem.getRemark())) {
        String originalRemark = StrUtil.isNotBlank(orderItem2.getRemark()) ? orderItem2.getRemark() : "";
        String rejectReasonText = "拒绝退款原因：" + orderItem.getRemark();
        
        // 如果原备注不为空，则追加；否则直接设置拒绝原因
        if (StrUtil.isNotBlank(originalRemark)) {
          orderItem2.setRemark(originalRemark + "；" + rejectReasonText);
        } else {
          orderItem2.setRemark(rejectReasonText);
        }
        
        log.info("[MOCK_REFUND] 保存拒绝退款原因 - 订单项ID: {}, 原因: {}", 
          orderItem2.getId(), orderItem.getRemark());
      }
      orderItemService.updateById(orderItem2);
      
      // 同步更新订单状态为拒绝退款
      syncOrderStatusWithOrderItem(orderItem2.getOrderId(), OrderInfoEnum.STATUS_8.getValue());
    }
  }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void notifyRefunds(WxPayRefundNotifyResult notifyResult) {
		OrderItem orderItem = orderItemService.getById(notifyResult.getReqInfo().getOutRefundNo());
		OrderInfo orderInfo = baseMapper.selectById(orderItem.getOrderId());
		if(OrderInfoEnum.STATUS_7.getValue().equals(orderItem.getStatus())){
			//更新订单状态
			orderItem.setIsRefund(CommonConstants.YES);
			orderInfo.setStatus(OrderInfoEnum.STATUS_6.getValue());
			orderItemService.updateById(orderItem);
			baseMapper.updateById(orderInfo);
		}
	}

	@Override
	public OrderInfo orderUserLv(PlaceUserLvDTO placeUserLvDTO) {
		return null;
	}

	@Override
	public OrderInfo selectById2(String id) {
		return orderInfoMapper.selectById2(id);
	}

	@Override
	public IPage<TravelOrderStat> selectOrderStatPage(IPage<TravelOrderStat> page, OrderInfo orderInfo) {
		return orderInfoMapper.selectOrderStatPage(page,orderInfo);
	}

	@Override
	public List<OrderInfoDTO> getOrderInfoList(OrderInfo orderInfo) {
		return orderInfoMapper.getOrderInfoList(orderInfo);
	}

	/**
	 * 处理超时订单状态
	 * 当Redis键不存在或已过期时，检查订单是否真的超时，如果超时则处理
	 */
	private void handleTimeoutOrderStatus(OrderInfo orderInfo) {
		try {
			// 卫语句：只处理未支付的订单
			if (!CommonConstants.NO.equals(orderInfo.getIsPay())) {
				return;
			}
			
			// 卫语句：订单创建时间为空
			if (orderInfo.getCreateTime() == null) {
				log.warn("[TIMEOUT_ORDER_STATUS] 订单创建时间为空，无法判断是否超时: orderId={}", orderInfo.getId());
				return;
			}
			
			// 计算订单创建到现在的时间（分钟）
			long elapsedMinutes = DateUtil.between(orderInfo.getCreateTime(), DateUtils.getNowDate(), DateUnit.MINUTE);
			
			// 如果确实超时（超过30分钟）
			if (elapsedMinutes >= DEFAULT_ORDER_TIMEOUT_MINUTES) {
				log.warn("[TIMEOUT_ORDER_STATUS] 订单确认超时，开始处理: orderId={}, 创建时间={}, 已过时间={}分钟", 
					orderInfo.getId(), orderInfo.getCreateTime(), elapsedMinutes);
				
				// 调用已有的订单取消逻辑，保持一致性
				orderCancel(orderInfo);
				
				log.info("[TIMEOUT_ORDER_STATUS] 超时订单处理完成: orderId={}", orderInfo.getId());
			} else {
				// 未超时，但Redis键丢失，重新设置Redis键
				long remainingMinutes = DEFAULT_ORDER_TIMEOUT_MINUTES - elapsedMinutes;
				String keyRedis = String.valueOf(StrUtil.format("{}:{}", MallConstants.REDIS_ORDER_KEY_IS_PAY_0, orderInfo.getId()));
				
				// 转换为秒数设置过期时间，保持与获取时的单位一致
				long remainingSeconds = remainingMinutes * 60;
				redisTemplate.opsForValue().set(keyRedis, orderInfo.getOrderNo(), remainingSeconds, TimeUnit.SECONDS);
				
				// 重新计算outTime（以秒为单位）
				Long outTime = redisTemplate.getExpire(keyRedis);
				if (outTime > 0) {
					orderInfo.setOutTime(outTime);
				}
				
				log.info("[TIMEOUT_ORDER_STATUS] Redis键已恢复: orderId={}, 剩余时间={}分钟({}秒)", 
					orderInfo.getId(), remainingMinutes, remainingSeconds);
			}
			
		} catch (Exception e) {
			log.error("[TIMEOUT_ORDER_STATUS] 处理超时订单状态失败: orderId={}, error={}", 
				orderInfo.getId(), e.getMessage(), e);
			// 不抛出异常，避免影响查询主流程
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cancelRefundApplication(String orderItemId) {
		log.info("[CANCEL_REFUND] 开始取消退款申请 - 订单项ID: {}", orderItemId);
		
		// 查询订单项
		OrderItem orderItem = orderItemService.getById(orderItemId);
		if (ObjectUtil.isNull(orderItem)) {
			log.error("[CANCEL_REFUND] 订单项不存在 - ID: {}", orderItemId);
			throw new CustomException("订单项不存在");
		}
		
		// 验证订单项状态必须是"退款中"
		if (!OrderInfoEnum.STATUS_4.getValue().equals(orderItem.getStatus())) {
			log.error("[CANCEL_REFUND] 订单项状态不是退款中，无法取消 - ID: {}, 当前状态: {}", 
				orderItemId, orderItem.getStatus());
			throw new CustomException("订单项状态不是退款中，无法取消申请");
		}
		
		// 验证是否有保存的原状态
		if (StrUtil.isBlank(orderItem.getPreviousStatus())) {
			log.error("[CANCEL_REFUND] 未找到申请退款前的状态 - ID: {}", orderItemId);
			throw new CustomException("无法确定退款申请前的状态");
		}
		
		// 恢复到申请退款前的状态
		String previousStatus = orderItem.getPreviousStatus();
		orderItem.setStatus(previousStatus);
		orderItem.setPreviousStatus(null); // 清空保存的状态
		orderItem.setRefundApplyTime(null); // 清空退款申请时间
		
		orderItemService.updateById(orderItem);
		
		// 同步更新主订单状态
		syncOrderStatusWithOrderItem(orderItem.getOrderId(), previousStatus);
		
		log.info("[CANCEL_REFUND] 取消退款申请完成 - 订单项ID: {}, 恢复状态: {}", 
			orderItemId, previousStatus);
	}

	/**
	 * 订单项状态变更后，同步更新订单状态
	 */
	@Override
	public void syncOrderStatusWithOrderItem(String orderId, String status) {
		OrderInfo orderInfo = baseMapper.selectById(orderId);
		if (orderInfo != null) {
			orderInfo.setStatus(status);
			baseMapper.updateById(orderInfo);
			log.info("[ORDER_SYNC] 订单状态已同步 - 订单ID: {}, 新状态: {}", orderId, status);
		}
	}

}
