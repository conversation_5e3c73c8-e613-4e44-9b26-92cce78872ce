package com.joolun.mall.entity.query;

import java.io.Serializable;
import java.util.Set;

import lombok.*;

/**
 * 用户浏览记录对象 ty_user_browse_info
 *
 * <AUTHOR>
 * @date 2023-12-09
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class TyUserBrowseInfoQuery implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 商品ids */
  private Set<String> goodIds;
}
