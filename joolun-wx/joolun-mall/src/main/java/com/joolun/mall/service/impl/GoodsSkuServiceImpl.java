package com.joolun.mall.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.common.utils.DateUtils;
import com.joolun.mall.dto.GoodsAttrValueAppDTO;
import com.joolun.mall.dto.GoodsSkuAppDTO;
import com.joolun.mall.dto.SkuCalendarPriceAppDTO;
import com.joolun.mall.entity.*;
import com.joolun.mall.mapper.GoodsSkuCalendarPriceMapper;
import com.joolun.mall.mapper.GoodsSkuMapper;
import com.joolun.mall.mapper.GoodsSkuSpecificationValueMapper;
import com.joolun.mall.service.GoodsSpecificationAttributeService;
import com.joolun.mall.service.GoodsSpecificationValueService;
import com.joolun.mall.service.IGoodsSkuService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 商品 SKU Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class GoodsSkuServiceImpl extends ServiceImpl<GoodsSkuMapper, GoodsSku>
    implements IGoodsSkuService {
  @Resource private GoodsSkuMapper goodsSkuMapper;
  @Resource private GoodsSkuCalendarPriceMapper goodsSkuCalendarPriceMapper;
  @Resource private GoodsSkuSpecificationValueMapper goodsSkuSpecificationValueMapper;
  @Resource private GoodsSpecificationValueService goodsSpecificationValueService;
  @Resource private GoodsSpecificationAttributeService goodsSpecificationAttributeService;

  /**
   * 查询商品 SKU
   *
   * @param id 商品 SKU ID
   * @return 商品 SKU
   */
  @Override
  public GoodsSku selectGoodsSkuById(String id) {
    return goodsSkuMapper.selectGoodsSkuById(id);
  }

  /**
   * 查询商品 SKU 列表
   *
   * @param goodsSku 商品 SKU
   * @return 商品 SKU
   */
  @Override
  public List<GoodsSku> selectGoodsSkuList(GoodsSku goodsSku) {
    return goodsSkuMapper.selectGoodsSkuList(goodsSku);
  }

  /**
   * 新增商品 SKU
   *
   * @param goodsSku 商品 SKU
   * @return 结果
   */
  @Override
  public int insertGoodsSku(GoodsSku goodsSku) {
    goodsSku.setCreateTime(DateUtils.getNowDate());
    return goodsSkuMapper.insertGoodsSku(goodsSku);
  }

  /**
   * 修改商品 SKU
   *
   * @param goodsSku 商品 SKU
   * @return 结果
   */
  @Override
  public int updateGoodsSku(GoodsSku goodsSku) {
    goodsSku.setUpdateTime(DateUtils.getNowDate());
    return goodsSkuMapper.updateGoodsSku(goodsSku);
  }

  /**
   * 批量删除商品 SKU
   *
   * @param ids 需要删除的商品 SKU ID
   * @return 结果
   */
  @Override
  public int deleteGoodsSkuByIds(String[] ids) {
    return goodsSkuMapper.deleteGoodsSkuByIds(ids);
  }

  /**
   * 删除商品 SKU 信息
   *
   * @param id 商品 SKU ID
   * @return 结果
   */
  @Override
  public int deleteGoodsSkuById(String id) {
    return goodsSkuMapper.deleteGoodsSkuById(id);
  }

  @Override
  public int deleteGoodsSkuByGoodsIds(Set<String> goodsIds) {
    if (ObjectUtil.isEmpty(goodsIds)) {
      return 0;
    }
    return goodsSkuMapper.delete(new UpdateWrapper<GoodsSku>().in("goods_id", goodsIds));
  }

  /**
   * 根据商品ID获取SKU列表（小程序端使用）
   * 
   * @param goodsId 商品ID
   * @return SKU列表
   */
  @Override
  public List<GoodsSkuAppDTO> getSkuListByGoodsId(String goodsId) {
    if (ObjectUtil.isEmpty(goodsId)) {
      return new ArrayList<>();
    }
    
    // 1. 查询商品下的所有有效SKU
    QueryWrapper<GoodsSku> skuQuery = new QueryWrapper<>();
    skuQuery.eq("goods_id", goodsId)
            .eq("delete_flag", false)
            .orderByAsc("create_time");
    List<GoodsSku> skuList = goodsSkuMapper.selectList(skuQuery);
    
    if (ObjectUtil.isEmpty(skuList)) {
      return new ArrayList<>();
    }
    
    // 2. 批量查询SKU的价格区间
    List<String> skuIds = skuList.stream().map(GoodsSku::getId).collect(Collectors.toList());
    Map<String, BigDecimal[]> priceRangeMap = calculatePriceRanges(skuIds);
    
    // 3. 批量查询SKU的规格信息
    Map<String, List<GoodsAttrValueAppDTO>> specInfoMap = getSkuSpecInfoMap(skuIds);
    
    // 4. 组装返回数据
    return skuList.stream().map(sku -> {
      GoodsSkuAppDTO dto = GoodsSkuAppDTO.builder()
          .skuId(sku.getId())
          .skuName(ObjectUtil.isEmpty(sku.getSkuName()) ? "默认规格" : sku.getSkuName())
          .specificationType(sku.getHasSpecifications() != null ? sku.getHasSpecifications().toString() : "0")
          .specInfo(specInfoMap.getOrDefault(sku.getId(), new ArrayList<>()))
          .build();
      
      // 设置价格区间
      BigDecimal[] priceRange = priceRangeMap.get(sku.getId());
      if (priceRange != null) {
        dto.setMinPrice(priceRange[0]);
        dto.setMaxPrice(priceRange[1]);
      } else {
        dto.setMinPrice(BigDecimal.ZERO);
        dto.setMaxPrice(BigDecimal.ZERO);
      }
      
      return dto;
    }).collect(Collectors.toList());
  }
  
  /**
   * 计算SKU价格区间
   */
  private Map<String, BigDecimal[]> calculatePriceRanges(List<String> skuIds) {
    if (ObjectUtil.isEmpty(skuIds)) {
      return new HashMap<>();
    }
    
    QueryWrapper<GoodsSkuCalendarPrice> priceQuery = new QueryWrapper<>();
    priceQuery.in("sku_id", skuIds)
             .eq("delete_flag", false)
             .eq("status", 1); // 只统计在售状态的价格
    
    List<GoodsSkuCalendarPrice> priceList = goodsSkuCalendarPriceMapper.selectList(priceQuery);
    
    Map<String, BigDecimal[]> priceRangeMap = new HashMap<>();
    
    // 按SKU分组统计价格区间
    Map<String, List<GoodsSkuCalendarPrice>> priceGroupMap = priceList.stream()
        .collect(Collectors.groupingBy(GoodsSkuCalendarPrice::getSkuId));
    
    priceGroupMap.forEach((skuId, prices) -> {
      BigDecimal minPrice = null;
      BigDecimal maxPrice = null;
      
      for (GoodsSkuCalendarPrice price : prices) {
        // 成人价格
        if (price.getAdultPrice() != null) {
          if (minPrice == null || price.getAdultPrice().compareTo(minPrice) < 0) {
            minPrice = price.getAdultPrice();
          }
          if (maxPrice == null || price.getAdultPrice().compareTo(maxPrice) > 0) {
            maxPrice = price.getAdultPrice();
          }
        }
        // 儿童价格
        if (price.getChildPrice() != null) {
          if (minPrice == null || price.getChildPrice().compareTo(minPrice) < 0) {
            minPrice = price.getChildPrice();
          }
          if (maxPrice == null || price.getChildPrice().compareTo(maxPrice) > 0) {
            maxPrice = price.getChildPrice();
          }
        }
      }
      
      priceRangeMap.put(skuId, new BigDecimal[]{
          minPrice != null ? minPrice : BigDecimal.ZERO,
          maxPrice != null ? maxPrice : BigDecimal.ZERO
      });
    });
    
    return priceRangeMap;
  }

  /** 获取SKU规格信息 */
  private Map<String, List<GoodsAttrValueAppDTO>> getSkuSpecInfoMap(List<String> skuIds) {
    if (ObjectUtil.isEmpty(skuIds)) {
      return new HashMap<>();
    }

    QueryWrapper<GoodsSkuSpecificationValue> specQuery = new QueryWrapper<>();
    specQuery.in("sku_id", skuIds).eq("delete_flag", false);

    List<GoodsSkuSpecificationValue> skuSpecList =
        goodsSkuSpecificationValueMapper.selectList(specQuery);

    if (ObjectUtil.isEmpty(skuSpecList)) {
      return new HashMap<>();
    }

    // 获取所有规格值ID
    List<String> specValueIds =
        skuSpecList.stream()
            .map(GoodsSkuSpecificationValue::getSpecValueId)
            .distinct()
            .collect(Collectors.toList());

    // 批量查询规格值信息
    QueryWrapper<GoodsSpecificationValue> valueQuery = new QueryWrapper<>();
    valueQuery.in("id", specValueIds).eq("delete_flag", false);

    List<GoodsSpecificationValue> specValueList = goodsSpecificationValueService.list(valueQuery);
    Map<String, GoodsSpecificationValue> specValueMap =
        specValueList.stream().collect(Collectors.toMap(GoodsSpecificationValue::getId, v -> v));

    // 获取所有规格(分类)ID，用于查询规格名
    List<String> attrIds =
        specValueList.stream()
            .map(GoodsSpecificationValue::getAttrId)
            .distinct()
            .collect(Collectors.toList());

    Map<String, GoodsSpecificationAttribute> attrMap;
    if (!attrIds.isEmpty()) {
      QueryWrapper<GoodsSpecificationAttribute> attrQuery = new QueryWrapper<>();
      attrQuery.in("id", attrIds).eq("delete_flag", false);
      List<GoodsSpecificationAttribute> attrList =
          goodsSpecificationAttributeService.list(attrQuery);
      if (attrList != null) {
        attrMap =
            attrList.stream().collect(Collectors.toMap(GoodsSpecificationAttribute::getId, a -> a));
      } else {
        attrMap = new HashMap<>();
      }
    } else {
      attrMap = new HashMap<>();
    }

    // 按SKU分组组装规格信息
    Map<String, List<GoodsAttrValueAppDTO>> resultMap = new HashMap<>();

    for (GoodsSkuSpecificationValue skuSpec : skuSpecList) {
      GoodsSpecificationValue specValue = specValueMap.get(skuSpec.getSpecValueId());
      if (specValue != null) {
        GoodsSpecificationAttribute attr = attrMap.get(specValue.getAttrId());
        GoodsAttrValueAppDTO dto =
            GoodsAttrValueAppDTO.builder()
                .attrId(skuSpec.getAttrId())
                .attrName(attr != null ? attr.getAttrName() : null)
                .valueId(skuSpec.getSpecValueId())
                .valueName(specValue.getName())
                .build();
        String skuId = skuSpec.getSkuId();
        resultMap.computeIfAbsent(skuId, k -> new ArrayList<>()).add(dto);
      }
    }

    // 对每个SKU下的规格信息按分类排序，再按规格值排序
    Comparator<GoodsAttrValueAppDTO> comparator =
        (d1, d2) -> {
          Integer attrSort1 =
              Optional.ofNullable(attrMap.get(d1.getAttrId()))
                  .map(GoodsSpecificationAttribute::getSortOrder)
                  .orElse(999);
          Integer attrSort2 =
              Optional.ofNullable(attrMap.get(d2.getAttrId()))
                  .map(GoodsSpecificationAttribute::getSortOrder)
                  .orElse(999);
          if (!Objects.equals(attrSort1, attrSort2)) {
            return attrSort1 - attrSort2;
          }
          Integer valSort1 =
              Optional.ofNullable(specValueMap.get(d1.getValueId()))
                  .map(GoodsSpecificationValue::getSortOrder)
                  .orElse(999);
          Integer valSort2 =
              Optional.ofNullable(specValueMap.get(d2.getValueId()))
                  .map(GoodsSpecificationValue::getSortOrder)
                  .orElse(999);
          return valSort1 - valSort2;
        };

    resultMap.values().forEach(list -> list.sort(comparator));

    return resultMap;
  }

  /**
   * 获取SKU价格日历（小程序端使用）
   * 
   * @param skuId SKU ID
   * @param days 天数
   * @return 价格日历列表
   */
  @Override
  public List<SkuCalendarPriceAppDTO> getSkuCalendarPrices(String skuId, Integer days) {
    if (ObjectUtil.isEmpty(skuId)) {
      return new ArrayList<>();
    }
    
    if (days == null || days <= 0) {
      days = 30; // 默认30天
    }
    
    // 1. 验证SKU是否存在
    GoodsSku sku = goodsSkuMapper.selectById(skuId);
    if (sku == null || Boolean.TRUE.equals(sku.getDeleteFlag())) {
      return new ArrayList<>();
    }
    
    // 2. 生成日期范围
    LocalDate startDate = LocalDate.now();
    LocalDate endDate = startDate.plusDays(days - 1);
    
    // 3. 查询价格数据
    QueryWrapper<GoodsSkuCalendarPrice> priceQuery = new QueryWrapper<>();
    priceQuery.eq("sku_id", skuId)
             .eq("delete_flag", false)
             .ge("calendar_date", startDate)
             .le("calendar_date", endDate)
             .orderByAsc("calendar_date");
    
    List<GoodsSkuCalendarPrice> priceList = goodsSkuCalendarPriceMapper.selectList(priceQuery);
    
    // 4. 创建价格数据映射
    Map<LocalDate, GoodsSkuCalendarPrice> priceMap = priceList.stream()
        .collect(Collectors.toMap(
            price -> price.getCalendarDate().toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDate(),
            price -> price,
            (existing, replacement) -> existing // 如果有重复日期，保留第一个
        ));
    
    // 5. 生成完整的日期列表
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    List<SkuCalendarPriceAppDTO> resultList = new ArrayList<>();

    LocalDate currentDate = startDate;
    while (!currentDate.isAfter(endDate)) {
      GoodsSkuCalendarPrice priceData = priceMap.get(currentDate);

      // 获取星期几
      String dayOfWeek = getDayOfWeekInChinese(currentDate);

      SkuCalendarPriceAppDTO dto = SkuCalendarPriceAppDTO.builder()
          .skuId(skuId) // 设置SKU ID
          .date(currentDate.format(formatter))
          .dayOfWeek(dayOfWeek)
          .build();
      
      if (priceData != null) {
        // 有价格数据
        dto.setId(priceData.getId()); // 设置价格记录ID
        dto.setAdultPrice(priceData.getAdultPrice());
        dto.setChildPrice(priceData.getChildPrice());
        dto.setStock(priceData.getStock());
        dto.setCommission(priceData.getCommission());
        // 状态：1-可售 2-停售，根据实际状态设置
        dto.setStatus(priceData.getStatus() != null ? priceData.getStatus() : 1);
      } else {
        // 无价格数据，设置为置灰状态，ID保持为null
        dto.setAdultPrice(BigDecimal.ZERO);
        dto.setChildPrice(BigDecimal.ZERO);
        dto.setStock(0);
        dto.setCommission(BigDecimal.ZERO);
        dto.setStatus(0); // 0-无数据(置灰)
      }
      
      resultList.add(dto);
      currentDate = currentDate.plusDays(1);
    }
    
    return resultList;
  }

  /**
   * 获取中文星期几
   *
   * @param date 日期
   * @return 中文星期几
   */
  private String getDayOfWeekInChinese(LocalDate date) {
    switch (date.getDayOfWeek()) {
      case MONDAY:
        return "周一";
      case TUESDAY:
        return "周二";
      case WEDNESDAY:
        return "周三";
      case THURSDAY:
        return "周四";
      case FRIDAY:
        return "周五";
      case SATURDAY:
        return "周六";
      case SUNDAY:
        return "周日";
      default:
        return "";
    }
  }
}
