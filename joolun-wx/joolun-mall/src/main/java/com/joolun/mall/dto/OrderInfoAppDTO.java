package com.joolun.mall.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.joolun.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
@Data
@TableName("order_info")
@ApiModel(description = "商城订单")
public class OrderInfoAppDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;

  /** 用户id */
  private String userId;

  /** 订单单号 */
  private String orderNo;

  /** 支付方式1、货到付款；2、在线支付 */
  private String paymentWay;

  /** 是否支付0、未支付 1、已支付 */
  private String isPay;

  /** 订单名 */
  private String name;

  /** 状态0、待付款 1、待发货 2、待收货 3、已完成 4、已关闭 */
  private String status;

  /** 销售金额 */
  private BigDecimal salesPrice;

  /** 支付金额（销售金额+运费金额-积分抵扣金额-电子券抵扣金额） */
  private BigDecimal paymentPrice;

  /** 状态0、待付款 1、待发货 2、待收货 3、已完成 4、已关闭 */
  private String statusDesc;

  /** 成人总人数 */
  private Integer adultQuantity;

  /** 儿童总人数 */
  private Integer childrenQuantity;

  /** 出发日期 */
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date departureDate;

  /** 图片 */
  @ApiModelProperty(value = "图片")
  private String picUrl;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Excel(name = "创建时间")
  private Date createTime;
}
