package com.joolun.mall.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.joolun.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 商城订单详情
 *
 * <AUTHOR>
 * @date 2019-09-10 15:31:40
 */
@Data
@ApiModel(description = "商城订单详情")
public class OrderItemAppDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;

  private String orderId;

  /** 用户id */
  private String userId;

  /** 订单单号 */
  private String orderNo;

  /** 订单创建时间 */
  @ApiModelProperty(value = "订单创建时间")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /** 订单联系人名称 */
  @ApiModelProperty(value = "订单联系人名称")
  private String orderContactName;

  /** 订单联系人电话 */
  @ApiModelProperty(value = "订单联系人电话")
  private String orderContactPhone;

  /** 状态0、待付款 1、待发货 2、待收货 3、已完成 4、已关闭 */
  @ApiModelProperty(value = "状态0、待付款 1、待发货 2、待收货 3、已完成 4、已关闭")
  private String status;

  /** 状态0、待付款 1、待发货 2、待收货 3、已完成 4、已关闭 */
  private String statusDesc;

  // ======== 订单商品信息 =========//

  /** 商品Id */
  @ApiModelProperty(value = "商品Id")
  private String spuId;

  /** SKU ID (新增字段 - SKU迁移) */
  @ApiModelProperty(value = "SKU ID")
  private String skuId;

  /** 商品名 */
  @ApiModelProperty(value = "商品名")
  private String spuName;

  /** SKU套餐名称 (新增字段 - SKU迁移) */
  @ApiModelProperty(value = "SKU套餐名称")
  private String skuName;

  /** 卖点 */
  @ApiModelProperty(value = "卖点")
  private String sellPoint;

  /** 图片 */
  @ApiModelProperty(value = "图片")
  private String picUrl;

  /** 商品数量 */
  @ApiModelProperty(value = "商品数量")
  private Integer quantity;

  /** 支付金额（购买单价*商品数量+运费金额） */
  @ApiModelProperty(value = "支付金额（购买单价*商品数量+运费金额）")
  private BigDecimal paymentPrice;

  /** 成人数量 */
  @ApiModelProperty(value = "成人数量")
  private Integer adultQuantity;

  /** 儿童数量 */
  @ApiModelProperty(value = "儿童数量")
  private Integer childQuantity;

  /** 7天退款比例 */
  @Excel(name = "7天退款比例")
  private BigDecimal sevenDayRefund;

  /** 15天退款比例 */
  @Excel(name = "15天退款比例")
  private BigDecimal fifteenDayRefun;

  /** 48小时退款比例 */
  @Excel(name = "48小时退款比例")
  private BigDecimal fortyEightHoursRefun;

  /** 24小时退款比例 */
  @Excel(name = "24小时退款比例")
  private BigDecimal twentyFourHoursRefun;

  /** 出发日期 */
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date departureDate;

  /** 出发日期对应的周几 */
  @ApiModelProperty(value = "出发日期对应的周几")
  private String dayOfWeek;

  /** 订单状态过期时间（秒） */
  @ApiModelProperty(value = "订单状态过期时间（秒）")
  private Long outTime;

  /** 订单出行人信息 */
  private List<OrderItemTravelersAppDTO> orderItemTravelersAppDTO;
}
