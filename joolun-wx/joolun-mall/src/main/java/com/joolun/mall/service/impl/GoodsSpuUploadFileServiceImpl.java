package com.joolun.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joolun.mall.constant.GoodsSpuUploadFileConstants;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.mall.mapper.GoodsSpuUploadFileMapper;
import com.joolun.mall.entity.GoodsSpuUploadFile;
import com.joolun.mall.service.GoodsSpuUploadFileService;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class GoodsSpuUploadFileServiceImpl extends ServiceImpl<GoodsSpuUploadFileMapper, GoodsSpuUploadFile> implements GoodsSpuUploadFileService {

  @Override
  public boolean deleteByIds(String[] ids) {
      LambdaUpdateWrapper<GoodsSpuUploadFile> set =
              Wrappers.<GoodsSpuUploadFile>lambdaUpdate()
                      .set(GoodsSpuUploadFile::getDeleteFlag, Boolean.TRUE)
                      .in(GoodsSpuUploadFile::getId, ids);
    return this.update(set);
  }

  @Override
  public boolean checkCoverImageLimit(String objectId) {
    if (!StringUtils.hasText(objectId)) {
      return false;
    }
    
    LambdaQueryWrapper<GoodsSpuUploadFile> queryWrapper = Wrappers.<GoodsSpuUploadFile>lambdaQuery()
        .eq(GoodsSpuUploadFile::getObjectId, objectId)
        .eq(GoodsSpuUploadFile::getObjectType, GoodsSpuUploadFileConstants.UPLOAD_FILE_TYPE_DEFAULT)
        .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE);
    
    long count = this.count(queryWrapper);
    return count >= 1; // 封面图限制为1张
  }

  @Override
  public List<GoodsSpuUploadFile> getByObjectIdAndType(String objectId, String objectType) {
    LambdaQueryWrapper<GoodsSpuUploadFile> queryWrapper = Wrappers.<GoodsSpuUploadFile>lambdaQuery()
        .eq(StringUtils.hasText(objectId), GoodsSpuUploadFile::getObjectId, objectId)
        .eq(StringUtils.hasText(objectType), GoodsSpuUploadFile::getObjectType, objectType)
        .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
        .orderByAsc(GoodsSpuUploadFile::getFileSort)
        .orderByAsc(GoodsSpuUploadFile::getCreateTime);
    
    return this.list(queryWrapper);
  }

  @Override
  public boolean deleteByObjectIdAndType(String objectId, String objectType) {
    LambdaUpdateWrapper<GoodsSpuUploadFile> updateWrapper = Wrappers.<GoodsSpuUploadFile>lambdaUpdate()
        .set(GoodsSpuUploadFile::getDeleteFlag, Boolean.TRUE)
        .eq(StringUtils.hasText(objectId), GoodsSpuUploadFile::getObjectId, objectId)
        .eq(StringUtils.hasText(objectType), GoodsSpuUploadFile::getObjectType, objectType)
        .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE);
    
    return this.update(updateWrapper);
  }

  @Override
  public boolean updateObjectIdBatch(List<String> fileIds, String objectId) {
    if (fileIds == null || fileIds.isEmpty() || !StringUtils.hasText(objectId)) {
      return false;
    }
    
    LambdaUpdateWrapper<GoodsSpuUploadFile> updateWrapper = Wrappers.<GoodsSpuUploadFile>lambdaUpdate()
        .set(GoodsSpuUploadFile::getObjectId, objectId)
        .in(GoodsSpuUploadFile::getId, fileIds)
        .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE);
    
    return this.update(updateWrapper);
  }
}
