package com.joolun.mall.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 订单创建配置
 * 缓存订单数量信息，避免重复调用
 */
@Data
public class OrderCreationConfig {
    
    private final int adultQuantity;
    private final int elderlyQuantity;
    private final int olderChildQuantity;
    private final int youngChildQuantity;
    private final BigDecimal replenishRoomNum;
    
    public OrderCreationConfig(PlaceOrderDTO dto) {
        // 使用 Optional.ofNullable().orElse(0) 来安全地处理可能为 null 的 Integer 类型
        // 如果 dto.getAdultQuantity() 返回 null，则默认为 0
        this.adultQuantity = Optional.ofNullable(dto.getAdultQuantity()).orElse(0);
        this.elderlyQuantity = Optional.ofNullable(dto.getElderlyQuantity()).orElse(0);
        this.olderChildQuantity = Optional.ofNullable(dto.getOlderChildQuantity()).orElse(0);
        this.youngChildQuantity = Optional.ofNullable(dto.getYoungChildQuantity()).orElse(0);

        // replenishRoomNum 是 BigDecimal 类型，它可以为 null，但为了避免后续操作的 NPE，
        // 最好也在构造时进行 null 检查，如果不需要进行计算可以保持 null，
        // 如果后续会直接使用其值进行计算（如 hasRoomSupplement()），
        // 那么在赋值时可以考虑赋予 BigDecimal.ZERO 或其他默认值，取决于业务逻辑。
        // 这里我们选择直接赋值，并在 hasRoomSupplement() 中进行 null 检查。
        this.replenishRoomNum = dto.getReplenishRoomNum();
    }
    
    /**
     * 获取总人数
     */
    public int getTotalQuantity() {
        return adultQuantity + elderlyQuantity + olderChildQuantity + youngChildQuantity;
    }
    
    /**
     * 获取儿童总数（大童+小童）
     */
    public int getTotalChildQuantity() {
        return olderChildQuantity + youngChildQuantity;
    }
    
    /**
     * 是否有成人
     */
    public boolean hasAdult() {
        return adultQuantity > 0;
    }
    
    /**
     * 是否有老人
     */
    public boolean hasElderly() {
        return elderlyQuantity > 0;
    }
    
    /**
     * 是否有儿童
     */
    public boolean hasChild() {
        return getTotalChildQuantity() > 0;
    }
    
    /**
     * 是否需要房差
     */
    public boolean hasRoomSupplement() {
        return replenishRoomNum != null && replenishRoomNum.compareTo(BigDecimal.ZERO) > 0;
    }
} 