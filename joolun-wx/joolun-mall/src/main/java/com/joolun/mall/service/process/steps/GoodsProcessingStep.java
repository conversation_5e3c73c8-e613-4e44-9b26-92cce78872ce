package com.joolun.mall.service.process.steps;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.constant.GoodsSpuUploadFileConstants;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.mall.dto.OrderCreationContext;
import com.joolun.mall.dto.PlaceOrderGoodsDTO;
import com.joolun.mall.entity.*;
import com.joolun.mall.service.GoodsSpuService;
import com.joolun.mall.service.GoodsSpuUploadFileService;
import com.joolun.mall.service.IGoodsSkuService;
import com.joolun.mall.service.process.OrderProcessStep;
import com.joolun.mall.service.process.ProcessResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/** 商品处理步骤 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsProcessingStep implements OrderProcessStep {

  private final GoodsSpuService goodsSpuService;
  private final IGoodsSkuService goodsSkuService;
  private final GoodsSpuUploadFileService goodsSpuUploadFileService;

  @Override
  public ProcessResult execute(OrderCreationContext context) {
    log.info("开始执行商品处理步骤");

    // 卫语句：SKU列表为空
    //        if (CollectionUtils.isEmpty(context.getPlaceOrderDTO().getSkus())) {
    //            return ProcessResult.failure("商品SKU列表不能为空");
    //        }

    AtomicInteger processedCount = new AtomicInteger(0);
    // 使用方式
    String spuId = getSpuIdBySkuId(context.getPlaceOrderDTO().getSkuId());
    ProcessResult result = processOrderGoods(context, spuId);
    if (result.isSuccess()) {
      processedCount.incrementAndGet();
    } else {
      log.warn("商品处理失败: {}, 商品ID: {}", result.getMessage(), spuId);
      context.addWarning(String.format("商品[%s]处理失败: %s", spuId, result.getMessage()));
    }

    // 卫语句：没有成功处理的商品
    if (processedCount.get() == 0) {
      return ProcessResult.failure("没有成功处理的商品");
    }

    log.info("商品处理完成, 成功处理数量: {}", processedCount.get());
    return ProcessResult.success();
  }

  /** 处理单个商品（使用卫语句） */
  private ProcessResult processOrderGoods(OrderCreationContext context, String spuId) {
    // 卫语句：验证商品ID
    if (StringUtils.isBlank(spuId)) {
      return ProcessResult.failure("商品ID不能为空");
    }

    // 查找商品
    GoodsSpu goodsSpu = findGoodsSpu(spuId);

    // 卫语句：商品不存在
    if (goodsSpu == null) {
      return ProcessResult.failure("商品不存在或已下架");
    }

    // 卫语句：商品未上架
    if (!CommonConstants.YES.equals(goodsSpu.getShelf())) {
      return ProcessResult.failure("商品未上架");
    }

    // 卫语句：商品图片为空
    List<GoodsSpuUploadFile> picUrls =
        goodsSpuUploadFileService.list(
            Wrappers.<GoodsSpuUploadFile>lambdaQuery()
                .eq(GoodsSpuUploadFile::getDeleteFlag, Boolean.FALSE)
                .eq(GoodsSpuUploadFile::getObjectId, goodsSpu.getId()));
    goodsSpu.setPicUrls(picUrls);
    if (CollectionUtils.isEmpty(goodsSpu.getPicUrls())) {
      return ProcessResult.failure("商品图片信息缺失");
    }

    // 设置旅行社ID
    context.getOrderInfo().setTravelId(goodsSpu.getTravelId());

    // 构建订单项
    OrderItem orderItem = buildOrderItem(context, goodsSpu);
    context.getOrderItems().add(orderItem);
    context.getGoodsSpus().add(goodsSpu);

    // 处理出行人信息
    processTravelers(context);

    log.info(
        "商品处理成功, 商品ID: {}, 商品名称: {}, 当前库存: {}",
        goodsSpu.getId(),
        goodsSpu.getName(),
        context.getGoodsSkuCalendarPrice().getStock());

    return ProcessResult.success();
  }

  /** 查找商品 */
  private GoodsSpu findGoodsSpu(String spuId) {
    return goodsSpuService.getOne(
        Wrappers.<GoodsSpu>lambdaQuery()
            .eq(GoodsSpu::getId, spuId)
            .eq(GoodsSpu::getShelf, CommonConstants.YES));
  }

  /** 构建订单项 - 适配新SKU结构 */
  private OrderItem buildOrderItem(OrderCreationContext context, GoodsSpu goodsSpu) {
    OrderItem orderItem = new OrderItem();
    orderItem.setOrderId(context.getOrderInfo().getId());
    orderItem.setSpuId(goodsSpu.getId());
    orderItem.setSpuName(goodsSpu.getName());
    orderItem.setPicUrl(getMainPicUrl(goodsSpu));
    orderItem.setSpecs(context.getOrderInfo().getSpecs());

    // 设置数量信息
    int totalQuantity = context.getConfig().getTotalQuantity();
    orderItem.setQuantity(totalQuantity);
    orderItem.setElderlyQuantity(context.getConfig().getElderlyQuantity());
    orderItem.setAdultQuantity(context.getConfig().getAdultQuantity());
    orderItem.setOlderChildQuantity(context.getConfig().getOlderChildQuantity());
    orderItem.setYoungChildQuantity(context.getConfig().getYoungChildQuantity());

    orderItem.setSalesPrice(goodsSpu.getSalesPrice());
    orderItem.setDetailId(context.getPlaceOrderDTO().getDetailId());

    // 设置出发日期
    orderItem.setDepartureDate(context.getGoodsSkuCalendarPrice().getCalendarDate());

    // === 新增SKU相关信息设置 ===
    setOrderItemSkuInfo(context, orderItem);

    // 设置退款比例
    orderItem.setFifteenDayRefun(goodsSpu.getFifteenDayRefun());
    orderItem.setSevenDayRefund(goodsSpu.getSevenDayRefund());
    orderItem.setFortyEightHoursRefun(goodsSpu.getFortyEightHoursRefun());
    orderItem.setTwentyFourHoursRefun(goodsSpu.getTwentyFourHoursRefun());

    return orderItem;
  }

  /** 设置订单项的SKU信息 - 使用卫语句优化嵌套 */
  private void setOrderItemSkuInfo(OrderCreationContext context, OrderItem orderItem) {
    // 卫语句：SKU列表为空
    if (CollectionUtils.isEmpty(context.getPlaceOrderDTO().getSkus())) {
      return;
    }

    PlaceOrderGoodsDTO orderGoods = context.getPlaceOrderDTO().getSkus().get(0);

    // 卫语句：SKU ID为空
    if (StringUtils.isBlank(orderGoods.getSkuId())) {
      return;
    }

    // 设置SKU ID
    orderItem.setSkuId(orderGoods.getSkuId());
    log.info(
        "[SKU_INFO] 设置订单项SKU信息: itemId={}, skuId={}", orderItem.getId(), orderGoods.getSkuId());

    // 设置SKU名称（如果上下文中有的话）
    setSkuNameFromContext(context, orderItem);
  }

  /** 从上下文设置SKU名称 */
  private void setSkuNameFromContext(OrderCreationContext context, OrderItem orderItem) {
    // 卫语句：上下文中没有SKU信息
    if (context.getGoodsSku() == null) {
      return;
    }

    orderItem.setSkuName(context.getGoodsSku().getSkuName());
    log.info("[SKU_INFO] 设置SKU名称: skuName={}", context.getGoodsSku().getSkuName());
  }

  /** 处理出行人信息 */
  private void processTravelers(OrderCreationContext context) {
    List<UserCertInfo> userCertInfos = context.getPlaceOrderDTO().getUserCertInfos();

    // 卫语句：出行人信息为空
    if (CollectionUtils.isEmpty(userCertInfos)) {
      log.warn("出行人信息为空");
      return;
    }

    // 构建用户ID数组和出行人列表
    String[] userIds = new String[userCertInfos.size()];
    for (int i = 0; i < userCertInfos.size(); i++) {
      userIds[i] = userCertInfos.get(i).getId();

      OrderTravelers orderTravelers = new OrderTravelers();
      orderTravelers.setId(IdUtils.simpleUUID());
      orderTravelers.setUserId(userCertInfos.get(i).getId());
      orderTravelers.setCreateTime(DateUtils.getNowDate());
      context.getTravelers().add(orderTravelers);
    }

    // 设置到最后一个订单项（当前只处理一个订单项的情况）
    if (!context.getOrderItems().isEmpty()) {
      OrderItem lastOrderItem = context.getOrderItems().get(context.getOrderItems().size() - 1);
      lastOrderItem.setJoinUser(userIds);
    }
  }

  @Override
  public String getStepName() {
    return "商品处理";
  }

  @Override
  public int getOrder() {
    return 3;
  }

  private String getSpuIdBySkuId(String skuId) {
    if (StringUtils.isBlank(skuId)) {
      return null;
    }
    GoodsSku goodsSku = goodsSkuService.getById(skuId);
    return goodsSku != null ? goodsSku.getGoodsId() : null;
  }

  /**
   * 获取商品主图URL 优先级：主图(objectType=0) > 排序最小的图片 > 第一张图片
   *
   * @param goodsSpu 商品SPU信息
   * @return 主图URL，如果没有图片则返回空字符串
   */
  private String getMainPicUrl(GoodsSpu goodsSpu) {
    List<GoodsSpuUploadFile> picUrls = goodsSpu.getPicUrls();

    // 卫语句：图片列表为空
    if (CollectionUtils.isEmpty(picUrls)) {
      log.warn("商品[{}]没有图片信息", goodsSpu.getId());
      return "";
    }

    // 第一优先级：查找主图(objectType为0)
    for (GoodsSpuUploadFile picFile : picUrls) {
      if (GoodsSpuUploadFileConstants.UPLOAD_FILE_TYPE_DEFAULT.equals(picFile.getObjectType())) {
        log.debug("商品[{}]使用主图: {}", goodsSpu.getId(), picFile.getFileUrl());
        return picFile.getFileUrl();
      }
    }

    // 第二优先级：按fileSort排序，取最小值（排序最靠前）
    GoodsSpuUploadFile minSortPic =
        picUrls.stream()
            .filter(pic -> pic.getFileSort() != null)
            .min((pic1, pic2) -> Integer.compare(pic1.getFileSort(), pic2.getFileSort()))
            .orElse(null);

    if (minSortPic != null) {
      log.debug(
          "商品[{}]使用排序最小图片: {}, sort: {}",
          goodsSpu.getId(),
          minSortPic.getFileUrl(),
          minSortPic.getFileSort());
      return minSortPic.getFileUrl();
    }

    // 第三优先级：使用第一张图片（兜底策略）
    String firstPicUrl = picUrls.get(0).getFileUrl();
    log.debug("商品[{}]使用第一张图片: {}", goodsSpu.getId(), firstPicUrl);
    return firstPicUrl;
  }
}
