package com.joolun.mall.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 订单相关枚举
 */
public enum OrderInfoEnum implements IEnum<String> {

	STATUS_0("0","待付款"),
	STATUS_1("1","待发货"),  // 暂时不用，当前业务直接跳过此状态
	STATUS_2("2","待使用"),
	STATUS_3("3","已完成"),
	STATUS_4("4","退款中"),
	STATUS_5("5","已取消"),
	STATUS_6("6","已退款"),
	STATUS_7("7","同意退款"),
	STATUS_8("8","拒绝退款");



	OrderInfoEnum(final String value, final String desc) {
		this.value = value;
		this.desc = desc;
	}
	public static String STATUS_PREFIX = "STATUS";
	private String value;
	private String desc;

	@Override
	public String getValue() {
		return this.value;
	}

	@JsonValue
	public String getDesc(){
		return this.desc;
	}
}
