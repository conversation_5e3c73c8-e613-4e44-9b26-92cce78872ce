package com.joolun.mall.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.common.utils.DateUtils;
import com.joolun.common.utils.StringUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.mall.entity.UserCertInfo;
import com.joolun.mall.mapper.UserCertInfoMapper;
import com.joolun.mall.service.IUserCertInfoService;
import com.joolun.framework.utils.ThirdSessionHolder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户身份信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-12
 */
@Service
public class UserCertInfoServiceImpl extends ServiceImpl<UserCertInfoMapper, UserCertInfo>
    implements IUserCertInfoService {
  @Resource private UserCertInfoMapper userCertInfoMapper;

  /**
   * 查询用户身份信息
   *
   * @param id 用户身份信息ID
   * @return 用户身份信息
   */
  @Override
  public UserCertInfo selectUserCertInfoById(String id) {
    return userCertInfoMapper.selectUserCertInfoById(id);
  }

  /**
   * 查询用户身份信息列表
   *
   * @param userCertInfo 用户身份信息
   * @return 用户身份信息
   */
  @Override
  public List<UserCertInfo> selectUserCertInfoList(UserCertInfo userCertInfo) {
    return userCertInfoMapper.selectCertInfo(userCertInfo);
  }

  /**
   * 新增用户身份信息
   *
   * @param userCertInfo 用户身份信息
   * @return 结果
   */
  @Override
  public int insertUserCertInfo(UserCertInfo userCertInfo) {
    userCertInfo.setCreateTime(DateUtils.getNowDate());
    return userCertInfoMapper.insertUserCertInfo(userCertInfo);
  }

  /**
   * 修改用户身份信息
   *
   * @param userCertInfo 用户身份信息
   * @return 结果
   */
  @Override
  public int updateUserCertInfo(UserCertInfo userCertInfo) {
    userCertInfo.setUpdateTime(DateUtils.getNowDate());
    return userCertInfoMapper.updateUserCertInfo(userCertInfo);
  }

  /**
   * 批量删除用户身份信息
   *
   * @param ids 需要删除的用户身份信息ID
   * @return 结果
   */
  @Override
  public int deleteUserCertInfoByIds(String[] ids) {
    return userCertInfoMapper.deleteUserCertInfoByIds(ids);
  }

  /**
   * 删除用户身份信息信息
   *
   * @param id 用户身份信息ID
   * @return 结果
   */
  @Override
  public int deleteUserCertInfoById(String id) {
    return userCertInfoMapper.deleteUserCertInfoById(id);
  }

  @Override
  public List<UserCertInfo> selectCertInfo(UserCertInfo userCertInfo) {
    return userCertInfoMapper.selectCertInfo(userCertInfo);
  }

  @Override
  public IPage<UserCertInfo> selectByPageForApp(IPage<UserCertInfo> page, UserCertInfo userCertInfo) {
    return userCertInfoMapper.selectByPage(page, userCertInfo);
  }

  @Override
  public int saveOrUpdateUserCertInfo(UserCertInfo userCertInfo) {
    int r = 0;
    if (StringUtils.isNotEmpty(userCertInfo.getId())) {
      userCertInfo.setUpdateTime(DateUtils.getNowDate());
      userCertInfo.setUpdateBy(ThirdSessionHolder.getWxUserId());
      r = userCertInfoMapper.updateUserCertInfo(userCertInfo);
    } else {
      userCertInfo.setCreateBy(ThirdSessionHolder.getWxUserId());
      userCertInfo.setCreateTime(DateUtils.getNowDate());
      userCertInfo.setId(IdUtils.simpleUUID());
      r = userCertInfoMapper.insertUserCertInfo(userCertInfo);
    }
    return r;
  }

  @Override
  public Map<String, List<UserCertInfo>> selectOrderTravelersByOrderIds(List<String> orderIds) {
    if (ObjectUtil.isEmpty(orderIds)) {
      return new HashMap<>();
    }

    List<UserCertInfo> userCertInfoList =
        userCertInfoMapper.selectOrderTravelersByOrderIds(orderIds);
    if (ObjectUtil.isEmpty(userCertInfoList)) {
      return new HashMap<>();
    }

    return userCertInfoList.stream().collect(Collectors.groupingBy(UserCertInfo::getOrderId));
  }
}
