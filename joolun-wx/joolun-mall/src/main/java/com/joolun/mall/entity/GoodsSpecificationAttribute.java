package com.joolun.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.joolun.common.annotation.Excel;
import lombok.*;

import java.util.Date;

/** 商品规格名 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_specification_attribute")
public class GoodsSpecificationAttribute {
  @TableId(type = IdType.ASSIGN_ID)
  private String id;

  @TableField(value = "attr_name")
  private String attrName;

  @TableField(value = "goods_id")
  private String goodsId;

  /** 是否套餐 */
  @TableField(value = "is_package")
  private String isPackage;

  /** 是否共有属性: 0-不是;1-是; */
  @TableField(value = "common_attr")
  private Boolean commonAttr;

  /** 旅行社id */
  @Excel(name = "旅行社id")
  private String travelId;

  /** 删除标记：0-未删除;1-已删除; */
  @TableField(value = "delete_flag")
  private Boolean deleteFlag;

  @TableField(value = "create_by", fill = FieldFill.INSERT)
  private String createBy;

  @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
  private String updateBy;

  /** 创建时间 */
  @TableField(value = "create_time", fill = FieldFill.INSERT)
  private Date createTime;

  /** 修改时间 */
  @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
  private Date updateTime;

  @TableField(value = "sort_order")
  private Integer sortOrder;

  public static final String COL_ID = "id";

  public static final String COL_ATTR_NAME = "attr_name";

  public static final String COL_GOODS_ID = "goods_id";

  public static final String COL_IS_PACKAGE = "is_package";

  public static final String COL_COMMON_ATTR = "common_attr";

  public static final String COL_DELETE_FLAG = "delete_flag";

  public static final String COL_CREATE_BY = "create_by";

  public static final String COL_CREATE_TIME = "create_time";

  public static final String COL_UPDATE_BY = "update_by";

  public static final String COL_UPDATE_TIME = "update_time";

  public static final String COL_SORT_ORDER = "sort_order";
}
