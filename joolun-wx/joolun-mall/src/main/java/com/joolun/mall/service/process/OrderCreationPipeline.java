package com.joolun.mall.service.process;

import com.joolun.common.exception.CustomException;
import com.joolun.mall.dto.OrderCreationContext;
import com.joolun.mall.entity.OrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单创建流程编排器
 */
@Slf4j
@Component
public class OrderCreationPipeline {

    private final List<OrderProcessStep> steps;
    
    public OrderCreationPipeline(List<OrderProcessStep> steps) {
        // 按照order字段排序
        this.steps = steps.stream()
            .sorted(Comparator.comparingInt(OrderProcessStep::getOrder))
            .collect(Collectors.toList());
    }
    
    /**
     * 执行订单创建流程
     */
    public OrderInfo execute(OrderCreationContext context) {
        log.info("开始执行订单创建流程, 共{}个步骤", steps.size());
        
        for (OrderProcessStep step : steps) {
            executeStep(step, context);
        }
        
        logExecutionSummary(context);
        return context.getOrderInfo();
    }
    
    /**
     * 执行单个步骤（使用卫语句优化）
     */
    private void executeStep(OrderProcessStep step, OrderCreationContext context) {
        log.info("执行步骤: {}", step.getStepName());
        
        ProcessResult result = executeStepSafely(step, context);
        
        // 卫语句：处理成功情况，提前返回
        if (result.isSuccess()) {
            log.info("步骤执行成功: {}", step.getStepName());
            return;
        }
        
        // 卫语句：处理失败情况
        log.error("步骤执行失败: {}, 错误信息: {}", step.getStepName(), result.getMessage());

    // 卫语句：处理关键步骤失败
    if (step.isCritical()) {
      log.error("关键步骤[{}]执行失败: {}", step.getStepName(), result.getMessage());
      throw new CustomException(String.format("[%s]: %s", step.getStepName(), result.getMessage()));
    }

        // 非关键步骤失败，记录警告并继续
        log.warn("非关键步骤失败，继续执行后续步骤");
        context.addWarning(result.getMessage());
    }
    
    /**
     * 安全执行步骤，捕获异常
     */
    private ProcessResult executeStepSafely(OrderProcessStep step, OrderCreationContext context) {
        try {
            return step.execute(context);
        } catch (Exception e) {
            log.error("步骤执行异常: {}, 异常信息: {}", step.getStepName(), e.getMessage(), e);
            throw new CustomException(
                String.format("步骤[%s]执行异常: %s", step.getStepName(), e.getMessage()), e);
        }
    }
    
    /**
     * 记录执行总结
     */
    private void logExecutionSummary(OrderCreationContext context) {
        OrderInfo orderInfo = context.getOrderInfo();
        
        // 卫语句：订单为空
        if (orderInfo == null) {
            log.error("订单创建流程执行完成，但订单信息为空");
            return;
        }
        
        log.info("订单创建流程执行完成, 订单ID: {}, 订单号: {}, 总金额: {}", 
            orderInfo.getId(), orderInfo.getOrderNo(), orderInfo.getPaymentPrice());
            
        // 记录警告信息
        if (context.hasWarnings()) {
            log.warn("执行过程中产生{}个警告: {}", context.getWarnings().size(), context.getWarnings());
        }
    }
} 