package com.joolun.mall.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 小程序端SKU价格日历对象 SkuCalendarPriceAppDTO
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuCalendarPriceAppDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 价格记录ID */
    @ApiModelProperty(value = "价格记录ID")
    private String id;

    /** SKU ID */
    @ApiModelProperty(value = "SKU ID")
    private String skuId;

    /** 日期 yyyy-MM-dd */
    @ApiModelProperty(value = "日期 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String date;

    /** 成人价格 */
    @ApiModelProperty(value = "成人价格")
    private BigDecimal adultPrice;

    /** 儿童价格 */
    @ApiModelProperty(value = "儿童价格")
    private BigDecimal childPrice;

    /** 库存 */
    @ApiModelProperty(value = "库存")
    private Integer stock;

    /** 状态 1-可售 2-停售 0-无数据(置灰) */
    @ApiModelProperty(value = "状态 1-可售 2-停售 0-无数据(置灰)")
    private Integer status;

    /** 佣金比例 */
    @ApiModelProperty(value = "佣金比例")
    private BigDecimal commission;

    /** 星期几 */
    @ApiModelProperty(value = "星期几")
    private String dayOfWeek;
}