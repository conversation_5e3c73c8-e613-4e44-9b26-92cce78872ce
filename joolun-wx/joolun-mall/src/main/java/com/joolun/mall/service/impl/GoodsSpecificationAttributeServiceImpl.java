package com.joolun.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.common.exception.CustomException;
import com.joolun.common.utils.SecurityUtils;
import com.joolun.common.utils.uuid.IdUtils;
import com.joolun.mall.dto.GoodsSpecsAttrDTO;
import com.joolun.mall.dto.GoodsSpecsAttrTagValueDTO;
import com.joolun.mall.dto.GoodsSpecsAttrTagsDTO;
import com.joolun.mall.dto.GoodsSpecsDTO;
import com.joolun.mall.entity.GoodsSpecificationValue;
import com.joolun.mall.entity.GoodsSpecificationAttribute;
import com.joolun.mall.mapper.GoodsSpecsAttrMapper;
import com.joolun.mall.service.GoodsSpecificationAttributeService;
import com.joolun.mall.service.GoodsSpecificationValueService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class GoodsSpecificationAttributeServiceImpl extends ServiceImpl<GoodsSpecsAttrMapper, GoodsSpecificationAttribute>
    implements GoodsSpecificationAttributeService {

  @Resource private GoodsSpecificationValueService goodsSpecsService;

  @Override
  public boolean addAttrAndAttrValues(GoodsSpecsAttrDTO dto) {
    if (ObjectUtil.isNull(dto.getAttrName())) {
      throw new CustomException("规格名为空");
    }

    if (ObjectUtil.isNull(dto.getGoodsId())) {
      throw new CustomException("产品ID为空");
    }

    GoodsSpecificationAttribute item = new GoodsSpecificationAttribute();
    BeanUtil.copyProperties(dto, item);
    if (ObjectUtil.isNotNull(dto.getId())) {
      this.updateById(item);
    } else {
      List<GoodsSpecificationAttribute> list = this.list(this.initQueryWrapper(dto));
      Set<String> collect =
          list.stream().map(GoodsSpecificationAttribute::getAttrName).collect(Collectors.toSet());
      if (collect.contains(dto.getAttrName())) {
        // 已存在
        throw new CustomException("产品下已存在相同规格名，请修改");
      } else {
        item.setId(IdUtils.simpleUUID());
        this.save(item);
      }
    }

    if (ObjectUtil.isNotEmpty(dto.getGoodsSpecificationValueList())) {
      // 批量添加明细
      goodsSpecsService.batchSaveByAttr(dto.getGoodsSpecificationValueList(), item.getId());
    }
    return Boolean.TRUE;
  }

  @Override
  public List<GoodsSpecificationAttribute> selectByGoodsId(GoodsSpecsAttrDTO dto) {
//    if (ObjectUtil.isNull(dto.getGoodsId())) {
//      throw new CustomException("产品ID为空");
//    }
    return this.list(this.initQueryWrapper(dto));
  }

  @Override
  public List<GoodsSpecificationAttribute> selectAll() {
    return this.list(this.initQueryWrapper(GoodsSpecsAttrDTO.builder().build()));
  }

  /**
   * 初始化查询条件
   *
   * @param dto
   * @return
   */
  private LambdaQueryWrapper<GoodsSpecificationAttribute> initQueryWrapper(GoodsSpecsAttrDTO dto) {
    LambdaQueryWrapper<GoodsSpecificationAttribute> wrapper = new LambdaQueryWrapper<>();
    wrapper
        .eq(ObjectUtil.isNotNull(dto.getGoodsId()), GoodsSpecificationAttribute::getGoodsId, dto.getGoodsId())
        .like(
            ObjectUtil.isNotNull(dto.getAttrName()),
            GoodsSpecificationAttribute::getAttrName,
            dto.getAttrName() + "%")
        .eq(
            ObjectUtil.isNotNull(dto.getCommonAttr()),
            GoodsSpecificationAttribute::getCommonAttr,
            dto.getCommonAttr())
        .eq(
            ObjectUtil.isNotNull(dto.getIsPackage()),
            GoodsSpecificationAttribute::getIsPackage,
            dto.getIsPackage())
        .eq(GoodsSpecificationAttribute::getDeleteFlag, Boolean.FALSE)
        .orderByAsc(GoodsSpecificationAttribute::getSortOrder);
    return wrapper;
  }

  @Override
  public boolean removeAttr(GoodsSpecsAttrDTO dto) {
    LambdaUpdateWrapper<GoodsSpecificationValue> goodsSpecsSet =
        Wrappers.<GoodsSpecificationValue>lambdaUpdate()
            .set(GoodsSpecificationValue::getDeleteFlag, Boolean.TRUE)
            .eq(GoodsSpecificationValue::getAttrId, dto.getId())
            .eq(GoodsSpecificationValue::getDeleteFlag, Boolean.FALSE);
    goodsSpecsService.update(goodsSpecsSet);

//    List<GoodsSpecs> goodsSpecs =
//        goodsSpecsService.selectByDto(GoodsSpecsDTO.builder().attrId(dto.getId()).build());
//    if (ObjectUtil.isNotEmpty(goodsSpecs)) {
//      throw new CustomException("当前规格名下存在规格值，请优先删除规格值后再删除规格名");
//    }

    LambdaUpdateWrapper<GoodsSpecificationAttribute> set =
        Wrappers.<GoodsSpecificationAttribute>lambdaUpdate()
            .set(GoodsSpecificationAttribute::getDeleteFlag, Boolean.TRUE)
            .eq(GoodsSpecificationAttribute::getId, dto.getId());
    return this.update(set);
  }

  @Override
  public boolean addNonPackageSpu(GoodsSpecsAttrTagsDTO dto) {
    // 判断规格是否存在，如果不存在新增，如果存在则更新
    GoodsSpecificationAttribute build =
        GoodsSpecificationAttribute.builder().goodsId(dto.getGoodsId()).attrName(dto.getName()).build();
    boolean result = this.saveOrUpdate(build);

    List<GoodsSpecificationValue> goodsSpecs =
        goodsSpecsService.selectByDto(GoodsSpecsDTO.builder().attrId(build.getId()).build());
    Map<String, GoodsSpecificationValue> gsMap =
        goodsSpecs.stream().collect(Collectors.toMap(GoodsSpecificationValue::getId, a -> a, (k1, k2) -> k1));

    if (result) {
      // 判断规格值是否存在，如果不存在新增，如果存在更新
      for (GoodsSpecsAttrTagValueDTO tagValueDTO : dto.getTages()) {
        GoodsSpecificationValue specs = gsMap.get(Long.parseLong(tagValueDTO.getId()));
        if (ObjectUtil.isNull(specs)) {
          goodsSpecsService.save(
              GoodsSpecificationValue.builder()
//                  .goodsId(dto.getGoodsId())
                  .attrId(build.getId())
                  .name(tagValueDTO.getName())
                  .build());
        } else {
          goodsSpecsService.updateById(
              GoodsSpecificationValue.builder()
//                  .goodsId(dto.getGoodsId())
                  .id(tagValueDTO.getId())
                  .attrId(build.getId())
                  .name(tagValueDTO.getName())
                  .build());
        }
      }
    }

    //    GoodsSpuDetail.GoodsSpuDetailBuilder goodsSpuDetailBuilder =
    //            GoodsSpuDetail.builder()
    //                    .goodsId(dto.getGoodsId())
    //                    .adultPrice(dto.getAdultPrice())
    //                    .childPrice(dto.getChildPrice())
    //                    .stock(dto.getStock())
    //                    .commission(dto.getCommission())
    //                    .date(dto.getDate())
    //                    .endTime(dto.getEndTime());
    //
    //    if (ObjectUtil.isNotNull(dto.getId())) {
    //      goodsSpuDetailBuilder.id(dto.getId());
    //    }
    //    GoodsSpuDetail build = goodsSpuDetailBuilder.build();
    //    boolean result = this.saveOrUpdate(build);
    //
    //    for (GoodsSpecsAttrTagsDTO tag : dto.getTags()) {
    //
    //      attrDetailService.deleteByGsdIdAndAttrIds(
    //              build.getId(),
    //              tag.getTag().stream()
    //                      .map(GoodsSpecsAttrTagValueDTO::getId)
    //                      .collect(Collectors.toSet()));
    //
    //      for (GoodsSpecsAttrTagValueDTO tagValueDTO : tag.getTag()) {
    //        attrDetailService.save(
    //                GoodsSpecsAttrDetail.builder()
    //                        .goodsId(dto.getGoodsId())
    //                        .gsdId(build.getId())
    //                        .attrId(tagValueDTO.getId())
    //                        .build());
    //      }
    //    }
    return result;
  }

  @Override
  public List<GoodsSpecsAttrTagsDTO> selectByGoodsIdReturnAttrTagsDTO(GoodsSpecsAttrDTO dto) {
    if (ObjectUtil.isNull(dto.getGoodsId())) {
      throw new CustomException("产品ID为空");
    }
    List<GoodsSpecificationAttribute> list = this.list(this.initQueryWrapper(dto));
    List<GoodsSpecsAttrTagsDTO> result = new ArrayList<>();
    for (GoodsSpecificationAttribute goodsSpecificationAttribute : list) {
      GoodsSpecsAttrTagsDTO tempTags =
              BeanUtil.copyProperties(goodsSpecificationAttribute, GoodsSpecsAttrTagsDTO.class);
      tempTags.setName(goodsSpecificationAttribute.getAttrName());

      LambdaQueryWrapper<GoodsSpecificationValue> specsWrapper = new LambdaQueryWrapper<>();
      specsWrapper
              .eq(GoodsSpecificationValue::getAttrId, goodsSpecificationAttribute.getId())
              .eq(GoodsSpecificationValue::getDeleteFlag, Boolean.FALSE);
      List<GoodsSpecificationValue> goodsSpecificationValueList = goodsSpecsService.list(specsWrapper);
      List<GoodsSpecsAttrTagValueDTO> tagValueDTOS = new ArrayList<>();
      if (ObjectUtil.isNotEmpty(goodsSpecificationValueList)) {
        for (GoodsSpecificationValue specs : goodsSpecificationValueList) {
          tagValueDTOS.add(
                  GoodsSpecsAttrTagValueDTO.builder()
                          .id(specs.getId().toString())
                          .name(specs.getName())
                          .build());
        }
        tempTags.setTages(tagValueDTOS);
      }
      result.add(tempTags);
    }
    return result;
  }

  @Override
  public boolean add(GoodsSpecsAttrDTO dto) {
    if (ObjectUtil.isNull(dto.getAttrName())) {
      throw new CustomException("请填写规格名");
    }

    if (ObjectUtil.isEmpty(dto.getGoodsSpecificationValueList())) {
      throw new CustomException("请填写规格值");
    }

    GoodsSpecificationAttribute item = new GoodsSpecificationAttribute();
    BeanUtil.copyProperties(dto, item);
    if (ObjectUtil.isNotNull(dto.getId())) {
      this.updateById(item);
    } else {
      List<GoodsSpecificationAttribute> list = this.list(this.initQueryWrapper(dto));
      Set<String> collect =
              list.stream().map(GoodsSpecificationAttribute::getAttrName).collect(Collectors.toSet());
      if (collect.contains(dto.getAttrName())) {
        // 已存在
        throw new CustomException("已存在相同规格名，请修改");
      } else {
        item.setId(IdUtils.simpleUUID());
        item.setTravelId(SecurityUtils.getLoginUser().getUser().getTravelId());
        this.save(item);
      }
    }

    if (ObjectUtil.isNotEmpty(dto.getGoodsSpecificationValueList())) {
      // 批量添加明细
      goodsSpecsService.batchSaveByAttr(dto.getGoodsSpecificationValueList(), item.getId());
    }
    return Boolean.TRUE;
  }

  @Override
  public GoodsSpecsAttrDTO getAttrAndSpecsById(String id) {
    GoodsSpecificationAttribute byId = this.getById(id);
    GoodsSpecsAttrDTO result = BeanUtil.copyProperties(byId, GoodsSpecsAttrDTO.class);
    List<GoodsSpecificationValue> goodsSpecs =
        goodsSpecsService.selectList(GoodsSpecsDTO.builder().attrId(result.getId()).build());
    result.setGoodsSpecificationValueList(goodsSpecs);
    return result;
  }
}
