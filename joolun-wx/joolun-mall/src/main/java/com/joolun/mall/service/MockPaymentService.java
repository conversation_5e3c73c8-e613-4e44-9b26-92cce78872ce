
package com.joolun.mall.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.joolun.common.utils.DateUtils;
import com.joolun.mall.config.PaymentConfig;
import com.joolun.mall.entity.OrderInfo;
import com.joolun.mall.entity.OrderItem;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 模拟支付服务
 * 用于开发和测试环境的支付功能模拟
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
@Service
@AllArgsConstructor
@ConditionalOnProperty(name = "payment.mock.enabled", havingValue = "true")
public class MockPaymentService {

    private final PaymentConfig paymentConfig;
    private final OrderInfoService orderInfoService;
    
    // 支付任务管理
    private final ConcurrentHashMap<String, ScheduledFuture<?>> paymentTasks = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(10);

    /**
     * 初始化方法 - 验证配置加载
     */
    @PostConstruct
    public void init() {
        log.info("[MOCK_PAYMENT] ==================== 配置验证 ====================");
        log.info("[MOCK_PAYMENT] 模拟支付服务初始化完成");
        log.info("[MOCK_PAYMENT] 启用状态: {}", paymentConfig.getMock().isEnabled());
        log.info("[MOCK_PAYMENT] 成功率: {}%", paymentConfig.getMock().getSuccessRate());
        log.info("[MOCK_PAYMENT] 回调延迟: {} 秒", paymentConfig.getMock().getCallbackDelaySeconds());
        log.info("[MOCK_PAYMENT] 回调延迟开关: {}", paymentConfig.getMock().isCallbackDelay());
        log.info("[MOCK_PAYMENT] 失败原因: {}", paymentConfig.getMock().getFailureReason());
        log.info("[MOCK_PAYMENT] ================================================");
    }

    /**
     * 模拟统一下单
     * 
     * @param orderInfo 订单信息
     * @return 模拟支付结果
     */
    public Map<String, Object> mockUnifiedOrder(OrderInfo orderInfo) {
        log.info("[MOCK_PAYMENT] 模拟统一下单开始 - 订单号: {}, 金额: {}", 
            orderInfo.getOrderNo(), orderInfo.getPaymentPrice());

        // 卫语句：检查订单有效性
        if (orderInfo == null) {
            log.error("[MOCK_PAYMENT] 订单信息为空");
            throw new RuntimeException("订单信息不能为空");
        }

        if (orderInfo.getPaymentPrice() == null || orderInfo.getPaymentPrice().compareTo(BigDecimal.ZERO) < 0) {
            log.error("[MOCK_PAYMENT] 订单金额无效: {}", orderInfo.getPaymentPrice());
            throw new RuntimeException("订单金额无效");
        }

        // 模拟微信支付返回数据
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("appId", "wx_mock_app_id");
        mockResult.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
        mockResult.put("nonceStr", IdUtil.randomUUID());
        mockResult.put("package", "prepay_id=mock_prepay_id_" + IdUtil.fastSimpleUUID());
        mockResult.put("signType", "MD5");
        mockResult.put("paySign", "mock_sign_" + IdUtil.fastSimpleUUID());

        log.info("[MOCK_PAYMENT] 模拟统一下单完成 - 订单号: {}, 返回数据: {}", 
            orderInfo.getOrderNo(), JSONUtil.toJsonStr(mockResult));

        // 异步模拟支付回调
        if (paymentConfig.getMock().isCallbackDelay()) {
            schedulePaymentCallback(orderInfo);
        }

        return mockResult;
    }

    /**
     * 调度模拟支付回调
     * 
     * @param orderInfo 订单信息
     */
    private void schedulePaymentCallback(OrderInfo orderInfo) {
        int delaySeconds = paymentConfig.getMock().getCallbackDelaySeconds();
        log.info("[MOCK_PAYMENT] 配置的延迟时间: {}秒", delaySeconds); // 添加这行日志

        String orderNo = orderInfo.getOrderNo();
        
        // 创建延迟任务
        ScheduledFuture<?> future = scheduler.schedule(() -> {
            try {
                // 检查订单当前状态，避免重复处理
                OrderInfo currentOrderInfo = orderInfoService.getById(orderInfo.getId());
                if (currentOrderInfo == null) {
                    log.warn("[MOCK_PAYMENT] 订单不存在，跳过回调处理 - 订单号: {}", orderNo);
                    return;
                }
                
                // 如果订单已经支付，跳过处理
                if (!"0".equals(currentOrderInfo.getIsPay())) {
                    log.info("[MOCK_PAYMENT] 订单已支付，跳过回调处理 - 订单号: {}", orderNo);
                    return;
                }
                
                // 根据配置的成功率决定支付结果
                Random random = new Random();
                boolean isSuccess = random.nextInt(100) < paymentConfig.getMock().getSuccessRate();

                if (isSuccess) {
                    mockSuccessfulPaymentCallback(currentOrderInfo);
                } else {
                    mockFailedPaymentCallback(currentOrderInfo);
                }
                
            } catch (Exception e) {
                log.error("[MOCK_PAYMENT] 模拟支付回调异常 - 订单号: {}, 错误: {}", 
                    orderNo, e.getMessage(), e);
            } finally {
                // 清理任务记录
                paymentTasks.remove(orderNo);
            }
        }, paymentConfig.getMock().getCallbackDelaySeconds(), TimeUnit.SECONDS);
        
        // 保存任务引用，用于可能的取消操作
        paymentTasks.put(orderNo, future);
        log.info("[MOCK_PAYMENT] 任务已存储 - 订单号: {}, 当前任务数: {}", orderNo, paymentTasks.size());
        
        log.info("[MOCK_PAYMENT] 已调度支付回调任务 - 订单号: {}, 延迟: {}秒", 
            orderNo, paymentConfig.getMock().getCallbackDelaySeconds());
    }

    /**
     * @deprecated 使用 schedulePaymentCallback 替代
     */
    @Deprecated
    @Async
    public void mockPaymentCallbackAsync(OrderInfo orderInfo) {
        log.warn("[MOCK_PAYMENT] 使用了已弃用的 mockPaymentCallbackAsync 方法");
        schedulePaymentCallback(orderInfo);
    }

    /**
     * 模拟成功的支付回调
     * 
     * @param orderInfo 订单信息
     */
    private void mockSuccessfulPaymentCallback(OrderInfo orderInfo) {
        log.info("[MOCK_PAYMENT] 模拟支付成功回调 - 订单号: {}", orderInfo.getOrderNo());

        try {
            // 构造模拟的支付回调数据（限制32字符以内）
            String mockTransactionId = "MOCK_" + IdUtil.fastSimpleUUID().substring(0, 26);
            orderInfo.setTransactionId(mockTransactionId);
            orderInfo.setPaymentTime(DateUtils.getNowDate());

            // 调用真实的订单处理逻辑
            orderInfoService.notifyOrder(orderInfo);

            log.info("[MOCK_PAYMENT] 模拟支付成功处理完成 - 订单号: {}, 交易号: {}", 
                orderInfo.getOrderNo(), mockTransactionId);

        } catch (Exception e) {
            log.error("[MOCK_PAYMENT] 模拟支付回调处理失败 - 订单号: {}, 错误: {}", 
                orderInfo.getOrderNo(), e.getMessage(), e);
        }
    }

    /**
     * 模拟失败的支付回调
     * 
     * @param orderInfo 订单信息
     */
    private void mockFailedPaymentCallback(OrderInfo orderInfo) {
        log.warn("[MOCK_PAYMENT] 模拟支付失败回调 - 订单号: {}, 原因: {}", 
            orderInfo.getOrderNo(), paymentConfig.getMock().getFailureReason());

        // 在模拟支付失败的情况下，订单状态保持不变
        // 可以在这里添加支付失败的处理逻辑，比如发送通知等
    }

    /**
     * 模拟退款处理
     * 
     * @param orderItem 订单项
     * @return 模拟退款结果
     */
    public Map<String, Object> mockRefundProcess(OrderItem orderItem) {
        log.info("[MOCK_PAYMENT] 模拟退款开始 - 订单项ID: {}, 退款金额: {}", 
            orderItem.getId(), orderItem.getPaymentPrice());

        // 卫语句：检查退款条件
        if (orderItem == null) {
            log.error("[MOCK_PAYMENT] 订单项为空");
            throw new RuntimeException("订单项不能为空");
        }

        if (orderItem.getPaymentPrice() == null || orderItem.getPaymentPrice().compareTo(BigDecimal.ZERO) <= 0) {
            log.error("[MOCK_PAYMENT] 退款金额无效: {}", orderItem.getPaymentPrice());
            throw new RuntimeException("退款金额必须大于0");
        }

        // 模拟微信退款返回数据
        Map<String, Object> mockRefundResult = new HashMap<>();
        mockRefundResult.put("refundId", "MOCK_REFUND_" + IdUtil.fastSimpleUUID());
        mockRefundResult.put("outRefundNo", orderItem.getId());
        mockRefundResult.put("refundFee", orderItem.getPaymentPrice().multiply(new BigDecimal(100)).intValue());
        mockRefundResult.put("refundStatus", "SUCCESS");

        log.info("[MOCK_PAYMENT] 模拟退款完成 - 订单项ID: {}, 退款结果: {}", 
            orderItem.getId(), JSONUtil.toJsonStr(mockRefundResult));

        // 异步模拟退款回调
        mockRefundCallbackAsync(orderItem, (String) mockRefundResult.get("refundId"));

        return mockRefundResult;
    }

    /**
     * 异步模拟退款回调
     * 
     * @param orderItem 订单项
     * @param refundId 退款ID
     */
    @Async
    public void mockRefundCallbackAsync(OrderItem orderItem, String refundId) {
        try {
            // 模拟退款处理延迟
            TimeUnit.SECONDS.sleep(paymentConfig.getMock().getCallbackDelaySeconds());

            log.info("[MOCK_PAYMENT] 模拟退款回调 - 订单项ID: {}, 退款ID: {}", 
                orderItem.getId(), refundId);

            // 模拟退款成功回调数据
            // 这里可以调用真实的退款回调处理逻辑
            // orderInfoService.notifyRefunds(mockRefundNotifyResult);

            log.info("[MOCK_PAYMENT] 模拟退款回调处理完成 - 订单项ID: {}", orderItem.getId());

        } catch (InterruptedException e) {
            log.error("[MOCK_PAYMENT] 模拟退款回调被中断: {}", e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 检查是否启用模拟支付
     * 
     * @return 是否启用模拟支付
     */
    public boolean isMockPaymentEnabled() {
        boolean enabled = paymentConfig.getMock().isEnabled();
        log.debug("[MOCK_PAYMENT] 模拟支付状态: {}", enabled ? "启用" : "禁用");
        return enabled;
    }

    /**
     * 获取模拟支付配置信息
     * 
     * @return 配置信息
     */
    public PaymentConfig.Mock getMockConfig() {
        return paymentConfig.getMock();
    }

    /**
     * 模拟支付验证
     * 验证订单是否可以进行支付
     * 
     * @param orderInfo 订单信息
     * @return 验证结果
     */
    public boolean validatePaymentOrder(OrderInfo orderInfo) {
        // 卫语句：基本验证
        if (orderInfo == null) {
            log.error("[MOCK_PAYMENT] 订单验证失败：订单为空");
            return false;
        }

        if (orderInfo.getOrderNo() == null || orderInfo.getOrderNo().isEmpty()) {
            log.error("[MOCK_PAYMENT] 订单验证失败：订单号为空");
            return false;
        }

        if (orderInfo.getPaymentPrice() == null) {
            log.error("[MOCK_PAYMENT] 订单验证失败：支付金额为空");
            return false;
        }

        if (orderInfo.getPaymentPrice().compareTo(BigDecimal.ZERO) < 0) {
            log.error("[MOCK_PAYMENT] 订单验证失败：支付金额不能为负数");
            return false;
        }

        log.info("[MOCK_PAYMENT] 订单验证通过 - 订单号: {}, 金额: {}", 
            orderInfo.getOrderNo(), orderInfo.getPaymentPrice());
        return true;
    }

    /**
     * 模拟支付环境检查
     * 检查当前环境是否适合使用模拟支付
     * 
     * @param environment 当前环境
     * @return 是否适合使用模拟支付
     */
    public boolean isEnvironmentSuitableForMockPayment(String environment) {
        boolean suitable = "dev".equals(environment) || "test".equals(environment);
        
        if (!suitable) {
            log.warn("[MOCK_PAYMENT] 当前环境 {} 不适合使用模拟支付", environment);
        } else {
            log.info("[MOCK_PAYMENT] 当前环境 {} 适合使用模拟支付", environment);
        }
        
        return suitable;
    }
    
    /**
     * 取消支付任务
     * 当用户取消支付时调用此方法
     * 
     * @param orderNo 订单号
     * @return 是否成功取消
     */
    public boolean cancelPaymentTask(String orderNo) {
        ScheduledFuture<?> future = paymentTasks.get(orderNo);
        if (future != null) {
            boolean cancelled = future.cancel(false);
            if (cancelled) {
                paymentTasks.remove(orderNo);
                log.info("[MOCK_PAYMENT] 已取消支付回调任务 - 订单号: {}", orderNo);
                return true;
            } else {
                log.warn("[MOCK_PAYMENT] 取消支付回调任务失败 - 订单号: {}, 任务可能已执行", orderNo);
                return false;
            }
        } else {
            log.warn("[MOCK_PAYMENT] 未找到支付回调任务 - 订单号: {}", orderNo);
            return false;
        }
    }
    
    /**
     * 获取待处理的支付任务数量
     * 
     * @return 任务数量
     */
    public int getPendingPaymentTaskCount() {
        return paymentTasks.size();
    }
    
    /**
     * 清理所有支付任务
     */
    public void clearAllPaymentTasks() {
        int cancelledCount = 0;
        for (Map.Entry<String, ScheduledFuture<?>> entry : paymentTasks.entrySet()) {
            if (entry.getValue().cancel(false)) {
                cancelledCount++;
            }
        }
        paymentTasks.clear();
        log.info("[MOCK_PAYMENT] 已清理所有支付任务，取消数量: {}", cancelledCount);
    }
    
    /**
     * 资源清理
     */
    @PreDestroy
    public void destroy() {
        log.info("[MOCK_PAYMENT] 开始清理模拟支付服务资源");
        clearAllPaymentTasks();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("[MOCK_PAYMENT] 模拟支付服务资源清理完成");
    }
} 