/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，项目使用请保留此说明
 */
package com.joolun.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.joolun.common.annotation.Excel;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.enums.OrderInfoEnum;
import com.joolun.weixin.entity.WxUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
@Data
@TableName("order_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "商城订单")
public class OrderInfo extends Model<OrderInfo> {
	private static final long serialVersionUID = 1L;

	/**
	 * PK
	 */
	@ApiModelProperty(value = "PK")
	@TableId(type = IdType.ASSIGN_ID)
	@Excel(name = "ID")
	private String id;
	/**
	 * 逻辑删除标记（0：显示；1：隐藏）
	 */
	@ApiModelProperty(value = "逻辑删除标记")
	private String delFlag;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@Excel(name = "创建时间")
	private Date createTime;
	/**
	 * 最后更新时间
	 */
	@ApiModelProperty(value = "最后更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	@Excel(name = "用户ID")
	private String userId;
	/**
	 * 订单单号
	 */
	@ApiModelProperty(value = "订单单号")
	@Excel(name = "订单单号")
	private String orderNo;
	/**
	 * 支付方式1、货到付款；2、在线支付
	 */
	@ApiModelProperty(value = "支付方式")
	private String paymentWay;
	/**
	 * 是否支付0、未支付 1、已支付
	 */
	@ApiModelProperty(value = "是否支付0、未支付 1、已支付")
	private String isPay;
	/**
	 * 订单名
	 */
	@ApiModelProperty(value = "订单名")
	@Excel(name = "订单名")
	private String name;
	/**
	 * 订单状态:0-待付款;1-待发货;2-待使用;3-已完成;4-退款中;5-已取消;6-已退款;7-同意退款;8-拒绝退款;
	 */
	@ApiModelProperty(value = "订单状态:0-待付款;1-待发货;2-待使用;3-已完成;4-退款中;5-已取消;6-已退款;7-同意退款;8-拒绝退款;")
	@Excel(name = "订单状态:0-待付款;1-待发货;2-待使用;3-已完成;4-退款中;5-已取消;6-已退款;7-同意退款;8-拒绝退款;")
	private String status;
	/**
	 * 运费金额
	 */
	@ApiModelProperty(value = "运费金额")
	private BigDecimal freightPrice;
	/**
	 * 销售金额
	 */
	@ApiModelProperty(value = "销售金额")

	private BigDecimal salesPrice;
	/**
	 * 支付金额（销售金额+运费金额-积分抵扣金额-电子券抵扣金额）
	 */
	@ApiModelProperty(value = "支付金额（销售金额+运费金额-积分抵扣金额-电子券抵扣金额）")
	@Excel(name = "支付金额")
	private BigDecimal paymentPrice;
	/**
	 * 付款时间
	 */
	@ApiModelProperty(value = "付款时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@Excel(name = "付款时间")
	private Date paymentTime;
	/**
	 * 发货时间
	 */
	@ApiModelProperty(value = "发货时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date deliveryTime;
	/**
	 * 收货时间
	 */
	@ApiModelProperty(value = "收货时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date receiverTime;
	/**
	 * 成交时间
	 */
	@ApiModelProperty(value = "成交时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@Excel(name = "成交时间")
	private Date closingTime;
	/**
	 * 买家留言
	 */
	@ApiModelProperty(value = "买家留言")
	@Excel(name = "买家留言")
	private String userMessage;
	/**
	 * 支付交易ID
	 */
	@ApiModelProperty(value = "支付交易ID")
	@Excel(name = "支付交易ID")
	private String transactionId;
	/**
	 * 物流id
	 */
	@ApiModelProperty(value = "物流id")
	private String logisticsId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 订单详情
	 */
	@TableField(exist = false)
	@Excel(name = "订单详情")
	private List<OrderItem> listOrderItem;
	/**
	 * 订单状态过期时间
	 */
	@TableField(exist = false)
	private Long outTime;
	/**
	 * 状态0、待付款 1、待发货 2、待收货 3、已完成 4、已关闭
	 */
	@TableField(exist = false)
	private String statusDesc;

	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@TableField(exist = false)
	private Date beginTime;

	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@TableField(exist = false)
	private Date endTime;
	/**
	 * 订单物流
	 */
	@TableField(exist = false)
	private OrderLogistics orderLogistics;
	/**
	 * 物流商家
	 */
	@TableField(exist = false)
	private String logistics;
	/**
	 * 用户信息
	 */
	@TableField(exist = false)
	private WxUser userInfo;
	/**
	 * 物流单号
	 */
	@TableField(exist = false)
	private String logisticsNo;

	/** 订单出行人信息 */
	@TableField(exist = false)
	@Excel(name = "订单出行人信息")
	private List<UserCertInfo> orderTravelersList;


	/**
	 * 订单联系人名称
	 */
	@ApiModelProperty(value = "订单联系人名称")
	@Excel(name = "订单联系人名称")
	private String orderContactName;

	/**
	 * 订单联系人电话
	 */
	@ApiModelProperty(value = "订单联系人电话")
	@Excel(name = "订单联系人电话")
	private String orderContactPhone;

	/**
	 * 旅行社id
	 */
	@ApiModelProperty(value = "旅行社id")
	private String travelId;

	/**
	 * 评论状态 0 无需评论，1待评论，2已经评论
	 */
	@ApiModelProperty(value = "评论状态")
	private String evaluateFlag;
	/**
	 * 规格ID
	 */
	@TableField(exist = false)
	private Integer specs;

	/**
	 * 规格ID
	 */
	@TableField(exist = false)
	private String specsName;

	/**
	 * 出行人身份证
	 */
	@TableField(exist = false)
	private String certId;

	/**
	 * 出行人姓名
	 */
	@TableField(exist = false)
	private String certName;

	/**
	 * 出行人手机号
	 */
	@TableField(exist = false)
	private String certPhone;

	/**
	 * 成人总人数
	 */
	@TableField(exist = false)
	private Integer adultQuantity;

	/**
	 * 儿童总人数
	 */
	@TableField(exist = false)
	private Integer childrenQuantity;

	/**
	 * 状态列表（用于多状态查询，如："0,1,2"）
	 */
	@TableField(exist = false)
	private List<String> statusList;

	public String getStatusDesc() {
		if (CommonConstants.NO.equals(this.isPay) && this.status == null) {
			return "待付款";
		}
		if (this.status == null) {
			return null;
		}
		return OrderInfoEnum.valueOf(OrderInfoEnum.STATUS_PREFIX + "_" + this.status).getDesc();
	}
}
