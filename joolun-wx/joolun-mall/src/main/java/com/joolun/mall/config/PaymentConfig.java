package com.joolun.mall.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付配置类
 * 支持模拟支付和真实支付的环境配置
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@Component
@ConfigurationProperties(prefix = "payment")
public class PaymentConfig {

    /**
     * 模拟支付配置
     */
    private Mock mock = new Mock();

    /**
     * 支付超时配置
     */
    private Timeout timeout = new Timeout();

    @Data
    public static class Mock {
        /**
         * 是否启用模拟支付
         */
        private boolean enabled = false;

        /**
         * 模拟支付成功率（0-100）
         */
        private int successRate = 100;

        /**
         * 模拟支付延迟时间（秒）
         */
        private int delaySeconds = 2;

        /**
         * 模拟支付失败原因
         */
        private String failureReason = "模拟支付失败";

        /**
         * 是否模拟回调延迟
         */
        private boolean callbackDelay = true;

        /**
         * 回调延迟时间（秒）
         */
        private int callbackDelaySeconds = 3;
    }

    @Data
    public static class Timeout {
        /**
         * 支付超时时间（分钟）
         */
        private int paymentTimeoutMinutes = 30;

        /**
         * 自动取消超时订单
         */
        private boolean autoCancelExpiredOrders = true;

        /**
         * 超时检查间隔（分钟）
         */
        private int checkIntervalMinutes = 5;
    }

    /**
     * 获取模拟支付是否启用
     * @return true if mock payment is enabled
     */
    public boolean isMockEnabled() {
        return mock.isEnabled();
    }
} 