package com.joolun.mall.dto;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.joolun.common.annotation.Excel;
import com.joolun.mall.entity.ShoppingCart;
import com.joolun.mall.entity.UserCertInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 下单参数
 *
 * <AUTHOR>
 * @date 2019-08-13 10:18:34
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ApiModel(description = "下单参数")
public class PlaceOrderDTO extends Model<ShoppingCart> {
	private static final long serialVersionUID = 1L;

	/**
	 * 支付方式1、货到付款；2、在线支付
	 */
	@ApiModelProperty(value = "支付方式1、货到付款；2、在线支付")
	private String paymentWay;

	/**
	 * 付款方式1、微信支付
	 */
	@ApiModelProperty(value = "付款方式")
	private String paymentType;

	/**
	 * 买家留言
	 */
	@ApiModelProperty(value = "买家留言")
	private String userMessage;


	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private String userId;

	/**
	 * 订单类型（0、普通订单；）
	 */
	@ApiModelProperty(value = "订单类型")
	private String orderType;

	/**
	 * 成人数量
	 */
	@ApiModelProperty(value = "成人数量")
	private Integer adultQuantity;

	/**
	 * 老年人数量
	 */
	@ApiModelProperty(value = "老年人数量")
	private Integer elderlyQuantity;

	/** 大童数量 */
	@ApiModelProperty(value = "大童数量")
	private Integer olderChildQuantity;

	/** 小童数量 */
	@ApiModelProperty(value = "小童数量")
	private Integer youngChildQuantity;

	/**
	 * 具体日期id-价格日历ID
	 */
	@ApiModelProperty(value = "具体日期id")
	private String detailId;

	/**
	 * skuId
	 */
	@ApiModelProperty(value = "skuId")
	private String skuId;

	@ApiModelProperty(value = "商品-线路")
	private List<PlaceOrderGoodsDTO> skus;

	/**
	 * 支付金额
	 */
	@ApiModelProperty(value = "支付金额")
	private BigDecimal paymentPrice;

	/***
	 * 出行人信息
	 */
	@ApiModelProperty(value = "出行人信息")
	private List<UserCertInfo> userCertInfos;


	/**
	 * 订单联系人名称
	 */
	@ApiModelProperty(value = "订单联系人名称")
	private String orderContactName;

	/**
	 * 订单联系人电话
	 */
	@ApiModelProperty(value = "订单联系人电话")
	private String orderContactPhone;

	/**
	 * 补充房间数量
	 */
	@ApiModelProperty(value = "补充房间数量")
	private BigDecimal replenishRoomNum;

}
