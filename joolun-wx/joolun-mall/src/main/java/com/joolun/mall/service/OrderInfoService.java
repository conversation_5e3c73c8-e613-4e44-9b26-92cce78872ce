package com.joolun.mall.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.joolun.common.exception.CustomException;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.constant.MallConstants;
import com.joolun.mall.dto.*;
import com.joolun.mall.entity.OrderInfo;
import com.joolun.mall.entity.OrderItem;
import com.joolun.mall.entity.TravelOrderStat;
import com.joolun.mall.entity.UserCertInfo;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
public interface OrderInfoService extends IService<OrderInfo> {

	IPage<OrderInfo> page1(IPage<OrderInfo> page, Wrapper<OrderInfo> queryWrapper);

	/**
	 * 下单
	 * @param placeOrderDTO
	 */
	OrderInfo orderSub(PlaceOrderDTO placeOrderDTO);

	IPage<OrderInfo> page2(IPage<OrderInfo> page, OrderInfo orderInfo);

	IPage<OrderInfoAppDTO> selectByPageForApp(IPage<OrderInfo> page, OrderInfo orderInfo);

	OrderInfo getById2(Serializable id);

 	OrderItemAppDTO getOrderItemAppByOrderId(Serializable id);

	@Transactional(rollbackFor = Exception.class)
	abstract void orderCancel(OrderInfo orderInfo);
	/**
	 * 订单收货
	 * @param orderInfo
	 */
	void orderReceive(OrderInfo orderInfo);

	/**
	 * 处理订单回调
	 * @param orderInfo
	 */
	void notifyOrder(OrderInfo orderInfo);

	/**
	 * 处理0元订单支付
	 * @param orderInfo
	 */
	@Transactional(rollbackFor = Exception.class)
	void processZeroPaymentOrder(OrderInfo orderInfo);

	/**
	 * 发起退款
	 * @param orderItem
	 */
	void saveRefunds(OrderItem orderItem);

	/**
	 * 统一退款入口 - 自动判断模拟或真实退款
	 * @param orderItem
	 */
	void processOrderRefunds(OrderItem orderItem);

	/**
	 * 操作退款-仅真实支付
	 * @param orderItem
	 */
	void doOrderRefunds(OrderItem orderItem);

  /**
   * 操作退款-模拟
   *
   * @param orderItem
   */
  void doOrderRefundsForMock(OrderItem orderItem);

	/**
	 * 退款回调
	 * @param notifyResult
	 */
	void notifyRefunds(WxPayRefundNotifyResult notifyResult);


	public OrderInfo orderUserLv(PlaceUserLvDTO placeUserLvDTO);

	public OrderInfo selectById2(String id);

	IPage<TravelOrderStat> selectOrderStatPage(IPage<TravelOrderStat> page, OrderInfo orderInfo);

	List<OrderInfoDTO> getOrderInfoList(OrderInfo orderInfo);

	/**
	 * 用户取消退款申请
	 * @param orderItemId 订单项ID
	 */
	void cancelRefundApplication(String orderItemId);

  /**
   * 订单项状态变更后，同步更新订单状态
   *
   * @param orderId
   * @param status
   */
  void syncOrderStatusWithOrderItem(String orderId, String status);
}
