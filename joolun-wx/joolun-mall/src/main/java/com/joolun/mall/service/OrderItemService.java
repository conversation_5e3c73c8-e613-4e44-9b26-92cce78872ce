package com.joolun.mall.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.mall.dto.OrderItemAppDTO;
import com.joolun.mall.entity.OrderItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 商城订单详情
 *
 * <AUTHOR>
 * @date 2019-09-10 15:31:40
 */
public interface OrderItemService extends IService<OrderItem> {

  List<OrderItem> listByOrderIds(List<String> orderIds);

  Map<String, List<OrderItemAppDTO>> listByOrderIdsReturnMap(List<String> orderIds);

  OrderItem selectOneByOrderId(String orderId);
}
