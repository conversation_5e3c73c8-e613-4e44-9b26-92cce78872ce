package com.joolun.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.mall.dto.OrderItemAppDTO;
import com.joolun.mall.entity.OrderItem;
import com.joolun.mall.mapper.OrderItemMapper;
import com.joolun.mall.service.OrderItemService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商城订单详情
 *
 * <AUTHOR>
 * @date 2019-09-10 15:31:40
 */
@Service
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem>
    implements OrderItemService {

  @Override
  public List<OrderItem> listByOrderIds(List<String> orderIds) {
    LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
    wrapper
        .in(ObjectUtil.isNotNull(orderIds), OrderItem::getOrderId, orderIds)
        .eq(OrderItem::getDelFlag, Boolean.FALSE);
    List<OrderItem> list = this.list(wrapper);
    if (ObjectUtil.isEmpty(list)) {
      return new ArrayList<>();
    }
    return list;
  }

  @Override
  public Map<String, List<OrderItemAppDTO>> listByOrderIdsReturnMap(List<String> orderIds) {
    LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
    wrapper
        .in(ObjectUtil.isNotNull(orderIds), OrderItem::getOrderId, orderIds)
        .eq(OrderItem::getDelFlag, Boolean.FALSE);
    List<OrderItem> list = this.list(wrapper);
    if (ObjectUtil.isEmpty(list)) {
      return Collections.emptyMap();
    }
    List<OrderItemAppDTO> orderItemAppDTOS = BeanUtil.copyToList(list, OrderItemAppDTO.class);
    return orderItemAppDTOS.stream().collect(Collectors.groupingBy(OrderItemAppDTO::getOrderId));
  }

  @Override
  public OrderItem selectOneByOrderId(String orderId) {
    LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(OrderItem::getOrderId, orderId).eq(OrderItem::getDelFlag, Boolean.FALSE);
    return this.getOne(wrapper);
  }
}
