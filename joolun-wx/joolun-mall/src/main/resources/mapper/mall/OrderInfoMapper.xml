<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，项目使用请保留此说明
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.mall.mapper.OrderInfoMapper">

	<resultMap id="orderInfoMap" type="com.joolun.mall.entity.OrderInfo">
		<id property="id" column="id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="userId" column="user_id"/>
		<result property="orderNo" column="order_no"/>
		<result property="salesPrice" column="sales_price"/>
		<result property="freightPrice" column="freight_price"/>
		<result property="paymentWay" column="payment_way"/>
		<result property="paymentPrice" column="payment_price"/>
		<result property="paymentTime" column="payment_time"/>
		<result property="deliveryTime" column="delivery_time"/>
		<result property="receiverTime" column="receiver_time"/>
		<result property="closingTime" column="closing_time"/>
		<result property="transactionId" column="transaction_id"/>
		<result property="logisticsId" column="logistics_id"/>
		<result property="remark" column="remark"/>
		<result property="isPay" column="is_pay"/>
		<result property="name" column="name"/>
		<result property="status" column="status"/>
		<result property="userMessage" column="user_message"/>
		<result property="orderContactName" column="order_contact_name"/>
		<result property="orderContactPhone" column="order_contact_phone"/>
		<result property="travelId" column="travel_id"/>
		<result property="evaluateFlag" column="evaluate_Flag"/>
	</resultMap>

	<resultMap id="orderInfoMap2" extends="orderInfoMap" type="com.joolun.mall.entity.OrderInfo">
		<collection property="listOrderItem" ofType="com.joolun.mall.entity.OrderItem"
					select="com.joolun.mall.mapper.OrderItemMapper.selectList2"
					column="{orderId=id}">
		</collection>
		<collection property="orderLogistics" ofType="com.joolun.mall.entity.OrderLogistics"
					select="com.joolun.mall.mapper.OrderLogisticsMapper.selectById"
					column="{id=logistics_id}">
		</collection>
		<collection property="orderTravelersList" ofType="com.joolun.mall.entity.UserCertInfo"
					select="com.joolun.mall.mapper.UserCertInfoMapper.selectOrderTravelersByOrderId"
					column="{orderId=id}">
		</collection>
<!--		<collection property="orderTravelersList" notNullColumn="id" javaType="java.util.List" resultMap="OrderTravelersResult" />-->
	</resultMap>

	<resultMap type="OrderTravelers" id="OrderTravelersResult">
		<result property="id"    column="bid"    />
		<result property="orderId"    column="order_id"    />
		<result property="userId"    column="user_id"    />
		<result property="createTime"    column="create_time"    />
	</resultMap>

	<resultMap id="TravelOrderStatMap" type="com.joolun.mall.entity.TravelOrderStat">
		<result property="travelId"    column="travel_id"    />
		<result property="travelName"    column="travel_Name"    />
		<result property="dsyje"    column="dsyje"    />
		<result property="ywcje"    column="ywcje"    />
		<result property="yqxje"    column="jyqxe"    />
		<result property="dsys"    column="dsys"    />
		<result property="ywcs"    column="ywcs"    />
		<result property="yqxs"    column="yqxs"    />
		<result property="ytks"    column="ytks"    />
		<result property="yzfzs"    column="yzfzs"    />
		<result property="ytkzs"    column="ytkzs"    />
		<result property="ddzs"    column="ddzs"    />
	</resultMap>


	<resultMap id="orderInfoDTOMap" type="com.joolun.mall.dto.OrderInfoDTO">
		<id property="id" column="id"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="userId" column="user_id"/>
		<result property="orderNo" column="order_no"/>
		<result property="salesPrice" column="sales_price"/>
		<result property="freightPrice" column="freight_price"/>
		<result property="paymentPrice" column="payment_price"/>
		<result property="paymentTime" column="payment_time"/>
		<result property="deliveryTime" column="delivery_time"/>
		<result property="receiverTime" column="receiver_time"/>
		<result property="closingTime" column="closing_time"/>
		<result property="transactionId" column="transaction_id"/>
		<result property="isPay" column="is_pay"/>
		<result property="name" column="name"/>
		<result property="status" column="status"/>
		<result property="userMessage" column="user_message"/>
		<result property="orderContactName" column="order_contact_name"/>
		<result property="orderContactPhone" column="order_contact_phone"/>
		<result property="travelId" column="travel_id"/>
		<result property="username"    column="username"    />
		<result property="certType"    column="cert_type"    />
		<result property="certId"    column="cert_id"    />
		<result property="validStartData"    column="valid_start_data"    />
		<result property="validEndData"    column="valid_end_data"    />
		<result property="userType"    column="user_type"    />
		<result property="sex"    column="sex"    />
		<result property="specsName"    column="specs_name"    />
	</resultMap>


	<sql id="orderInfoSql">
		order_info.`id`,
		order_info.`del_flag`,
		order_info.`create_time`,
		order_info.`update_time`,
		order_info.`user_id`,
		order_info.`order_no`,
		order_info.`sales_price`,
		order_info.`freight_price`,
		order_info.`payment_way`,
		order_info.`payment_price`,
		order_info.`payment_time`,
		order_info.`delivery_time`,
		order_info.`receiver_time`,
		order_info.`closing_time`,
		order_info.`is_pay`,
		order_info.`name`,
		order_info.`status`,
		order_info.`user_message`,
		order_info.`transaction_id`,
		order_info.`logistics_id`,
		order_info.`remark`,
		order_info.`order_contact_name`,
		order_info.`order_contact_phone`,
		order_info.travel_id,
		order_info.evaluate_Flag
	</sql>

	<select id="selectPage1" resultMap="orderInfoMap2">
		SELECT DISTINCT
		<include refid="orderInfoSql"/>
		FROM order_info order_info
		left join order_travelers b on order_info.id=b.order_id
		left join user_cert_info c on b.user_id=c.id
		<where>
			<if test="query.userId != null">
				AND order_info.`user_id` = #{query.userId}
			</if>
			<!-- 多状态查询：优先使用statusList -->
			<if test="query.statusList != null and query.statusList.size() > 0">
				AND (
				<foreach collection="query.statusList" item="statusItem" separator=" OR ">
					<choose>
						<when test="statusItem == '0'">
							(order_info.`is_pay` = '0' AND order_info.`status` is null)
						</when>
						<when test="statusItem == '4'">
							order_info.`status` = '3'
						</when>
						<otherwise>
							order_info.`status` = #{statusItem}
						</otherwise>
					</choose>
				</foreach>
				)
			</if>
			<!-- 单状态查询：仅在statusList为空时使用 -->
			<if test="(query.statusList == null or query.statusList.size() == 0) and query.status != null and query.status != ''">
				<if test="query.status == '0'.toString()">
					AND order_info.`is_pay` = '0'
					AND order_info.`status` is null
				</if>
				<if test="query.status == '4'.toString()">
					AND order_info.`status` = '3'
				</if>
				<if test="query.status != '0'.toString() and query.status != '4'.toString()">
					AND order_info.`status` = #{query.status}
				</if>
			</if>
			<if test="query.isPay != null">
				AND order_info.`is_pay` = #{query.isPay}
			</if>
			<if test="query.orderNo != null">
				AND order_info.`order_no` = #{query.orderNo}
			</if>
			<if test="query.beginTime != null">
				AND order_info.`create_time` >= #{query.beginTime}
			</if>
			<if test="query.endTime != null">
				AND #{query.endTime} >= order_info.`create_time`
			</if>
			<if test="query.travelId != null">
				AND order_info.`travel_Id` = #{query.travelId}
			</if>
		</where>
		ORDER BY order_info.create_time DESC
	</select>

	<select id="selectPage2" resultMap="orderInfoMap2">
		SELECT
		<include refid="orderInfoSql"/>
		FROM order_info order_info
		<where>
			<if test="query.userId != null">
				AND order_info.`user_id` = #{query.userId}
			</if>
			<if test="query.status != null">
				<if test="query.status == '0'.toString()">
					AND order_info.`is_pay` = '0'
					AND order_info.`status` is null
				</if>
				<if test="query.status == '4'.toString()">
					AND order_info.`status` = '3'
				</if>
				<if test="query.status != '0'.toString() and query.status != '4'.toString()">
					AND order_info.`status` = #{query.status}
				</if>
			</if>
			<if test="query.orderNo != null">
				AND order_info.`order_no` = #{query.orderNo}
			</if>
			<if test="query.travelId != null">
				AND order_info.`travel_Id` = #{query.travelId}
			</if>
			<if test="query.name != null">
				AND order_info.`name` like concat('%', #{query.name}, '%')
			</if>
			<if test="query.orderContactName != null">
				AND order_info.`order_contact_name` like concat('%', #{query.orderContactName}, '%')
			</if>
			<if test="query.orderContactPhone != null">
				AND order_info.`order_contact_phone` like concat('%', #{query.orderContactPhone}, '%')
			</if>
			<if test="query.certId != null">
				AND c.`cert_id` like concat('%', #{query.certId}, '%')
			</if>
			<if test="query.certName != null">
				AND c.`name` like concat('%', #{query.certName}, '%')
			</if>
			<if test="query.certPhone != null">
				AND c.`phone` like concat('%', #{query.certPhone}, '%')
			</if>
		</where>
		group by order_info.order_no
	</select>

	<select id="selectByPage" resultMap="orderInfoMap">
		SELECT
		<include refid="orderInfoSql"/>
		FROM order_info order_info
		<where>
			<if test="query.userId != null">
				AND order_info.`user_id` = #{query.userId}
			</if>
			<!-- 多状态查询：优先使用statusList -->
			<if test="query.statusList != null and query.statusList.size() > 0">
				AND (
					<foreach collection="query.statusList" item="statusItem" separator=" OR ">
						<choose>
							<when test="statusItem == '0'">
								(order_info.`is_pay` = '0' AND order_info.`status` = '0')
							</when>
							<when test="statusItem == '4'">
								order_info.`status` = '3'
							</when>
							<otherwise>
								order_info.`status` = #{statusItem}
							</otherwise>
						</choose>
					</foreach>
				)
			</if>
			<!-- 单状态查询：仅在statusList为空时使用 -->
			<if test="(query.statusList == null or query.statusList.size() == 0) and query.status != null and query.status != ''">
				<if test="query.status == '0'.toString()">
					AND order_info.`is_pay` = '0'
					AND order_info.`status` = '0'
				</if>
				<if test="query.status == '4'.toString()">
					AND order_info.`status` = '3'
				</if>
				<if test="query.status != '0'.toString() and query.status != '4'.toString()">
					AND order_info.`status` = #{query.status}
				</if>
			</if>
			<if test="query.orderNo != null">
				AND order_info.`order_no` = #{query.orderNo}
			</if>
			<if test="query.travelId != null">
				AND order_info.`travel_Id` = #{query.travelId}
			</if>
			<if test="query.name != null">
				AND order_info.`name` like concat('%', #{query.name}, '%')
			</if>
			<if test="query.orderContactName != null">
				AND order_info.`order_contact_name` like concat('%', #{query.orderContactName}, '%')
			</if>
			<if test="query.orderContactPhone != null">
				AND order_info.`order_contact_phone` like concat('%', #{query.orderContactPhone}, '%')
			</if>
			<if test="query.certId != null">
				AND c.`cert_id` like concat('%', #{query.certId}, '%')
			</if>
			<if test="query.certName != null">
				AND c.`name` like concat('%', #{query.certName}, '%')
			</if>
			<if test="query.certPhone != null">
				AND c.`phone` like concat('%', #{query.certPhone}, '%')
			</if>
		</where>
		group by order_info.order_no
		order by order_info.create_time desc
	</select>

	<select id="selectById2" resultMap="orderInfoMap2">
		SELECT
		<include refid="orderInfoSql"/>
		FROM order_info order_info
		WHERE order_info.`id` = #{id}
	</select>

	<sql id="selectOrderTravelersVo">
		select id as bid, order_id, user_id, create_time from order_travelers
	</sql>

	<select id="selectOrderTravelersList" parameterType="OrderTravelers" resultMap="OrderTravelersResult">
		<include refid="selectOrderTravelersVo"/>
		<where>
			<if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
			<if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
		</where>
	</select>

	<select id="selectOrderTravelersById" parameterType="String" resultMap="OrderTravelersResult">
		<include refid="selectOrderTravelersVo"/>
		where id = #{id}
	</select>


	<insert id="insertOrderTravelers" parameterType="OrderTravelers">
		insert into order_travelers
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="orderId != null">order_id,</if>
			<if test="userId != null">user_id,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="orderId != null">#{orderId},</if>
			<if test="userId != null">#{userId},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>

	<update id="updateOrderTravelers" parameterType="OrderTravelers">
		update order_travelers
		<trim prefix="SET" suffixOverrides=",">
			<if test="orderId != null">order_id = #{orderId},</if>
			<if test="userId != null">user_id = #{userId},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteOrderTravelersById" parameterType="String">
		delete from order_travelers where id = #{id}
	</delete>

	<delete id="deleteOrderTravelersByIds" parameterType="java.lang.String">
		delete from order_travelers where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<delete id="deleteOrderTravelersByOrderIds" parameterType="java.lang.String">
		delete from order_travelers where order_id in
		<foreach item="orderId" collection="array" open="(" separator="," close=")">
			#{orderId}
		</foreach>
	</delete>

	<delete id="deleteOrderTravelersByOrderId" parameterType="java.lang.Long">
		delete from order_travelers where order_id = #{orderId}
	</delete>

	<insert id="batchOrderTravelers" >
		insert into order_travelers( id, order_id, user_id, create_time) values
		<foreach item="item" index="index" collection="list" separator=",">
			( #{item.id}, #{item.orderId}, #{item.userId}, #{item.createTime})
		</foreach>
	</insert>


	<select id="selectOrderStatPage" resultMap="TravelOrderStatMap">
		SELECT
			a.travel_id,
			b.`name`,
			SUM(CASE WHEN a.status='0' THEN a.payment_price  ELSE 0 END) AS dfkje,	-- 待付款
			SUM(CASE WHEN a.status='2' THEN a.payment_price  ELSE 0 END) AS dsyje,	-- 待使用
			SUM(CASE WHEN a.status='3' THEN a.payment_price  ELSE 0 END) AS ywcje,  -- 已完成
			SUM(CASE WHEN a.status='4' THEN a.payment_price  ELSE 0 END) AS tkzje,	-- 退款中
			SUM(CASE WHEN a.status='5' THEN a.payment_price  ELSE 0 END) AS yqxje,	-- 已取消
			SUM(CASE WHEN a.status='6' THEN a.payment_price  ELSE 0 END) AS ytkje,	-- 已退款
			SUM(CASE WHEN a.status='0' THEN 1  ELSE 0 END) AS dfks, -- 待付款
			SUM(CASE WHEN a.status='2' THEN 1  ELSE 0 END) AS dsys, -- 待使用
			SUM(CASE WHEN a.status='3' THEN 1  ELSE 0 END) AS ywcs, -- 已完成
			SUM(CASE WHEN a.status='4' THEN 1  ELSE 0 END) AS tkzs, -- 退款中
			SUM(CASE WHEN a.status='5' THEN 1  ELSE 0 END) AS yqxs, -- 已取消
			SUM(CASE WHEN a.status='6' THEN 1  ELSE 0 END) AS ytks,  -- 已退款
			SUM(CASE WHEN a.is_pay='1' THEN a.payment_price  ELSE 0 END) AS yzf,	-- 已支付金额
			count(1) ddzs	-- 订单总数
		 FROM order_info a
		 left join travel_agency b on a.travel_id=b.id
		<where>
			<if test="query.travelId != null">
				AND a.`travel_Id` = #{query.travelId}
			</if>
		</where>
	</select>

	<select id="getOrderInfoList" resultMap="orderInfoDTOMap">
		SELECT order_info.`id`,
		order_info.`del_flag`,
		order_info.`create_time`,
		order_info.`update_time`,
		order_info.`user_id`,
		order_info.`order_no`,
		order_info.`sales_price`,
		order_info.`freight_price`,
		order_info.`payment_way`,
		order_info.`payment_price`,
		order_info.`payment_time`,
		order_info.`delivery_time`,
		order_info.`receiver_time`,
		order_info.`closing_time`,
		order_info.`is_pay`,
		order_info.`name`,
		order_info.`status`,
		order_info.`user_message`,
		order_info.`transaction_id`,
		order_info.`logistics_id`,
		order_info.`remark`,
		order_info.`order_contact_name`,
		order_info.`order_contact_phone`,
		order_info.travel_id,
		order_info.evaluate_Flag ,
		user_cert_info.name as username,
		user_cert_info.cert_type,
		user_cert_info.cert_id,
		user_cert_info.valid_start_data,
		user_cert_info.valid_end_data,
		user_cert_info.user_type,
		user_cert_info.sex,
		user_cert_info.phone,
		order_item.adult_quantity,
		order_item.older_child_quantity,
		order_item.young_child_quantity,
		order_item.elderly_quantity,
		order_item.seven_day_refund,
		order_item.fifteen_day_refun,
		order_item.forty_eight_hours_refun,
		order_item.Twenty_four_hours_refun,
		order_item.departure_date,
		order_item.refund_apply_time,
		order_item.specs,
		specs.name as specs_name
		FROM order_info order_info
		left join order_travelers order_travelers on order_info.id = order_travelers.order_id
		left join user_cert_info user_cert_info on user_cert_info.id = order_travelers.user_id
		left join order_item order_item on order_item.order_id = order_info.id
		left join goods_specs specs on order_item.specs = specs.id
		<where>
			<if test="query.userId != null">
				AND order_info.`user_id` = #{query.userId}
			</if>
			<if test="query.status != null">
				<if test="query.status == '0'.toString()">
					AND order_info.`is_pay` = '0'
					AND order_info.`status` is null
				</if>
				<if test="query.status == '4'.toString()">
					AND order_info.`status` = '3'
				</if>
				<if test="query.status != '0'.toString() and query.status != '4'.toString()">
					AND order_info.`status` = #{query.status}
				</if>
			</if>
			<if test="query.orderNo != null">
				AND order_info.`order_no` like concat('%', #{query.orderNo}, '%')
			</if>
			<if test="query.isPay != null">
				AND order_info.`is_pay` = #{query.isPay}
			</if>
			<if test="query.travelId != null">
				AND order_info.`travel_Id` = #{query.travelId}
			</if>
			<if test="query.specsName != null">
				AND specs.name  like  concat('%', #{specsName}, '%')
			</if>
			<if test="query.beginTime != null">
				AND order_info.`create_time` >= #{query.beginTime}
			</if>
			<if test="query.endTime != null">
				AND #{query.endTime} >= order_info.`create_time`
			</if>
			<if test="query.name != null">
				AND order_info.`name` like concat('%', #{query.name}, '%')
			</if>
			<if test="query.orderContactName != null">
				AND order_info.`order_contact_name` like concat('%', #{query.orderContactName}, '%')
			</if>
			<if test="query.orderContactPhone != null">
				AND order_info.`order_contact_phone` like concat('%', #{query.orderContactPhone}, '%')
			</if>
			<if test="query.certId != null">
				AND user_cert_info.`cert_id` like concat('%', #{query.certId}, '%')
			</if>
			<if test="query.certName != null">
				AND user_cert_info.`name` like concat('%', #{query.certName}, '%')
			</if>
			<if test="query.certPhone != null">
				AND user_cert_info.`phone` like concat('%', #{query.certPhone}, '%')
			</if>
		</where>
		order by order_info.create_time desc,order_info.order_no
	</select>


</mapper>
