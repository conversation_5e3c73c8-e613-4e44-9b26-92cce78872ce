<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joolun.mall.mapper.TyUserCollectMapper">
    
    <resultMap type="TyUserCollect" id="TyUserCollectResult">
        <result property="id"    column="id"    />
        <result property="goodId"    column="good_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="deleteFlag"    column="delete_flag"    />
    </resultMap>

    <sql id="selectTyUserCollectVo">
        select id, good_id, user_id, create_time, create_by, delete_flag from ty_user_collect
    </sql>

    <select id="selectTyUserCollectList" parameterType="TyUserCollect" resultMap="TyUserCollectResult">
        <include refid="selectTyUserCollectVo"/>
        <where>
            delete_flag = false
            <if test="goodId != null and goodId != ''">
                and good_id = #{goodId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
        </where>
    </select>
    
    <select id="selectTyUserCollectById" parameterType="String" resultMap="TyUserCollectResult">
        <include refid="selectTyUserCollectVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTyUserCollect" parameterType="TyUserCollect">
        insert into ty_user_collect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="goodId != null">good_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="deleteFlag != null">delete_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="goodId != null">#{goodId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
         </trim>
    </insert>

    <update id="updateTyUserCollect" parameterType="TyUserCollect">
        update ty_user_collect
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodId != null">good_id = #{goodId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTyUserCollectById" parameterType="String">
        delete from ty_user_collect where id = #{id}
    </delete>

    <delete id="deleteTyUserCollectByIds" parameterType="java.lang.String">
        delete from ty_user_collect where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>