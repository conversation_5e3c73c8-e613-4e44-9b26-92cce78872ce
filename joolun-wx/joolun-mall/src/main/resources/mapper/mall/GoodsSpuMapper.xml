<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，项目使用请保留此说明
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.mall.mapper.GoodsSpuMapper">
	<resultMap id="goodsSpuMap" type="com.joolun.mall.entity.GoodsSpu">
		<id property="id" column="id"/>
		<result property="spuCode" column="spu_code"/>
		<result property="name" column="name"/>
		<result property="sellPoint" column="sell_point"/>
		<result property="description" column="description"/>
		<result property="categoryFirst" column="category_first"/>
		<result property="categorySecond" column="category_second"/>
<!--		<result property="picUrls" column="pic_urls"/>-->
		<result property="shelf" column="shelf"/>
		<result property="sort" column="sort"/>
		<result property="salesPrice" column="sales_price"/>
		<result property="marketPrice" column="market_price"/>
		<result property="costPrice" column="cost_price"/>
		<result property="stock" column="stock"/>
		<result property="saleNum" column="sale_num"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="delFlag" column="del_flag"/>
		<result property="commission" column="version"/>
		<result property="costIntro" column="cost_intro"/>
		<result property="startingPoint" column="starting_Point"/>
		<result property="dest" column="dest"/>
		<result property="rebate" column="rebate"/>
		<result property="childDefinition" column="child_definition"/>
		<result property="travelId"    column="travel_id"    />
		<result property="olderChildFlag"    column="older_child_flag"    />
		<result property="youngChildFlag"    column="young_child_flag"    />
		<result property="elderlyFlag"    column="elderly_flag"    />
		<result property="adultFlag"    column="adult_flag"    />
		<result property="childFlag"    column="child_flag"    />
		<result property="boardingPoint"    column="boarding_point"    />
		<result property="roomAllowance"    column="room_allowance"    />
		<result property="dateInfo"    column="date_info"    />
		<result property="stayInfo"    column="stay_info"    />
		<result property="mealsInfo"    column="meals_info"    />
		<result property="level"    column="level"    />
		<result property="sevenDayRefund"    column="seven_day_refund"    />
		<result property="fifteenDayRefun"    column="fifteen_day_refun"    />
		<result property="fortyEightHoursRefun"    column="forty_eight_hours_refun"    />
		<result property="twentyFourHoursRefun"    column="Twenty_four_hours_refun"    />
		<result property="startProvince"    column="start_province"    />
		<result property="startCity"    column="start_city"    />
		<result property="startRegion"    column="start_region"    />
		<result property="destProvince"    column="dest_province"    />
		<result property="destCity"    column="dest_city"    />
		<result property="destRegion"    column="dest_region"    />
		<result property="startProvinceName"    column="start_provinceName"    />
		<result property="startRegionName"    column="start_regionName"    />
		<result property="startCityName"    column="start_cityName"    />
		<result property="phone"    column="phone"    />
		<result property="isShow"    column="is_show"    />
		<result property="rqCode"    column="rq_code"    />
		<result property="remind"    column="remind"    />
		<result property="mustAdult"    column="must_adult"    />
		<result property="specificationType"    column="specification_type"    />
		<result property="spotName"    column="spot_name"    />

<!--		<collection property="detailList" javaType="java.util.List" resultMap="GoodsSpuDetailResult" />-->
<!--		<collection property="goodsSpecificationValue" javaType="java.util.List" resultMap="GoodsSpecsResult" />-->
	</resultMap>

	<resultMap id="GoodsSpuGoodsSpuDetailResult" type="GoodsSpu" extends="goodsSpuMap">
<!--		<collection property="goodsSpuDetailList" notNullColumn="b_id" javaType="java.util.List" resultMap="GoodsSpuDetailResult" />-->
		<collection property="pickupPoints" notNullColumn="points_id" javaType="java.util.List" resultMap="PickupPointsResult" />
	</resultMap>

	<resultMap type="GoodsSpuDetail" id="GoodsSpuDetailResult">
		<result property="id"    column="b_id"    />
		<result property="goodsId"    column="b_goods_id"    />
		<result property="price"    column="b_price"    />
		<result property="adultPrice"    column="b_adult_price"    />
		<result property="childPrice"    column="b_child_price"    />
		<result property="elderlyPrice"    column="b_elderly_price"    />
		<result property="date"    column="b_date"    />
		<result property="commission"    column="b_commission"    />
		<result property="createBy"    column="b_create_by"    />
		<result property="createTime"    column="b_create_time"    />
		<result property="updateBy"    column="b_update_by"    />
		<result property="updateTime"    column="b_update_time"    />
		<result property="stock"    column="b_stock"    />
		<result property="saleNum"    column="b_sale_Num"    />
		<result property="endTime"    column="b_endTime"    />
	</resultMap>

	<resultMap type="TravelAgencyPickupPoints" id="PickupPointsResult">
		<result property="id"    column="points_id"    />
		<result property="address"    column="address"    />
		<result property="delFlag"    column="del_flag"    />
		<result property="createBy"    column="create_by"    />
		<result property="createTime"    column="create_time"    />
		<result property="updateTime"    column="update_time"    />
		<result property="updateBy"    column="update_by"    />
		<result property="travelId"    column="travel_id"    />
		<result property="travelName"    column="travel_Name"    />
	</resultMap>

	<resultMap type="com.joolun.mall.entity.GoodsSpecificationValue" id="GoodsSpecsResult">
		<result property="id"    column="s_id"    />
		<result property="name"    column="s_name"    />
<!--		<result property="attr_values"    column="s_values"    />-->
		<result property="goodsId"    column="s_goods_id"    />
		<result property="createBy"    column="s_create_by"    />
		<result property="createTime"    column="s_create_time"    />
		<result property="updateBy"    column="s_update_by"    />
		<result property="updateTime"    column="s_update_time"    />
	</resultMap>


	<resultMap id="goodsSpuMap1" extends="goodsSpuMap" type="com.joolun.mall.entity.GoodsSpu">

	</resultMap>

	<resultMap id="goodsSpuMap2" extends="goodsSpuMap" type="com.joolun.mall.entity.GoodsSpu">

	</resultMap>

	<resultMap id="goodsSpuMap3" extends="goodsSpuMap" type="com.joolun.mall.entity.GoodsSpu">

	</resultMap>

	<resultMap id="goodsSpuMap4" extends="goodsSpuMap" type="com.joolun.mall.entity.GoodsSpu">

	</resultMap>

	<sql id="goodsSpuSql">
		goods_spu.`id`,
		goods_spu.`spu_code`,
		goods_spu.`name`,
		goods_spu.`sell_point`,
		goods_spu.`description`,
		goods_spu.`category_first`,
		goods_spu.`category_second`,
		goods_spu.`pic_urls`,
		goods_spu.`shelf`,
		goods_spu.`sort`,
		goods_spu.`sales_price`,
		goods_spu.`market_price`,
		goods_spu.`cost_price`,
		goods_spu.`stock`,
		goods_spu.`sale_num`,
		goods_spu.`create_time`,
		goods_spu.`update_time`,
		goods_spu.`del_flag`,
		goods_spu.`version`,
		goods_spu.`cost_intro`,
		goods_spu.`starting_Point`,
		goods_spu.`dest`,
		goods_spu.`rebate`,
		goods_spu.`child_definition`,
		goods_spu.travel_id,
		goods_spu.older_child_flag,
		goods_spu.young_child_flag,
		goods_spu.elderly_flag,
		goods_spu.adult_flag,
		goods_spu.child_flag,
		goods_spu.boarding_point,
		goods_spu.room_allowance,
		goods_spu.date_info, goods_spu.stay_info, goods_spu.meals_info, goods_spu.level,
		goods_spu.seven_day_refund, goods_spu.fifteen_day_refun, goods_spu.forty_eight_hours_refun, goods_spu.Twenty_four_hours_refun,
		goods_spu.start_province, goods_spu.start_city, goods_spu.start_region, goods_spu.dest_province, goods_spu.dest_city,
		goods_spu.dest_region,goods_spu.phone,goods_spu.is_show,  goods_spu.rq_code,goods_spu.remind,goods_spu.must_adult

	</sql>

	<sql id="goodsSpuSql2">
		goods_spu.`id`,
		goods_spu.`spu_code`,
		goods_spu.`name`,
		goods_spu.`sell_point`,
		null `description`,
		goods_spu.`category_first`,
		goods_spu.`category_second`,
		goods_spu.`pic_urls`,
		goods_spu.`shelf`,
		goods_spu.`sort`,
		goods_spu.`sales_price`,
		goods_spu.`market_price`,
		goods_spu.`cost_price`,
		goods_spu.`stock`,
		goods_spu.`sale_num`,
		goods_spu.`create_time`,
		goods_spu.`update_time`,
		goods_spu.`del_flag`,
		goods_spu.`version`,
		goods_spu.`cost_intro`,
		goods_spu.`starting_Point`,
		goods_spu.`dest`,
		goods_spu.`rebate`,
		goods_spu.`child_definition`,
		goods_spu.travel_id,
		goods_spu.older_child_flag,
		goods_spu.young_child_flag,
		goods_spu.elderly_flag,
		goods_spu.adult_flag,
		goods_spu.child_flag,
		goods_spu.boarding_point,
		goods_spu.room_allowance,
		goods_spu.date_info, goods_spu.stay_info, goods_spu.meals_info, goods_spu.level,
		goods_spu.seven_day_refund, goods_spu.fifteen_day_refun, goods_spu.forty_eight_hours_refun, goods_spu.Twenty_four_hours_refun,
		goods_spu.start_province, goods_spu.start_city, goods_spu.start_region, goods_spu.dest_province, goods_spu.dest_city,
		    goods_spu.dest_region,goods_spu.phone,goods_spu.is_show,  goods_spu.rq_code,goods_spu.remind,goods_spu.must_adult
	</sql>

	<select id="selectPage1" resultMap="goodsSpuMap">
		SELECT goods_spu.`id`,
			   goods_spu.`spu_code`,
			   goods_spu.`name`,
			   goods_spu.`sell_point`,
			   null                 `description`,
			   goods_spu.`category_first`,
			   goods_spu.`category_second`,
			   goods_spu.`pic_urls`,
			   goods_spu.`shelf`,
			   goods_spu.`sort`,
			   goods_spu.`sales_price`,
			   goods_spu.`market_price`,
			   goods_spu.`cost_price`,
			   goods_spu.`stock`,
			   goods_spu.`sale_num`,
			   goods_spu.`create_time`,
			   goods_spu.`update_time`,
			   goods_spu.`del_flag`,
			   goods_spu.`version`,
			   goods_spu.`cost_intro`,
			   goods_spu.`starting_Point`,
			   goods_spu.`dest`,
			   goods_spu.`rebate`,
			   goods_spu.`child_definition`,
			   goods_spu.travel_id,
			   goods_spu.older_child_flag,
			   goods_spu.young_child_flag,
			   goods_spu.elderly_flag,
			   goods_spu.adult_flag,
			   goods_spu.child_flag,
			   goods_spu.boarding_point,
			   goods_spu.room_allowance,
			   goods_spu.date_info,
			   goods_spu.stay_info,
			   goods_spu.meals_info,
			   goods_spu.level,
			   goods_spu.seven_day_refund,
			   goods_spu.fifteen_day_refun,
			   goods_spu.forty_eight_hours_refun,
			   goods_spu.Twenty_four_hours_refun,
			   goods_spu.start_province,
			   goods_spu.start_city,
			   goods_spu.start_region,
			   goods_spu.dest_province,
			   goods_spu.dest_city,
			   goods_spu.dest_region,
			   goods_spu.phone,
			   goods_spu.is_show,
			   goods_spu.rq_code,
			   goods_spu.remind,
			   goods_spu.must_adult,
			   goods_spu.specification_type
		FROM goods_spu goods_spu
		<where>
			<if test="query.shelf != null">
				AND goods_spu.`shelf` = #{query.shelf}
			</if>
			<if test="query.isShow != null">
				AND goods_spu.`is_show` = #{query.isShow}
			</if>
			<if test="query.categoryFirst != null">
				AND goods_spu.`category_first` = #{query.categoryFirst}
			</if>
			<if test="query.categorySecond != null">
				AND goods_spu.`category_second` = #{query.categorySecond}
			</if>
			<if test="query.name != null">
				AND goods_spu.`name` like lower(concat('%', #{query.name}, '%'))
			</if>
			<if test="query.spuCode != null">
				AND goods_spu.`spu_code` = #{query.spuCode}
			</if>
			<if test="query.startProvince != null  and query.startProvince != ''">
				and goods_spu.start_province = #{query.startProvince}
			</if>
			<if test="query.startCity != null  and query.startCity != ''">
				and goods_spu.start_city = #{query.startCity}
			</if>
			<if test="query.startRegion != null  and query.startRegion != ''">
				and goods_spu.start_region = #{query.startRegion}
			</if>
			<if test="query.destProvince != null  and query.destProvince != ''">
				and goods_spu.dest_province = #{query.destProvince}
			</if>
			<if test="query.destCity != null  and query.destCity != ''">
				and goods_spu.dest_city = #{query.destCity}
			</if>
			<if test="query.destRegion != null  and query.destRegion != ''">
				and goods_spu.dest_region = #{query.destRegion}
			</if>
			<if test="query.travelId != null  and query.travelId != ''">
				and goods_spu.travel_id = #{query.travelId}
			</if>
		</where>
		order by goods_spu.create_time desc
	</select>



	<select id="selectCollectPageByUserId" resultMap="goodsSpuMap">
		SELECT
		<include refid="goodsSpuSql2"/>
		FROM ty_user_collect c2
		left join goods_spu goods_spu on goods_spu.id=c2.good_id
		<where>
			c2.user_id=#{userId} and goods_spu.id is not null and c2.delete_flag = false
		</where>
		group by c2.user_id,goods_spu.id
		order by c2.create_time desc
	</select>


	<select id="selectById1" resultMap="goodsSpuMap1">
		SELECT
		<include refid="goodsSpuSql"/>
		FROM goods_spu goods_spu
		WHERE goods_spu.`id` = #{id}
	</select>

	<select id="selectById2" resultMap="goodsSpuMap2">
		SELECT
			<include refid="goodsSpuSql"/>
		FROM goods_spu goods_spu
		WHERE goods_spu.`id` = #{id}
	</select>

	<select id="selectOneByShoppingCart" resultMap="goodsSpuMap3">
		SELECT
			<include refid="goodsSpuSql2"/>
		FROM goods_spu goods_spu
		WHERE goods_spu.`id` = #{id}
	</select>

	<select id="selectById4" resultMap="goodsSpuMap4">
		SELECT
		<include refid="goodsSpuSql"/>
		FROM goods_spu goods_spu
		WHERE goods_spu.`id` = #{id}
	</select>


	<!--新版商品管理-->

	<sql id="selectGoodsSpuVo">
		select id, spu_code, name, sell_point, description, category_first, category_second, pic_urls, shelf, sort,
		       sales_price, market_price, cost_price, stock, sale_num, create_time, update_time, del_flag, version,
		       commission, cost_intro, starting_point, dest, rebate, child_definition, travel_id, older_child_flag,
		       young_child_flag, elderly_flag, adult_flag, child_flag,boarding_point, room_allowance, date_info, stay_info,
		       meals_info, level, seven_day_refund, fifteen_day_refun, forty_eight_hours_refun, Twenty_four_hours_refun,
			   start_province, start_city, start_region, dest_province, dest_city, dest_region,phone,is_show,
			   rq_code,remind,must_adult
		from goods_spu
	</sql>

	<select id="selectGoodsSpuList" parameterType="GoodsSpu" resultMap="goodsSpuMap">
		<include refid="selectGoodsSpuVo"/>
		<where>
			<if test="spuCode != null  and spuCode != ''"> and spu_code = #{spuCode}</if>
			<if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
			<if test="sellPoint != null  and sellPoint != ''"> and sell_point = #{sellPoint}</if>
			<if test="description != null  and description != ''"> and description = #{description}</if>
			<if test="categoryFirst != null  and categoryFirst != ''"> and category_first = #{categoryFirst}</if>
			<if test="categorySecond != null  and categorySecond != ''"> and category_second = #{categorySecond}</if>
			<if test="picUrls != null  and picUrls != ''"> and pic_urls = #{picUrls}</if>
			<if test="shelf != null  and shelf != ''"> and shelf = #{shelf}</if>
			<if test="sort != null "> and sort = #{sort}</if>
			<if test="salesPrice != null "> and sales_price = #{salesPrice}</if>
			<if test="marketPrice != null "> and market_price = #{marketPrice}</if>
			<if test="costPrice != null "> and cost_price = #{costPrice}</if>
			<if test="stock != null "> and stock = #{stock}</if>
			<if test="saleNum != null "> and sale_num = #{saleNum}</if>
			<if test="version != null "> and version = #{version}</if>
			<if test="commission != null "> and commission = #{commission}</if>
			<if test="costIntro != null  and costIntro != ''"> and cost_intro = #{costIntro}</if>
			<if test="startingPoint != null  and startingPoint != ''"> and starting_point = #{startingPoint}</if>
			<if test="dest != null  and dest != ''"> and dest = #{dest}</if>
			<if test="rebate != null "> and rebate = #{rebate}</if>
			<if test="childDefinition != null  and childDefinition != ''"> and child_definition = #{childDefinition}</if>
			<if test="travelId != null  and travelId != ''"> and travel_id = #{travelId}</if>
			<if test="olderChildFlag != null  and olderChildFlag != ''"> and older_child_flag = #{olderChildFlag}</if>
			<if test="youngChildFlag != null  and youngChildFlag != ''"> and young_child_flag = #{youngChildFlag}</if>
			<if test="elderlyFlag != null  and elderlyFlag != ''"> and elderly_flag = #{elderlyFlag}</if>
			<if test="adultFlag != null  and adultFlag != ''"> and adult_flag = #{adultFlag}</if>
			<if test="childFlag != null  and childFlag != ''"> and child_flag = #{childFlag}</if>
			<if test="boardingPoint != null  and boardingPoint != ''"> and boarding_point = #{boardingPoint}</if>
			<if test="roomAllowance != null "> and room_allowance = #{roomAllowance}</if>
			<if test="dateInfo != null  and dateInfo != ''"> and date_info = #{dateInfo}</if>
			<if test="stayInfo != null  and stayInfo != ''"> and stay_info = #{stayInfo}</if>
			<if test="mealsInfo != null  and mealsInfo != ''"> and meals_info = #{mealsInfo}</if>
			<if test="level != null  and level != ''"> and level = #{level}</if>
			<if test="sevenDayRefund != null "> and seven_day_refund = #{sevenDayRefund}</if>
			<if test="fifteenDayRefun != null "> and fifteen_day_refun = #{fifteenDayRefun}</if>
			<if test="fortyEightHoursRefun != null "> and forty_eight_hours_refun = #{fortyEightHoursRefun}</if>
			<if test="twentyFourHoursRefun != null "> and Twenty_four_hours_refun = #{twentyFourHoursRefun}</if>
			<if test="startProvince != null  and startProvince != ''"> and start_province = #{startProvince}</if>
			<if test="startCity != null  and startCity != ''"> and start_city = #{startCity}</if>
			<if test="startRegion != null  and startRegion != ''"> and start_region = #{startRegion}</if>
			<if test="destProvince != null  and destProvince != ''"> and dest_province = #{destProvince}</if>
			<if test="destCity != null  and destCity != ''"> and dest_city = #{destCity}</if>
			<if test="destRegion != null  and destRegion != ''"> and dest_region = #{destRegion}</if>
			<if test="isShow != null  and isShow != ''"> and is_Show = #{isShow}</if>
		</where>
	</select>

	<select id="selectGoodsSpuById" parameterType="String" resultMap="GoodsSpuGoodsSpuDetailResult">
		select a.id,
			   a.spu_code,
			   a.name,
			   a.sell_point,
			   a.description,
			   a.category_first,
			   a.category_second,
			   a.shelf,
			   a.sort,
			   a.sales_price,
			   a.market_price,
			   a.cost_price,
			   a.stock,
			   a.sale_num,
			   a.create_time,
			   a.update_time,
			   a.del_flag,
			   a.version,
			   a.commission,
			   a.cost_intro,
			   a.starting_point,
			   a.dest,
			   a.rebate,
			   a.child_definition,
			   a.travel_id,
			   a.older_child_flag,
			   a.young_child_flag,
			   a.elderly_flag,
			   a.adult_flag,
			   a.boarding_point,
			   a.child_flag,
			   a.room_allowance,
			   a.date_info,
			   a.stay_info,
			   a.meals_info,
			   a.level,
			   a.seven_day_refund,
			   a.fifteen_day_refun,
			   a.forty_eight_hours_refun,
			   a.Twenty_four_hours_refun,
			   a.start_province,
			   a.start_city,
			   a.start_region,
			   a.dest_province,
			   a.dest_city,
			   a.dest_region,
			   a.phone,
			   a.is_Show,
			   a.rq_code,
			   a.remind,
			   a.must_adult,
			   a.spot_name,
			   c1.name           as start_provinceName,
			   c2.name           as start_cityName,
			   c3.name           as start_regionName,
			   d.id              as points_id,
			   d.address,
			   d.del_flag,
			   d.create_by,
			   d.create_time,
			   d.update_time,
			   d.update_by,
			   d.travel_id
		from goods_spu a
-- 				 LEFT JOIN (select * from goods_spu_detail where  date &gt;=date_format( NOW(), '%Y-%m-%d' ))b ON b.goods_id = a.id
		<!--			    left join goods_spu_detail b on b.goods_id=a.id-->
		left join goods_pickup_points c on c.goods_id = a.id
		left join travel_agency_pickup_points d on c.pickup_points_id = d.id
		left join ty_city c1 on a.start_province = c1.code
		left join ty_city c2 on a.start_city = c2.code
		left join ty_city c3 on a.start_region = c3.code
		<!--				left join goods_specs specs on specs.goods_id=a.id-->
		where a.id = #{id}
	</select>

	<insert id="insertGoodsSpu" parameterType="GoodsSpu">
		insert into goods_spu
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="spuCode != null">spu_code,</if>
			<if test="name != null and name != ''">name,</if>
			<if test="sellPoint != null and sellPoint != ''">sell_point,</if>
			<if test="description != null and description != ''">description,</if>
			<if test="categoryFirst != null and categoryFirst != ''">category_first,</if>
			<if test="categorySecond != null">category_second,</if>
			<if test="picUrls != null and picUrls != ''">pic_urls,</if>
			<if test="shelf != null and shelf != ''">shelf,</if>
			<if test="sort != null">sort,</if>
			<if test="salesPrice != null">sales_price,</if>
			<if test="marketPrice != null">market_price,</if>
			<if test="costPrice != null">cost_price,</if>
			<if test="stock != null">stock,</if>
			<if test="saleNum != null">sale_num,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="delFlag != null and delFlag != ''">del_flag,</if>
			<if test="version != null">version,</if>
			<if test="commission != null">commission,</if>
			<if test="costIntro != null">cost_intro,</if>
			<if test="startingPoint != null">starting_point,</if>
			<if test="dest != null">dest,</if>
			<if test="rebate != null">rebate,</if>
			<if test="childDefinition != null">child_definition,</if>
			<if test="travelId != null">travel_id,</if>
			<if test="olderChildFlag != null">older_child_flag,</if>
			<if test="youngChildFlag != null">young_child_flag,</if>
			<if test="elderlyFlag != null">elderly_flag,</if>
			<if test="adultFlag != null">adult_flag,</if>
			<if test="childFlag != null">child_flag,</if>
			<if test="boardingPoint != null">boarding_point,</if>
			<if test="roomAllowance != null">room_allowance,</if>
			<if test="dateInfo != null">date_info,</if>
			<if test="stayInfo != null">stay_info,</if>
			<if test="mealsInfo != null">meals_info,</if>
			<if test="level != null">level,</if>
			<if test="sevenDayRefund != null">seven_day_refund,</if>
			<if test="fifteenDayRefun != null">fifteen_day_refun,</if>
			<if test="fortyEightHoursRefun != null">forty_eight_hours_refun,</if>
			<if test="twentyFourHoursRefun != null">Twenty_four_hours_refun,</if>
			<if test="startProvince != null">start_province,</if>
			<if test="startCity != null">start_city,</if>
			<if test="startRegion != null">start_region,</if>
			<if test="destProvince != null">dest_province,</if>
			<if test="destCity != null">dest_city,</if>
			<if test="destRegion != null">dest_region,</if>
			<if test="phone != null">phone,</if>
			<if test="isShow != null">is_Show,</if>
			<if test="remind != null">remind,</if>
			<if test="mustAdult != null">must_adult,</if>
		</trim>
		<trim prefix="attr_values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="spuCode != null">#{spuCode},</if>
			<if test="name != null and name != ''">#{name},</if>
			<if test="sellPoint != null and sellPoint != ''">#{sellPoint},</if>
			<if test="description != null and description != ''">#{description},</if>
			<if test="categoryFirst != null and categoryFirst != ''">#{categoryFirst},</if>
			<if test="categorySecond != null">#{categorySecond},</if>
			<if test="picUrls != null and picUrls != ''">#{picUrls},</if>
			<if test="shelf != null and shelf != ''">#{shelf},</if>
			<if test="sort != null">#{sort},</if>
			<if test="salesPrice != null">#{salesPrice},</if>
			<if test="marketPrice != null">#{marketPrice},</if>
			<if test="costPrice != null">#{costPrice},</if>
			<if test="stock != null">#{stock},</if>
			<if test="saleNum != null">#{saleNum},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="delFlag != null and delFlag != ''">#{delFlag},</if>
			<if test="version != null">#{version},</if>
			<if test="commission != null">#{commission},</if>
			<if test="costIntro != null">#{costIntro},</if>
			<if test="startingPoint != null">#{startingPoint},</if>
			<if test="dest != null">#{dest},</if>
			<if test="rebate != null">#{rebate},</if>
			<if test="childDefinition != null">#{childDefinition},</if>
			<if test="travelId != null">#{travelId},</if>
			<if test="olderChildFlag != null">#{olderChildFlag},</if>
			<if test="youngChildFlag != null">#{youngChildFlag},</if>
			<if test="elderlyFlag != null">#{elderlyFlag},</if>
			<if test="adultFlag != null">#{adultFlag},</if>
			<if test="childFlag != null">#{childFlag},</if>
			<if test="boardingPoint != null">#{boardingPoint},</if>
			<if test="roomAllowance != null">#{roomAllowance},</if>
			<if test="dateInfo != null">#{dateInfo},</if>
			<if test="stayInfo != null">#{stayInfo},</if>
			<if test="mealsInfo != null">#{mealsInfo},</if>
			<if test="level != null">#{level},</if>
			<if test="sevenDayRefund != null">#{sevenDayRefund},</if>
			<if test="fifteenDayRefun != null">#{fifteenDayRefun},</if>
			<if test="fortyEightHoursRefun != null">#{fortyEightHoursRefun},</if>
			<if test="twentyFourHoursRefun != null">#{twentyFourHoursRefun},</if>
			<if test="startProvince != null">#{startProvince},</if>
			<if test="startCity != null">#{startCity},</if>
			<if test="startRegion != null">#{startRegion},</if>
			<if test="destProvince != null">#{destProvince},</if>
			<if test="destCity != null">#{destCity},</if>
			<if test="destRegion != null">#{destRegion},</if>
			<if test="phone != null">#{phone},</if>
			<if test="isShow != null">#{isShow},</if>
			<if test="remind != null">#{remind},</if>
			<if test="mustAdult != null">#{mustAdult},</if>
		</trim>
	</insert>

	<update id="updateGoodsSpu" parameterType="GoodsSpu">
		update goods_spu
		<trim prefix="SET" suffixOverrides=",">
			<if test="spuCode != null">spu_code = #{spuCode},</if>
			<if test="name != null and name != ''">name = #{name},</if>
			<if test="sellPoint != null and sellPoint != ''">sell_point = #{sellPoint},</if>
			<if test="description != null and description != ''">description = #{description},</if>
			<if test="categoryFirst != null and categoryFirst != ''">category_first = #{categoryFirst},</if>
			<if test="categorySecond != null">category_second = #{categorySecond},</if>
			<if test="shelf != null and shelf != ''">shelf = #{shelf},</if>
			<if test="sort != null">sort = #{sort},</if>
			<if test="salesPrice != null">sales_price = #{salesPrice},</if>
			<if test="marketPrice != null">market_price = #{marketPrice},</if>
			<if test="costPrice != null">cost_price = #{costPrice},</if>
			<if test="stock != null">stock = #{stock},</if>
			<if test="saleNum != null">sale_num = #{saleNum},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
			<if test="version != null">version = #{version},</if>
			<if test="commission != null">commission = #{commission},</if>
			<if test="costIntro != null">cost_intro = #{costIntro},</if>
			<if test="startingPoint != null">starting_point = #{startingPoint},</if>
			<if test="dest != null">dest = #{dest},</if>
			<if test="rebate != null">rebate = #{rebate},</if>
			<if test="childDefinition != null">child_definition = #{childDefinition},</if>
			<if test="travelId != null">travel_id = #{travelId},</if>
			<if test="olderChildFlag != null">older_child_flag = #{olderChildFlag},</if>
			<if test="youngChildFlag != null">young_child_flag = #{youngChildFlag},</if>
			<if test="elderlyFlag != null">elderly_flag = #{elderlyFlag},</if>
			<if test="adultFlag != null">adult_flag = #{adultFlag},</if>
			<if test="childFlag != null">child_flag = #{childFlag},</if>
			<if test="boardingPoint != null">boarding_point = #{boardingPoint},</if>
			<if test="roomAllowance != null">room_allowance = #{roomAllowance},</if>
			<if test="dateInfo != null">date_info = #{dateInfo},</if>
			<if test="stayInfo != null">stay_info = #{stayInfo},</if>
			<if test="mealsInfo != null">meals_info = #{mealsInfo},</if>
			<if test="level != null">level = #{level},</if>
			<if test="sevenDayRefund != null">seven_day_refund = #{sevenDayRefund},</if>
			<if test="fifteenDayRefun != null">fifteen_day_refun = #{fifteenDayRefun},</if>
			<if test="fortyEightHoursRefun != null">forty_eight_hours_refun = #{fortyEightHoursRefun},</if>
			<if test="twentyFourHoursRefun != null">Twenty_four_hours_refun = #{twentyFourHoursRefun},</if>
			<if test="startProvince != null">start_province = #{startProvince},</if>
			<if test="startCity != null">start_city = #{startCity},</if>
			<if test="startRegion != null">start_region = #{startRegion},</if>
			<if test="destProvince != null">dest_province = #{destProvince},</if>
			<if test="destCity != null">dest_city = #{destCity},</if>
			<if test="destRegion != null">dest_region = #{destRegion},</if>
			<if test="phone != null">phone = #{phone},</if>
			<if test="isShow != null">is_Show = #{isShow},</if>
			<if test="remind != null">remind = #{remind},</if>
			<if test="mustAdult != null">must_Adult = #{mustAdult},</if>
			<if test="spotName != null">spot_name = #{spotName},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteGoodsSpuById" parameterType="String">
		delete from goods_spu where id = #{id}
	</delete>

	<delete id="deleteGoodsSpuByIds" parameterType="java.lang.String">
		delete from goods_spu where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<delete id="deleteGoodsSpuDetailByGoodsIds" parameterType="java.lang.String">
		delete from goods_spu_detail where goods_id in
		<foreach item="goodsId" collection="array" open="(" separator="," close=")">
			#{goodsId}
		</foreach>
	</delete>

	<delete id="deleteGoodsSpuDetailByGoodsId" parameterType="String">
		delete from goods_spu_detail where goods_id = #{id}
	</delete>

	<delete id="deleteGoodsSpecificationValueByGoodsId" parameterType="String">
		delete from goods_specification_value where goods_id = #{id}
	</delete>

	<insert id="batchGoodsSpuDetail">
		insert into goods_spu_detail( id, goods_id, price, adult_price, child_price, elderly_price,
		                             date, commission, create_by, create_time, update_by, update_time,
		                             stock, sale_Num,end_time) values
		<foreach item="item" index="index" collection="list" separator=",">
			( #{item.id}, #{item.goodsId}, #{item.price}, #{item.adultPrice}, #{item.childPrice},
			 #{item.elderlyPrice}, #{item.date}, #{item.commission}, #{item.createBy}, #{item.createTime},
			 #{item.updateBy}, #{item.updateTime}, #{item.stock}, #{item.saleNum},#{item.endTime})
		</foreach>
	</insert>


	<select id="getGoodsSpuDetailById" parameterType="String" resultMap="GoodsSpuDetailResult">
		select
			b.id as b_id, b.goods_id as b_goods_id , b.price as b_price, b.adult_price as b_adult_price, b.child_price as b_child_price, b.elderly_price as b_elderly_price,
			   b.date as b_date, b.commission as b_commission,
			   b.create_by as b_create_by, b.create_time as b_create_time,
			   b.update_by as b_update_by, b.update_time as b_update_time, b.stock as b_stock, b.sale_Num as b_sale_Num,b.end_time as b_endTime
		from goods_spu_detail b
		where b.id = #{id}
	</select>

	<update id="updateGoodsSpuDetail" parameterType="GoodsSpuDetail">
		update goods_spu_detail
		<trim prefix="SET" suffixOverrides=",">
			<if test="goodsId != null">goods_id = #{goodsId},</if>
			<if test="price != null">price = #{price},</if>
			<if test="adultPrice != null">adult_price = #{adultPrice},</if>
			<if test="childPrice != null">child_price = #{childPrice},</if>
			<if test="elderlyPrice != null">elderly_price = #{elderlyPrice},</if>
			<if test="date != null">date = #{date},</if>
			<if test="commission != null">commission = #{commission},</if>
			<if test="createBy != null">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="stock != null">stock = #{stock},</if>
			<if test="saleNum != null">sale_Num = #{saleNum},</if>
			<if test="endTime != null">end_time = #{endTime},</if>
		</trim>
		where id = #{id}
	</update>

	<update id="updateGoodsSpuRQCode" parameterType="String">
		update goods_spu
		set rq_code= #{path}
		where id = #{id}
	</update>


	<select id="selectBrowsePageByUserId" resultMap="goodsSpuMap">
		SELECT
		<include refid="goodsSpuSql2"/>
		FROM goods_spu goods_spu
		right join ty_user_browse_info b  on goods_spu.id= b.good_id
		<where>
			b.user_id=#{userId}
		</where>
		group by b.user_id,goods_spu.id
		order by b.create_time desc
	</select>

	<insert id="batchGoodsSpecs">
		insert into goods_specification_value(id,name,goods_id,create_by,create_time) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.id} , #{item.name}, #{item.goodsId}, #{item.createBy}, #{item.createTime} )
		</foreach>
	</insert>

</mapper>
