<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.mall.mapper.OrderItemMapper">

	<resultMap id="orderItemMap" type="com.joolun.mall.entity.OrderItem">
		<id property="id" column="id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="orderId" column="order_id"/>
		<result property="spuId" column="spu_id"/>
		<result property="skuId" column="sku_id"/>
		<result property="spuName" column="spu_name"/>
		<result property="skuName" column="sku_name"/>
		<result property="picUrl" column="pic_url"/>
		<result property="quantity" column="quantity"/>
		<result property="salesPrice" column="sales_price"/>
		<result property="freightPrice" column="freight_price"/>
		<result property="paymentPrice" column="payment_price"/>
		<result property="remark" column="remark"/>
		<result property="status" column="status"/>
		<result property="isRefund" column="is_refund"/>
		<result property="joinUser"    column="join_user"    />
		<result property="commission"    column="commission"    />
		<result property="beneficiary"    column="beneficiary"    />
		<result property="detailId"    column="detail_id"    />
		<result property="adultQuantity"    column="adult_quantity"    />
		<result property="olderChildQuantity"    column="older_child_quantity"    />
		<result property="youngChildQuantity"    column="young_child_quantity"    />
		<result property="elderlyQuantity"    column="elderly_quantity"    />
		<result property="sevenDayRefund"    column="seven_day_refund"    />
		<result property="fifteenDayRefun"    column="fifteen_day_refun"    />
		<result property="fortyEightHoursRefun"    column="forty_eight_hours_refun"    />
		<result property="twentyFourHoursRefun"    column="Twenty_four_hours_refun"    />
		<result property="departureDate"    column="departure_date"    />
		<result property="refundApplyTime"    column="refund_apply_time"    />
		<result property="previousStatus"    column="previous_status"    />
		<result property="specs"    column="specs"    />
		<result property="specsName"    column="specs_name"    />
	</resultMap>

	<sql id="orderItemSql">
		order_item.`id`,
		order_item.`del_flag`,
		order_item.`create_time`,
		order_item.`update_time`,
		order_item.`order_id`,
		order_item.`spu_id`,
		order_item.`sku_id`,
		order_item.`spu_name`,
		order_item.`sku_name`,
		order_item.`pic_url`,
		order_item.`quantity`,
		order_item.`sales_price`,
		order_item.`freight_price`,
		order_item.`payment_price`,
		order_item.`remark`,
		order_item.`status`,
		order_item.`is_refund`,
		order_item.join_user,
		order_item.commission,
		order_item.beneficiary,
		order_item.detail_id,
		order_item.adult_quantity,
		order_item.older_child_quantity,
		order_item.young_child_quantity,
		order_item.elderly_quantity,
		order_item.seven_day_refund,
		order_item.fifteen_day_refun,
		order_item.forty_eight_hours_refun,
		order_item.Twenty_four_hours_refun,
		order_item.departure_date,
		order_item.refund_apply_time,
		order_item.previous_status,
		order_item.specs
	</sql>

	<select id="selectList2" resultMap="orderItemMap">
		SELECT
		<include refid="orderItemSql"/>
 		FROM order_item order_item
		WHERE order_item.`order_id` = #{orderId}
	</select>

	<sql id="selectOrderItemVo">
		select id,
			   del_flag,
			   create_time,
			   update_time,
			   order_id,
			   spu_id,
			   sku_id,
			   spu_name,
			   sku_name,
			   pic_url,
			   quantity,
			   sales_price,
			   freight_price,
			   payment_price,
			   remark,
			   status,
			   is_refund,
			   join_user,
			   commission,
			   beneficiary,
			   detail_id,
			   adult_quantity,
			   older_child_quantity,
			   young_child_quantity,
			   elderly_quantity,
			   seven_day_refund,
			   fifteen_day_refun,
			   forty_eight_hours_refun,
			   Twenty_four_hours_refun,
			   departure_date,
			   refund_apply_time,
			   previous_status,
			   specs
		from order_item
	</sql>

</mapper>
