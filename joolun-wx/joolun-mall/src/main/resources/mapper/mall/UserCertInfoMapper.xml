<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joolun.mall.mapper.UserCertInfoMapper">
    
    <resultMap type="UserCertInfo" id="UserCertInfoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="certType"    column="cert_type"    />
        <result property="certId"    column="cert_id"    />
        <result property="validStartData"    column="valid_start_data"    />
        <result property="validEndData"    column="valid_end_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="phone"    column="phone"    />
        <result property="userType"    column="user_type"    />
        <result property="sex"    column="sex"    />
        <result property="orderId"    column="order_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="openId"    column="open_id"    />
    </resultMap>

    <sql id="selectUserCertInfoVo">
        select id, name, cert_type, cert_id, valid_start_data, valid_end_data, create_by, create_time,
               update_by, update_time, phone, user_type, sex from user_cert_info
        left join wx_user u
    </sql>

    <select id="selectUserCertInfoList" parameterType="UserCertInfo" resultMap="UserCertInfoResult">
        <include refid="selectUserCertInfoVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="certType != null  and certType != ''"> and cert_type = #{certType}</if>
            <if test="certId != null  and certId != ''"> and cert_id = #{certId}</if>
            <if test="validStartData != null  and validStartData != ''"> and valid_start_data = #{validStartData}</if>
            <if test="validEndData != null  and validEndData != ''"> and valid_end_data = #{validEndData}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
        </where>
    </select>
    
    <select id="selectUserCertInfoById" parameterType="String" resultMap="UserCertInfoResult">
        <include refid="selectUserCertInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserCertInfo" parameterType="UserCertInfo">
        insert into user_cert_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="name != null">name,</if>
            <if test="certType != null">cert_type,</if>
            <if test="certId != null">cert_id,</if>
            <if test="validStartData != null">valid_start_data,</if>
            <if test="validEndData != null">valid_end_data,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="phone != null">phone,</if>
            <if test="userType != null">user_type,</if>
            <if test="sex != null">sex,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="certType != null">#{certType},</if>
            <if test="certId != null">#{certId},</if>
            <if test="validStartData != null">#{validStartData},</if>
            <if test="validEndData != null">#{validEndData},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="phone != null">#{phone},</if>
            <if test="userType != null">#{userType},</if>
            <if test="sex != null">#{sex},</if>
         </trim>
    </insert>

    <update id="updateUserCertInfo" parameterType="UserCertInfo">
        update user_cert_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="certType != null">cert_type = #{certType},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="validStartData != null">valid_start_data = #{validStartData},</if>
            <if test="validEndData != null">valid_end_data = #{validEndData},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="sex != null">sex = #{sex},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserCertInfoById" parameterType="String">
        delete from user_cert_info where id = #{id}
    </delete>

    <delete id="deleteUserCertInfoByIds" parameterType="java.lang.String">
        delete from user_cert_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="selectSql">
        select a.id, a.name, a.cert_type, a.cert_id, a.valid_start_data, a.valid_end_data,
               a.create_by, a.create_time, a.update_by, a.update_time,a.user_type,a.sex,a.phone,u.nick_name,u.open_id
        from user_cert_info a left join wx_user u on a.create_by = u.id
    </sql>

    <select id="selectCertInfo" parameterType="UserCertInfo" resultMap="UserCertInfoResult">
        <include refid="selectSql"/>
        <where>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="certType != null  and certType != ''"> and a.cert_type = #{certType}</if>
            <if test="certId != null  and certId != ''"> and a.cert_id = #{certId}</if>
            <if test="validStartData != null  and validStartData != ''"> and a.valid_start_data = #{validStartData}</if>
            <if test="validEndData != null  and validEndData != ''"> and a.valid_end_data = #{validEndData}</if>
            <if test="openId != null  and openId != ''"> and u.open_id = #{openId}</if>
            <if test="userType != null  and userType != ''"> and u.user_type = #{userType}</if>
            <if test="sex != null  and sex != ''"> and u.sex = #{sex}</if>
            <if test="phone != null  and phone != ''"> and u.phone like concat('%', #{phone}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and u.create_By = #{createBy}</if>
        </where>
    </select>

    <select id="selectByPage" parameterType="UserCertInfo" resultMap="UserCertInfoResult">
        <include refid="selectSql"/>
        <where>
            <if test="query.createBy != null  and query.createBy != ''"> and a.create_by = #{query.createBy}</if>
        </where>
    </select>


    <select id="selectOrderTravelersByOrderId" parameterType="UserCertInfo" resultMap="UserCertInfoResult">
        select a.id, a.name, a.cert_type, a.cert_id, a.valid_start_data, a.valid_end_data,
        a.create_by, a.create_time, a.update_by, a.update_time,a.user_type,a.sex,a.phone
        from user_cert_info a right join order_travelers o on a.id = o.user_id
        where o.order_id=#{orderId}
    </select>

    <select id="selectOrderTravelersByOrderIds" parameterType="UserCertInfo" resultMap="UserCertInfoResult">
        select a.id, a.name, a.cert_type, a.cert_id, a.valid_start_data, a.valid_end_data,
        a.create_by, a.create_time, a.update_by, a.update_time,a.user_type,a.sex,a.phone,o.order_id
        from user_cert_info a right join order_travelers o on a.id = o.user_id
        <where>
            <if test="orderIds != null and orderIds.size() > 0">
                o.order_id in
                <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
                    #{orderId}
            </foreach>
            </if>
        </where>
    </select>
</mapper>