package com.joolun.mall.service;

import com.joolun.common.core.domain.AjaxResult;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.config.PaymentConfig;
import com.joolun.mall.entity.OrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * 支付取消功能测试
 * 验证模拟支付任务可以被正确取消，防止用户取消支付后订单状态仍被更新的问题
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PaymentCancelTest {

    @Mock
    private OrderInfoService orderInfoService;
    
    @Mock
    private PaymentConfig paymentConfig;
    
    @Mock
    private PaymentConfig.Mock mockConfig;
    
    private MockPaymentService mockPaymentService;
    private WxPaymentService wxPaymentService;
    private OrderInfo testOrderInfo;
    
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 配置模拟支付设置 - 使用lenient模式避免不必要的stubbing警告
        lenient().when(paymentConfig.getMock()).thenReturn(mockConfig);
        lenient().when(mockConfig.isEnabled()).thenReturn(true);
        lenient().when(mockConfig.getSuccessRate()).thenReturn(95);
        lenient().when(mockConfig.getCallbackDelaySeconds()).thenReturn(2);
        lenient().when(mockConfig.isCallbackDelay()).thenReturn(true);
        
        // 创建模拟支付服务
        mockPaymentService = new MockPaymentService(paymentConfig, orderInfoService);
        
        // 创建测试订单
        testOrderInfo = new OrderInfo();
        testOrderInfo.setId("test_order_001");
        testOrderInfo.setOrderNo("ORDER_20241225_TEST_001");
        testOrderInfo.setName("测试商品");
        testOrderInfo.setIsPay(CommonConstants.NO);
        testOrderInfo.setPaymentPrice(new BigDecimal("99.99"));
        testOrderInfo.setUserId("test_user_001");
    }
    
    /**
     * 测试支付任务取消功能 - 应该能够成功取消未执行的支付任务
     */
    @Test
    public void should_cancel_payment_task_successfully_when_task_not_executed() throws Exception {
        // Given - 创建支付任务
        lenient().when(orderInfoService.getById(testOrderInfo.getId())).thenReturn(testOrderInfo);
        
        Map<String, Object> paymentResult = mockPaymentService.mockUnifiedOrder(testOrderInfo);
        assertNotNull("支付结果不应为空", paymentResult);
        assertTrue("应该有待处理的支付任务", mockPaymentService.getPendingPaymentTaskCount() > 0);
        
        // When - 取消支付任务
        boolean cancelled = mockPaymentService.cancelPaymentTask(testOrderInfo.getOrderNo());
        
        // Then - 验证取消结果
        assertTrue("支付任务应该被成功取消", cancelled);
        assertEquals("取消后不应有待处理任务", 0, mockPaymentService.getPendingPaymentTaskCount());
        
        // 等待确保任务确实被取消，不会执行回调
        Thread.sleep(3000);
        verify(orderInfoService, never()).notifyOrder(any(OrderInfo.class));
        
        log.info("[TEST] 支付任务取消测试通过");
    }
    
    /**
     * 测试重复取消支付任务 - 应该能够处理重复取消请求
     */
    @Test
    public void should_handle_duplicate_cancel_requests_gracefully() {
        // Given - 创建支付任务
        lenient().when(orderInfoService.getById(testOrderInfo.getId())).thenReturn(testOrderInfo);
        mockPaymentService.mockUnifiedOrder(testOrderInfo);
        
        // When - 第一次取消
        boolean firstCancel = mockPaymentService.cancelPaymentTask(testOrderInfo.getOrderNo());
        
        // Then - 第一次取消应该成功
        assertTrue("第一次取消应该成功", firstCancel);
        
        // When - 第二次取消
        boolean secondCancel = mockPaymentService.cancelPaymentTask(testOrderInfo.getOrderNo());
        
        // Then - 第二次取消应该返回false（任务已不存在）
        assertFalse("第二次取消应该返回false", secondCancel);
        
        log.info("[TEST] 重复取消处理测试通过");
    }
    
    /**
     * 测试取消不存在的支付任务 - 应该优雅处理
     */
    @Test
    public void should_handle_cancel_nonexistent_task_gracefully() {
        // Given - 没有创建任何支付任务
        String nonexistentOrderNo = "NONEXISTENT_ORDER_NO";
        
        // When - 尝试取消不存在的任务
        boolean cancelled = mockPaymentService.cancelPaymentTask(nonexistentOrderNo);
        
        // Then - 应该返回false，不抛出异常
        assertFalse("取消不存在的任务应该返回false", cancelled);
        
        log.info("[TEST] 取消不存在任务处理测试通过");
    }
    
    /**
     * 测试支付任务自动执行 - 如果不取消，任务应该会自动执行
     */
    @Test
    public void should_execute_payment_callback_when_not_cancelled() throws Exception {
        // Given - 配置短延迟以便快速测试
        lenient().when(mockConfig.getCallbackDelaySeconds()).thenReturn(1);
        lenient().when(orderInfoService.getById(testOrderInfo.getId())).thenReturn(testOrderInfo);
        
        // When - 创建支付任务但不取消
        mockPaymentService.mockUnifiedOrder(testOrderInfo);
        
        // Then - 等待任务执行
        Thread.sleep(2000);
        
        // 验证回调被执行
        verify(orderInfoService, times(1)).notifyOrder(any(OrderInfo.class));
        
        log.info("[TEST] 支付任务自动执行测试通过");
    }
    
    /**
     * 测试已支付订单的回调处理 - 应该跳过已支付订单
     */
    @Test
    public void should_skip_callback_for_already_paid_order() throws Exception {
        // Given - 创建已支付的订单
        OrderInfo paidOrder = new OrderInfo();
        paidOrder.setId("paid_order_001");
        paidOrder.setOrderNo("PAID_ORDER_20241225_001");
        paidOrder.setName("已支付测试商品");
        paidOrder.setIsPay(CommonConstants.YES); // 已支付
        paidOrder.setPaymentPrice(new BigDecimal("88.88")); // 设置订单金额
        paidOrder.setUserId("test_user_paid");
        
        lenient().when(mockConfig.getCallbackDelaySeconds()).thenReturn(1);
        lenient().when(orderInfoService.getById(paidOrder.getId())).thenReturn(paidOrder);
        
        // When - 创建支付任务
        mockPaymentService.mockUnifiedOrder(paidOrder);
        
        // Then - 等待任务执行
        Thread.sleep(2000);
        
        // 验证回调不被执行（因为订单已支付）
        verify(orderInfoService, never()).notifyOrder(any(OrderInfo.class));
        
        log.info("[TEST] 已支付订单跳过回调测试通过");
    }
    
    /**
     * 测试资源清理 - 服务关闭时应该清理所有任务
     */
    @Test
    public void should_cleanup_all_tasks_on_service_destroy() {
        // Given - 创建多个支付任务
        lenient().when(orderInfoService.getById(any(String.class))).thenReturn(testOrderInfo);
        
        for (int i = 0; i < 3; i++) {
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setId("order_" + i);
            orderInfo.setOrderNo("ORDER_" + i);
            orderInfo.setName("测试商品_" + i);
            orderInfo.setIsPay(CommonConstants.NO);
            orderInfo.setPaymentPrice(new BigDecimal("10.00"));
            orderInfo.setUserId("test_user_" + i);
            
            mockPaymentService.mockUnifiedOrder(orderInfo);
        }
        
        assertTrue("应该有多个待处理任务", mockPaymentService.getPendingPaymentTaskCount() >= 3);
        
        // When - 清理所有任务
        mockPaymentService.destroy();
        
        // Then - 验证所有任务被清理
        assertEquals("清理后应该没有待处理任务", 0, mockPaymentService.getPendingPaymentTaskCount());
        
        log.info("[TEST] 资源清理测试通过");
    }
} 