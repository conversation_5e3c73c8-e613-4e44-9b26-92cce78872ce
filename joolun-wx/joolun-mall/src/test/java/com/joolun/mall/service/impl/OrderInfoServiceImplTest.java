package com.joolun.mall.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.joolun.mall.config.CommonConstants;
import com.joolun.mall.dto.PlaceOrderDTO;
import com.joolun.mall.dto.PlaceOrderGoodsDTO;
import com.joolun.mall.dto.OrderItemAppDTO;
import com.joolun.mall.entity.*;
import com.joolun.mall.service.OrderInfoService;
import com.joolun.mall.service.OrderItemService;
import com.joolun.mall.service.GoodsSpuService;
import com.joolun.common.utils.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * OrderInfoServiceImpl 单元测试
 * 主要测试订单创建相关的基础数据结构验证
 * 为后续重构提供安全保障
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderInfoServiceImplTest {

  @Mock
  private OrderInfoService orderInfoService;

  private PlaceOrderDTO placeOrderDTO;
  private GoodsSpu goodsSpu;
  private GoodsSpuDetail goodsSpuDetail;

  /**
   * 测试数据初始化
   * 为每个测试方法准备标准的测试数据
   */
  @Before
  public void setUp() {
    // 初始化测试数据
    placeOrderDTO = new PlaceOrderDTO();
    placeOrderDTO.setDetailId("detail123");
    placeOrderDTO.setAdultQuantity(2);
    placeOrderDTO.setElderlyQuantity(1);
    placeOrderDTO.setOlderChildQuantity(1);
    placeOrderDTO.setYoungChildQuantity(0);
    placeOrderDTO.setReplenishRoomNum(BigDecimal.ONE);
    placeOrderDTO.setOrderContactName("张三");
    placeOrderDTO.setOrderContactPhone("13800138000");

    // 设置商品SKU信息
    PlaceOrderGoodsDTO orderGoods = new PlaceOrderGoodsDTO();
    orderGoods.setSpuId("spu123");
    List<PlaceOrderGoodsDTO> skus = new ArrayList<>();
    skus.add(orderGoods);
    placeOrderDTO.setSkus(skus);

    // 设置用户身份信息
    UserCertInfo userCertInfo = new UserCertInfo();
    userCertInfo.setId("cert123");
    userCertInfo.setName("张三");
    userCertInfo.setCertId("123456789012345678");
    List<UserCertInfo> userCertInfos = new ArrayList<>();
    userCertInfos.add(userCertInfo);
    placeOrderDTO.setUserCertInfos(userCertInfos);

    // 初始化商品信息
    goodsSpu = new GoodsSpu();
    goodsSpu.setId("spu123");
    goodsSpu.setName("测试商品");
    goodsSpu.setShelf(CommonConstants.YES);
    goodsSpu.setSalesPrice(new BigDecimal("100.00"));
    goodsSpu.setRoomAllowance(new BigDecimal("50.00"));
    goodsSpu.setTravelId("travel123");
    goodsSpu.setFifteenDayRefun(new BigDecimal("0.2"));
    goodsSpu.setSevenDayRefund(new BigDecimal("0.4"));
    goodsSpu.setFortyEightHoursRefun(new BigDecimal("0.6"));
    goodsSpu.setTwentyFourHoursRefun(new BigDecimal("0.8"));

    // Mock 商品图片
    List<GoodsSpuUploadFile> picUrls = new ArrayList<>();
    GoodsSpuUploadFile picFile = new GoodsSpuUploadFile();
    picFile.setFileUrl("http://example.com/pic.jpg");
    picUrls.add(picFile);
    goodsSpu.setPicUrls(picUrls);

    // 初始化商品详情
    goodsSpuDetail = new GoodsSpuDetail();
    goodsSpuDetail.setId("detail123");
    goodsSpuDetail.setStock(100);
    goodsSpuDetail.setAdultPrice(new BigDecimal("200.00"));
    goodsSpuDetail.setElderlyPrice(new BigDecimal("180.00"));
    goodsSpuDetail.setChildPrice(new BigDecimal("100.00"));
    goodsSpuDetail.setCommission(new BigDecimal("5"));
    goodsSpuDetail.setDate("2024-12-25");
  }

  /** 测试下单DTO数据结构完整性 验证订单基本信息、联系人信息、商品信息等关键字段是否正确设置 */
  @Test
  public void should_validate_place_order_dto_structure() {
    // Then - 验证测试数据结构
    assertNotNull(placeOrderDTO);
    assertEquals("detail123", placeOrderDTO.getDetailId());
    assertEquals(2, (int) placeOrderDTO.getAdultQuantity());
    assertEquals(1, (int) placeOrderDTO.getElderlyQuantity());
    assertEquals("张三", placeOrderDTO.getOrderContactName());
    assertEquals("13800138000", placeOrderDTO.getOrderContactPhone());
    assertEquals(1, placeOrderDTO.getSkus().size());
    assertEquals(1, placeOrderDTO.getUserCertInfos().size());
  }

  /**
   * 测试用户身份信息数据正确性
   * 验证出行人身份证信息是否正确设置和获取
   */
  @Test
  public void should_have_correct_user_cert_info_data() {
    // Given
    UserCertInfo userCertInfo = placeOrderDTO.getUserCertInfos().get(0);
    
    // Then - 验证身份信息
    assertEquals("cert123", userCertInfo.getId());
    assertEquals("张三", userCertInfo.getName());
    assertEquals("123456789012345678", userCertInfo.getCertId());
  }

  /**
   * 测试出行人数量计算逻辑
   * 验证成人、老人、儿童数量统计是否正确
   */
  @Test 
  public void should_calculate_total_quantity_correctly() {
    // When - 计算总人数
    int totalQuantity = placeOrderDTO.getAdultQuantity() + 
                       placeOrderDTO.getElderlyQuantity() + 
                       placeOrderDTO.getOlderChildQuantity() + 
                       placeOrderDTO.getYoungChildQuantity();
    
    // Then - 验证总数计算正确
    assertEquals(4, totalQuantity);
  }

  /**
   * 测试联系人信息有效性
   * 验证订单联系人姓名和电话号码不为空且有效
   */
  @Test
  public void should_validate_contact_information() {
    // Then - 验证联系人信息
    assertNotNull(placeOrderDTO.getOrderContactName());
    assertNotNull(placeOrderDTO.getOrderContactPhone());
    assertFalse(placeOrderDTO.getOrderContactName().isEmpty());
    assertFalse(placeOrderDTO.getOrderContactPhone().isEmpty());
  }

  /**
   * 测试商品详情ID有效性
   * 验证商品详情ID正确设置，确保订单关联到具体的商品规格
   */
  @Test
  public void should_have_valid_detail_id() {
    // Then - 验证详情ID
    assertNotNull(placeOrderDTO.getDetailId());
    assertEquals("detail123", placeOrderDTO.getDetailId());
  }

  /**
   * 测试房差数量数据有效性
   * 验证补房差数量为非负数，确保房费计算的准确性
   */
  @Test
  public void should_have_non_null_replenish_room_num() {
    // Then - 验证房差数量
    assertNotNull(placeOrderDTO.getReplenishRoomNum());
    assertTrue(placeOrderDTO.getReplenishRoomNum().compareTo(BigDecimal.ZERO) >= 0);
  }

  /**
   * 测试商品基本信息完整性
   * 验证商品对象的关键属性是否正确设置
   */
  @Test
  public void should_have_valid_goods_spu_data() {
    // Then - 验证商品基本信息
    assertNotNull(goodsSpu);
    assertEquals("spu123", goodsSpu.getId());
    assertEquals("测试商品", goodsSpu.getName());
    assertEquals(CommonConstants.YES, goodsSpu.getShelf());
    assertEquals(new BigDecimal("100.00"), goodsSpu.getSalesPrice());
    assertEquals("travel123", goodsSpu.getTravelId());
    assertNotNull(goodsSpu.getPicUrls());
    assertEquals(1, goodsSpu.getPicUrls().size());
    assertEquals("http://example.com/pic.jpg", goodsSpu.getPicUrls().get(0).getFileUrl());
  }

  /**
   * 测试商品详情信息完整性
   * 验证商品详情的价格、库存等关键信息
   */
  @Test
  public void should_have_valid_goods_detail_data() {
    // Then - 验证商品详情信息
    assertNotNull(goodsSpuDetail);
    assertEquals("detail123", goodsSpuDetail.getId());
    assertEquals(100, (int) goodsSpuDetail.getStock());
    assertEquals(new BigDecimal("200.00"), goodsSpuDetail.getAdultPrice());
    assertEquals(new BigDecimal("180.00"), goodsSpuDetail.getElderlyPrice());
    assertEquals(new BigDecimal("100.00"), goodsSpuDetail.getChildPrice());
    assertEquals(new BigDecimal("5"), goodsSpuDetail.getCommission());
    assertEquals("2024-12-25", goodsSpuDetail.getDate());
  }

  /**
   * 测试价格计算基础数据准确性
   * 验证不同人群的价格设置是否符合业务逻辑（成人价格 > 老人价格 > 儿童价格）
   */
  @Test
  public void should_have_logical_price_structure() {
    // Then - 验证价格逻辑
    BigDecimal adultPrice = goodsSpuDetail.getAdultPrice();
    BigDecimal elderlyPrice = goodsSpuDetail.getElderlyPrice();
    BigDecimal childPrice = goodsSpuDetail.getChildPrice();
    
    assertTrue("成人价格应该高于老人价格", adultPrice.compareTo(elderlyPrice) > 0);
    assertTrue("老人价格应该高于儿童价格", elderlyPrice.compareTo(childPrice) > 0);
    assertTrue("成人价格应该大于0", adultPrice.compareTo(BigDecimal.ZERO) > 0);
  }

  /**
   * 测试退改政策数据有效性
   * 验证商品的退改政策设置是否合理（时间越近退改费用越高）
   */
  @Test
  public void should_have_valid_refund_policy() {
    // Then - 验证退改政策
    BigDecimal fifteenDay = goodsSpu.getFifteenDayRefun();
    BigDecimal sevenDay = goodsSpu.getSevenDayRefund();
    BigDecimal fortyEightHours = goodsSpu.getFortyEightHoursRefun();
    BigDecimal twentyFourHours = goodsSpu.getTwentyFourHoursRefun();
    
    // 验证退改费用递增逻辑（距离出发时间越近，退改费用比例越高）
    assertTrue("24小时退改费用应该不低于48小时", twentyFourHours.compareTo(fortyEightHours) >= 0);
    assertTrue("48小时退改费用应该不低于7天", fortyEightHours.compareTo(sevenDay) >= 0);
    assertTrue("7天退改费用应该不低于15天", sevenDay.compareTo(fifteenDay) >= 0);
    
    // 验证所有退改费用都在合理范围内(0-1之间，表示百分比)
    assertTrue("15天退改费用应该在0-1之间", fifteenDay.compareTo(BigDecimal.ZERO) >= 0 && fifteenDay.compareTo(BigDecimal.ONE) <= 0);
    assertTrue("24小时退改费用应该在0-1之间", twentyFourHours.compareTo(BigDecimal.ZERO) >= 0 && twentyFourHours.compareTo(BigDecimal.ONE) <= 0);
  }

  /**
   * ====== 新增：SKU结构迁移后的单元测试 ======
   */

  /**
   * 测试订单查询接口当前结构 - 验证现有字段完整性
   * 验证当前订单结构的基本字段，为后续SKU迁移做准备
   */
  @Test
  public void should_get_order_detail_successfully_with_current_structure() {
    // Given - 构建当前结构的订单数据
    OrderInfo orderInfo = new OrderInfo();
    orderInfo.setId("order_123");
    orderInfo.setName("测试线路订单");
    orderInfo.setStatus("3"); // 已完成状态
    orderInfo.setIsPay(CommonConstants.YES);
    
    // 设置订单项，使用当前结构
    List<OrderItem> orderItems = new ArrayList<>();
    OrderItem orderItem = new OrderItem();
    orderItem.setId("item_123");
    orderItem.setOrderId("order_123");
    orderItem.setSpuId("spu_123");
    orderItem.setSpuName("测试商品");
    orderItem.setQuantity(2);
    orderItem.setAdultQuantity(2);
    orderItem.setElderlyQuantity(0);
    orderItem.setOlderChildQuantity(0);
    orderItem.setYoungChildQuantity(0);
    orderItem.setSalesPrice(new BigDecimal("200.00"));
    orderItem.setDetailId("detail_123"); // 当前的详情ID，后续会改为calendar_price_id
    orderItems.add(orderItem);
    orderInfo.setListOrderItem(orderItems);
    
    // When & Then - 验证订单数据结构的完整性
    assertNotNull("订单信息不能为空", orderInfo);
    assertEquals("订单ID应该匹配", "order_123", orderInfo.getId());
    assertEquals("订单状态应该为已完成", "3", orderInfo.getStatus());
    assertEquals("支付状态应该为已支付", CommonConstants.YES, orderInfo.getIsPay());
    
    // 验证订单项中的基本信息
    assertNotNull("订单项列表不能为空", orderInfo.getListOrderItem());
    assertEquals("订单项数量应该为1", 1, orderInfo.getListOrderItem().size());
    
    OrderItem item = orderInfo.getListOrderItem().get(0);
    assertNotNull("SPU ID不能为空", item.getSpuId());
    assertEquals("SPU ID应该匹配", "spu_123", item.getSpuId());
    assertNotNull("详情ID不能为空", item.getDetailId());
    assertEquals("详情ID应该匹配", "detail_123", item.getDetailId());
    
    // 注意：后续迁移后，这里应该增加SKU ID的验证
    // assertNotNull("SKU ID不能为空", item.getSkuId()); // 待迁移后启用
  }

  /**
   * 测试历史订单查询 - 基于GoodsSpuDetail的订单
   * 验证当前基于GoodsSpuDetail结构的历史订单能正常查询
   */
  @Test
  public void should_handle_historical_order_with_goods_spu_detail_gracefully() {
    // Given - 构建基于GoodsSpuDetail的历史订单数据
    OrderInfo orderInfo = new OrderInfo();
    orderInfo.setId("old_order_456");
    orderInfo.setName("历史订单");
    orderInfo.setStatus("3");
    orderInfo.setIsPay(CommonConstants.YES);
    
    // 订单项使用GoodsSpuDetail ID（旧结构）
    List<OrderItem> orderItems = new ArrayList<>();
    OrderItem orderItem = new OrderItem();
    orderItem.setId("item_456");
    orderItem.setOrderId("old_order_456");
    orderItem.setSpuId("spu_456");
    orderItem.setSpuName("历史商品");
    orderItem.setQuantity(1);
    orderItem.setSalesPrice(new BigDecimal("150.00"));
    orderItem.setDetailId("goods_spu_detail_456"); // 旧的GoodsSpuDetail ID
    orderItems.add(orderItem);
    orderInfo.setListOrderItem(orderItems);
    
    // When & Then - 验证系统能正常处理历史订单结构
    assertNotNull("订单信息不能为空", orderInfo);
    assertEquals("订单ID应该匹配", "old_order_456", orderInfo.getId());
    
    OrderItem item = orderInfo.getListOrderItem().get(0);
    assertNotNull("SPU ID应该存在", item.getSpuId());
    assertEquals("SPU ID应该匹配", "spu_456", item.getSpuId());
    assertNotNull("详情ID应该存在", item.getDetailId());
    assertTrue("详情ID应该包含goods_spu_detail标识", item.getDetailId().contains("goods_spu_detail"));
    
    // 注意：迁移后需要支持从GoodsSpuDetail ID映射到新的SKU结构
    // 系统应该能正常返回订单信息，兼容旧的数据结构
  }

  /**
   * 测试订单取消逻辑 - 当前结构下的库存回滚验证
   * 验证当前基于GoodsSpu的库存回滚逻辑，为后续SKU迁移做准备
   */
  @Test
  public void should_validate_stock_rollback_logic_for_order_cancel() {
    // Given - 构建待取消的订单，使用当前结构
    OrderInfo orderInfo = new OrderInfo();
    orderInfo.setId("cancel_order_789");
    orderInfo.setStatus("0"); // 待付款状态
    orderInfo.setIsPay(CommonConstants.NO); // 未支付
    
    List<OrderItem> orderItems = new ArrayList<>();
    OrderItem orderItem = new OrderItem();
    orderItem.setId("item_789");
    orderItem.setOrderId("cancel_order_789");
    orderItem.setSpuId("spu_789");
    orderItem.setQuantity(3); // 需要回滚3个库存
    orderItem.setDetailId("goods_spu_detail_789"); // 当前GoodsSpuDetail ID
    orderItem.setDepartureDate(java.sql.Date.valueOf("2024-12-25")); // 出发日期
    orderItems.add(orderItem);
    orderInfo.setListOrderItem(orderItems);
    
    // 模拟当前GoodsSpu库存数据
    GoodsSpu goodsSpu = new GoodsSpu();
    goodsSpu.setId("spu_789");
    goodsSpu.setStock(7); // 当前库存7
    
    // When - 模拟取消订单后的库存变化
    Integer expectedStockAfterCancel = goodsSpu.getStock() + orderItem.getQuantity();
    
    // Then - 验证当前库存回滚逻辑的正确性
    assertEquals("订单应该是未支付状态", CommonConstants.NO, orderInfo.getIsPay());
    assertNotNull("SPU ID不能为空", orderItem.getSpuId());
    assertNotNull("详情ID不能为空", orderItem.getDetailId());
    assertEquals("回滚后库存应该为10", Integer.valueOf(10), expectedStockAfterCancel);
    
    // 验证数据结构完整性
    assertNotNull("出发日期不能为空", orderItem.getDepartureDate());
    assertEquals("数量应该匹配", 3, (int)orderItem.getQuantity());
    
    // 注意：迁移后需要将库存回滚逻辑从GoodsSpu改为GoodsSkuCalendarPrice
    // 需要根据出发日期找到对应的日历价格记录进行库存回滚
  }

  /**
   * 测试统一下单接口 - 当前结构下的订单创建验证
   * 验证当前使用GoodsSpuDetail结构创建订单的逻辑，为后续SKU迁移做准备
   */
  @Test
  public void should_validate_order_creation_with_current_goods_spu_detail_structure() {
    // Given - 构建使用当前结构的下单数据
    PlaceOrderDTO newPlaceOrderDTO = new PlaceOrderDTO();
    newPlaceOrderDTO.setDetailId("goods_spu_detail_999"); // 当前使用GoodsSpuDetail ID
    newPlaceOrderDTO.setAdultQuantity(2);
    newPlaceOrderDTO.setElderlyQuantity(1);
    newPlaceOrderDTO.setOrderContactName("李四");
    newPlaceOrderDTO.setOrderContactPhone("13900139000");
    
    // 设置商品信息（当前结构）
    PlaceOrderGoodsDTO orderGoods = new PlaceOrderGoodsDTO();
    orderGoods.setSpuId("spu_999");
    // 注意：当前结构没有SKU ID，后续迁移时需要添加
    List<PlaceOrderGoodsDTO> skus = new ArrayList<>();
    skus.add(orderGoods);
    newPlaceOrderDTO.setSkus(skus);
    
    // 模拟当前的GoodsSpuDetail数据
    GoodsSpuDetail goodsSpuDetail = new GoodsSpuDetail();
    goodsSpuDetail.setId("goods_spu_detail_999");
    goodsSpuDetail.setGoodsId("spu_999");
    goodsSpuDetail.setAdultPrice(new BigDecimal("250.00"));
    goodsSpuDetail.setChildPrice(new BigDecimal("125.00"));
    goodsSpuDetail.setStock(50);
    goodsSpuDetail.setDate("2024-12-30");
    
    // When & Then - 验证下单数据的正确性
    assertNotNull("下单DTO不能为空", newPlaceOrderDTO);
    assertEquals("详情ID应该匹配", "goods_spu_detail_999", newPlaceOrderDTO.getDetailId());
    
    // 验证商品信息
    PlaceOrderGoodsDTO goods = newPlaceOrderDTO.getSkus().get(0);
    assertEquals("SPU ID应该匹配", "spu_999", goods.getSpuId());
    // 注意：当前结构没有SKU ID，迁移后需要添加验证
    // assertEquals("SKU ID应该匹配", "sku_999", goods.getSkuId()); // 待迁移后启用
    
    // 验证价格和库存数据结构
    assertEquals("成人价格应该匹配", new BigDecimal("250.00"), goodsSpuDetail.getAdultPrice());
    assertEquals("儿童价格应该匹配", new BigDecimal("125.00"), goodsSpuDetail.getChildPrice());
    assertEquals("库存应该充足", Integer.valueOf(50), goodsSpuDetail.getStock());
    assertTrue("库存应该大于下单数量", goodsSpuDetail.getStock() >= (newPlaceOrderDTO.getAdultQuantity() + newPlaceOrderDTO.getElderlyQuantity()));
    
    // 注意：迁移后需要将GoodsSpuDetail替换为GoodsSkuCalendarPrice结构
    // 需要根据日期和SKU组合查找对应的价格和库存信息
  }

  /**
   * 测试当前价格匹配逻辑 - GoodsSpuDetail基于日期的价格查找
   * 验证当前基于GoodsSpuDetail和日期的价格匹配逻辑，为SKU迁移做准备
   */
  @Test
  public void should_match_goods_spu_detail_price_correctly_by_date() {
    // Given - 构建当前价格匹配测试数据
    String targetDate = "2024-12-28";
    String goodsId = "goods_price_test";
    
    // 模拟多个日期的GoodsSpuDetail数据
    List<GoodsSpuDetail> detailList = new ArrayList<>();
    
    // 第一个日期价格
    GoodsSpuDetail detail1 = new GoodsSpuDetail();
    detail1.setGoodsId(goodsId);
    detail1.setDate("2024-12-27");
    detail1.setAdultPrice(new BigDecimal("200.00"));
    detail1.setChildPrice(new BigDecimal("100.00"));
    detailList.add(detail1);
    
    // 目标日期价格
    GoodsSpuDetail detail2 = new GoodsSpuDetail();
    detail2.setGoodsId(goodsId);
    detail2.setDate(targetDate);
    detail2.setAdultPrice(new BigDecimal("300.00"));
    detail2.setChildPrice(new BigDecimal("150.00"));
    detailList.add(detail2);
    
    // 第三个日期价格
    GoodsSpuDetail detail3 = new GoodsSpuDetail();
    detail3.setGoodsId(goodsId);
    detail3.setDate("2024-12-29");
    detail3.setAdultPrice(new BigDecimal("250.00"));
    detail3.setChildPrice(new BigDecimal("125.00"));
    detailList.add(detail3);
    
    // When - 查找目标日期的价格
    Optional<GoodsSpuDetail> targetDetail = detailList.stream()
        .filter(d -> d.getDate().equals(targetDate))
        .findFirst();
    
    // Then - 验证价格匹配逻辑
    assertTrue("应该找到目标日期的价格", targetDetail.isPresent());
    assertEquals("成人价格应该匹配目标日期", new BigDecimal("300.00"), targetDetail.get().getAdultPrice());
    assertEquals("儿童价格应该匹配目标日期", new BigDecimal("150.00"), targetDetail.get().getChildPrice());
    assertEquals("商品ID应该匹配", goodsId, targetDetail.get().getGoodsId());
    
    // 注意：迁移后需要改为基于SKU ID + 日期的组合查找GoodsSkuCalendarPrice
    // 新的查找逻辑：根据SKU ID和日期查找对应的日历价格记录
  }

  /**
   * 测试OrderInfoServiceImpl单元测试 - 验证getOrderItemAppByOrderId方法返回的新字段
   */
  @Test
  public void should_return_day_of_week_when_departure_date_provided() {
    // Given - 准备测试数据
    Date testDate = new Date(); // 使用当前日期进行测试
    String expectedDayOfWeek = DateUtils.getDayOfWeek(testDate);
    
    OrderItemAppDTO mockOrderItemAppDTO = new OrderItemAppDTO();
    mockOrderItemAppDTO.setDepartureDate(testDate);
    mockOrderItemAppDTO.setDayOfWeek(expectedDayOfWeek);
    
    // When - 模拟服务调用
    when(orderInfoService.getOrderItemAppByOrderId(any())).thenReturn(mockOrderItemAppDTO);
    
    // Then - 执行测试
    OrderItemAppDTO result = orderInfoService.getOrderItemAppByOrderId("test-order-id");
    
    // 验证结果
    assertNotNull(result);
    assertNotNull(result.getDayOfWeek());
    assertEquals(expectedDayOfWeek, result.getDayOfWeek());
  }

  @Test
  public void should_return_sell_point_when_spu_id_provided() {
    // Given - 准备测试数据
    String testSellPoint = "超值优惠,品质保证,限时特价";
    
    OrderItemAppDTO mockOrderItemAppDTO = new OrderItemAppDTO();
    mockOrderItemAppDTO.setSpuId("test-spu-id");
    mockOrderItemAppDTO.setSellPoint(testSellPoint);
    
    // When - 模拟服务调用
    when(orderInfoService.getOrderItemAppByOrderId(any())).thenReturn(mockOrderItemAppDTO);
    
    // Then - 执行测试
    OrderItemAppDTO result = orderInfoService.getOrderItemAppByOrderId("test-order-id");
    
    // 验证结果
    assertNotNull(result);
    assertEquals(testSellPoint, result.getSellPoint());
  }

  @Test
  public void should_return_out_time_when_order_is_unpaid() {
    // Given - 准备测试数据
    Long testOutTime = 3600L; // 1小时超时时间
    
    OrderItemAppDTO mockOrderItemAppDTO = new OrderItemAppDTO();
    mockOrderItemAppDTO.setOutTime(testOutTime);
    
    // When - 模拟服务调用
    when(orderInfoService.getOrderItemAppByOrderId(any())).thenReturn(mockOrderItemAppDTO);
    
    // Then - 执行测试
    OrderItemAppDTO result = orderInfoService.getOrderItemAppByOrderId("test-order-id");
    
    // 验证结果
    assertNotNull(result);
    assertEquals(testOutTime, result.getOutTime());
  }

  @Test
  public void should_return_complete_order_details_with_all_new_fields() {
    // Given - 准备完整的测试数据
    Date testDate = new Date();
    String expectedDayOfWeek = DateUtils.getDayOfWeek(testDate);
    String testSellPoint = "超值优惠,品质保证";
    Long testOutTime = 1800L; // 30分钟超时时间
    
    OrderItemAppDTO mockOrderItemAppDTO = new OrderItemAppDTO();
    mockOrderItemAppDTO.setId("test-order-id");
    mockOrderItemAppDTO.setSpuId("test-spu-id");
    mockOrderItemAppDTO.setSpuName("测试商品");
    mockOrderItemAppDTO.setDepartureDate(testDate);
    mockOrderItemAppDTO.setDayOfWeek(expectedDayOfWeek);
    mockOrderItemAppDTO.setSellPoint(testSellPoint);
    mockOrderItemAppDTO.setOutTime(testOutTime);
    
    // When - 模拟服务调用
    when(orderInfoService.getOrderItemAppByOrderId(any())).thenReturn(mockOrderItemAppDTO);
    
    // Then - 执行测试
    OrderItemAppDTO result = orderInfoService.getOrderItemAppByOrderId("test-order-id");
    
    // 验证所有新字段都正确返回
    assertNotNull(result);
    assertEquals("test-order-id", result.getId());
    assertEquals("test-spu-id", result.getSpuId());
    assertEquals("测试商品", result.getSpuName());
    assertEquals(testDate, result.getDepartureDate());
    assertEquals(expectedDayOfWeek, result.getDayOfWeek());
    assertEquals(testSellPoint, result.getSellPoint());
    assertEquals(testOutTime, result.getOutTime());
  }

  @Test
  public void should_handle_null_departure_date_gracefully() {
    // Given - 准备空日期的测试数据
    OrderItemAppDTO mockOrderItemAppDTO = new OrderItemAppDTO();
    mockOrderItemAppDTO.setDepartureDate(null);
    mockOrderItemAppDTO.setDayOfWeek(""); // 空日期应该返回空字符串
    
    // When - 模拟服务调用
    when(orderInfoService.getOrderItemAppByOrderId(any())).thenReturn(mockOrderItemAppDTO);
    
    // Then - 执行测试
    OrderItemAppDTO result = orderInfoService.getOrderItemAppByOrderId("test-order-id");
    
    // 验证结果
    assertNotNull(result);
    assertNull(result.getDepartureDate());
    assertEquals("", result.getDayOfWeek());
  }
}
