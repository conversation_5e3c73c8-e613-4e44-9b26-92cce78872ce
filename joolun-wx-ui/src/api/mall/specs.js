import request from '@/utils/request'

// 查询商品规格列表
export function listSpecs(query) {
  return request({
    url: '/mall/specs/list',
    method: 'get',
    params: query
  })
}

// 查询商品规格详细
export function getSpecs(id) {
  return request({
    url: '/mall/specs/' + id,
    method: 'get'
  })
}

// 新增商品规格
export function addSpecs(data) {
  return request({
    url: '/mall/specs',
    method: 'post',
    data: data
  })
}

// 修改商品规格
export function updateSpecs(data) {
  return request({
    url: '/mall/specs',
    method: 'put',
    data: data
  })
}

// 删除商品规格
export function delSpecs(id) {
  return request({
    url: '/mall/specs/' + id,
    method: 'delete'
  })
}

// 导出商品规格
export function exportSpecs(query) {
  return request({
    url: '/mall/specs/export',
    method: 'get',
    params: query
  })
}