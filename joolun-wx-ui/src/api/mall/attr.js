import request from '@/utils/request'

// 查询商品规格名列表
export function listAttr(query) {
  return request({
    url: '/mall/attr/list',
    method: 'get',
    params: query
  })
}

// 查询商品规格名详细
export function getAttr(id) {
  return request({
    url: '/mall/attr/' + id,
    method: 'get'
  })
}

// 新增商品规格名
export function addAttr(data) {
  return request({
    url: '/mall/attr',
    method: 'post',
    data: data
  })
}

// 修改商品规格名
export function updateAttr(data) {
  return request({
    url: '/mall/attr',
    method: 'put',
    data: data
  })
}

// 删除商品规格名
export function delAttr(id) {
  return request({
    url: '/mall/attr/' + id,
    method: 'delete'
  })
}

// 导出商品规格名
export function exportAttr(query) {
  return request({
    url: '/mall/attr/export',
    method: 'get',
    params: query
  })
}

// 批量更新规格排序
export function updateSort(data) {
  return request({
    url: '/mall/attr/sort',
    method: 'post',
    data
  })
}