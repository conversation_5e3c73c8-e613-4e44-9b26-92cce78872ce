import request from '@/utils/request'

// 查询city列表
export function listCity(query) {
  return request({
    url: '/city/list',
    method: 'get',
    params: query
  })
}

// 查询city详细
export function getCity(id) {
  return request({
    url: '/city/' + id,
    method: 'get'
  })
}

// 新增city
export function addCity(data) {
  return request({
    url: '/city',
    method: 'post',
    data: data
  })
}

// 修改city
export function updateCity(data) {
  return request({
    url: '/city',
    method: 'put',
    data: data
  })
}

// 删除city
export function delCity(id) {
  return request({
    url: '/city/' + id,
    method: 'delete'
  })
}

// 导出city
export function exportCity(query) {
  return request({
    url: '/city/export',
    method: 'get',
    params: query
  })
}

// 设置热门城市
export function setHotCities(data) {
  return request({
    url: '/city/setHotCities',
    method: 'post',
    data: data
  })
}

// 获取热门城市列表
export function getHotCities() {
  return request({
    url: '/city/hotCities',
    method: 'get'
  })
}
