<template>
  <el-drawer
    title="订单详情"
    :visible.sync="visible"
    direction="rtl"
    size="60%"
    :with-header="false"
    custom-class="orderinfo-drawer"
    @close="handleClose"
  >
    <div class="order-detail-container">
      <!-- Header -->
      <div class="detail-header">
        <div class="header-title">
          <h1>订单详情</h1>
          <el-tag type="info" size="medium">
            订单号: {{ orderData.orderNo }}
          </el-tag>
        </div>
        <el-button
          type="text"
          icon="el-icon-close"
          size="large"
          @click="handleClose"
          class="close-btn"
        />
      </div>

      <!-- 订单状态总览 -->
      <el-card class="status-overview-card" shadow="never">
        <div slot="header" class="card-header">
          <i class="el-icon-s-order card-icon"></i>
          <span class="card-title">订单状态</span>
        </div>
        <div class="status-grid">
          <div class="status-item">
            <span class="status-label">订单编号</span>
            <span class="status-value">{{ orderData.orderNo }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">订单状态</span>
            <el-tag
              :type="getStatusTagType(orderData.status)"
              effect="dark"
            >
              {{ orderData.statusDesc }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">支付状态</span>
            <el-tag
              :type="orderData.isPay=='1' ? 'success' : 'danger'"
              effect="dark"
            >
              {{ orderData.isPay == '1' ? '已支付' : '未支付' }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">支付金额</span>
            <span class="status-value amount">￥{{ orderData.paymentPrice }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">创建时间</span>
            <span class="status-value">{{ orderData.createTime }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">付款时间</span>
            <span class="status-value">{{ orderData.paymentTime || '-' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">支付流水号</span>
            <span class="status-value">{{ orderData.transactionId || '-' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">买家留言</span>
            <span class="status-value">{{ orderData.userMessage || '-' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 商品信息 -->
      <el-card class="product-info-card" shadow="never">
        <div slot="header" class="card-header">
          <i class="el-icon-goods card-icon"></i>
          <span class="card-title">商品信息</span>
        </div>
        <div class="product-list">
          <div
            v-for="(item, index) in orderData.listOrderItem"
            :key="index"
            class="product-item"
          >
            <div class="product-image">
              <img :src="item.picUrl" alt="商品图片" />
            </div>
            <div class="product-details">
              <h4 class="product-name">{{ item.spuName }}</h4>
              <div class="product-specs">
                <div class="spec-item">
                  <span class="spec-label">商品单价</span>
                  <span class="spec-value">￥{{ item.salesPrice }}</span>
                </div>
                <div class="spec-item">
                  <span class="spec-label">数量</span>
                  <span class="spec-value">{{ item.quantity }}人</span>
                </div>
                <div class="spec-item">
                  <span class="spec-label">规格</span>
                  <span class="spec-value">{{ item.specsName || '-' }}</span>
                </div>
                <div class="spec-item">
                  <span class="spec-label">出行日期</span>
                  <span class="spec-value">{{ item.departureDate || '-' }}</span>
                </div>
                <div class="spec-item">
                  <span class="spec-label">支付金额</span>
                  <span class="spec-value amount">￥{{ item.paymentPrice }}</span>
                </div>
                <div class="spec-item">
                  <span class="spec-label">备注/拒绝原因</span>
                  <span class="spec-value">{{ item.remark || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 出行人信息 -->
      <el-card
        v-if="orderData.orderTravelersList && orderData.orderTravelersList.length"
        class="travelers-info-card"
        shadow="never"
      >
        <div slot="header" class="card-header">
          <i class="el-icon-user-solid card-icon"></i>
          <span class="card-title">出行人信息</span>
        </div>
        <div class="travelers-list">
          <div
            v-for="(traveler, index) in orderData.orderTravelersList"
            :key="index"
            class="traveler-item"
          >
            <div class="traveler-details">
              <div class="traveler-field">
                <i class="el-icon-user field-icon"></i>
                <span class="field-label">姓名</span>
                <span class="field-value">{{ traveler.name }}</span>
              </div>
              <div class="traveler-field">
                <span class="field-label">证件类型</span>
                <span class="field-value">
                  {{ getCertTypeName(traveler.certType) }}
                </span>
              </div>
              <div class="traveler-field">
                <span class="field-label">证件号</span>
                <span class="field-value">{{ traveler.certId }}</span>
              </div>
              <div class="traveler-field">
                <i class="el-icon-phone field-icon"></i>
                <span class="field-label">联系方式</span>
                <span class="field-value">{{ traveler.phone }}</span>
              </div>
              <div class="traveler-field">
                <span class="field-label">性别</span>
                <span class="field-value">{{ getSexName(traveler.sex) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 收货人信息 -->
      <el-card
        v-if="orderData.orderLogistics"
        class="logistics-info-card"
        shadow="never"
      >
        <div slot="header" class="card-header">
          <i class="el-icon-location-outline card-icon"></i>
          <span class="card-title">收货人信息</span>
        </div>
        <div class="logistics-item">
          <div class="logistics-details">
            <div class="logistics-field">
              <span class="field-label">收货人</span>
              <span class="field-value">{{ orderData.orderLogistics.userName }}</span>
            </div>
            <div class="logistics-field">
              <i class="el-icon-phone field-icon"></i>
              <span class="field-label">联系电话</span>
              <span class="field-value">{{ orderData.orderLogistics.telNum }}</span>
            </div>
            <div class="logistics-field">
              <span class="field-label">收货地址</span>
              <span class="field-value">{{ orderData.orderLogistics.address }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 订单联系人 -->
      <el-card
        v-if="orderData.userInfo"
        class="contact-info-card"
        shadow="never"
      >
        <div slot="header" class="card-header">
          <i class="el-icon-user card-icon"></i>
          <span class="card-title">订单联系人</span>
        </div>
        <div class="contact-item">
          <div class="contact-details">
            <div class="contact-field">
              <span class="field-label">联系人</span>
              <span class="field-value">{{ orderData.userInfo.nickName }}</span>
            </div>
            <div class="contact-field">
              <i class="el-icon-phone field-icon"></i>
              <span class="field-label">联系电话</span>
              <span class="field-value">{{ orderData.userInfo.phone }}</span>
            </div>
            <div class="contact-field">
              <span class="field-label">用户ID</span>
              <span class="field-value">{{ orderData.userInfo.id }}</span>
            </div>
            <div class="contact-field">
              <i class="el-icon-time field-icon"></i>
              <span class="field-label">注册时间</span>
              <span class="field-value">{{ orderData.userInfo.createTime }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <!-- <div class="action-buttons">
        <el-button type="primary" icon="el-icon-printer" @click="handlePrint">
          打印订单
        </el-button>
        <el-button icon="el-icon-download" @click="handleExport">
          导出详情
        </el-button>
        <el-button icon="el-icon-service" @click="handleContact">
          联系客服
        </el-button>
      </div> -->
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'OrderDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderData: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    getStatusTagType(status) {
      // 根据订单状态返回对应的标签类型
      switch (status) {
        case '0': // 待付款
          return 'info'
        case '2': // 待使用
        case '3': // 已完成
          return 'success'
        case '4': // 退款中
          return 'warning'
        case '5': // 已取消
        case '8': // 拒绝退款
          return 'danger'
        case '6': // 已退款
        case '7': // 同意退款
          return 'success'
        default:
          return 'info'
      }
    },
    getCertTypeName(certType) {
      const certTypeMap = {
        '1': '身份证',
        '2': '护照',
        '3': '港澳通行证'
      }
      return certTypeMap[certType] || '未知'
    },
    getSexName(sex) {
      const sexMap = {
        '0': '男',
        '1': '女'
      }
      return sexMap[sex] || '未知'
    },
    handlePrint() {
      // 打印订单逻辑
      this.$emit('print', this.orderData)
    },
    handleExport() {
      // 导出详情逻辑
      this.$emit('export', this.orderData)
    },
    handleContact() {
      // 联系客服逻辑
      this.$emit('contact', this.orderData)
    }
  }
}
</script>

<style lang="scss" scoped>
.orderinfo-drawer {
  .el-drawer__body {
    padding: 0;
    overflow-y: auto;
    height: calc(100vh - 60px);
    background: #f5f7fa;
  }
}

.order-detail-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header 样式 */
.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .header-title {
    display: flex;
    align-items: center;
    gap: 16px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .close-btn {
    font-size: 20px;
    color: #6b7280;

    &:hover {
      color: #374151;
    }
  }
}

/* 卡片通用样式 */
.el-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .el-card__header {
    padding: 20px 24px;
    border-bottom: 1px solid #f3f4f6;
    background: #fafbfc;
  }

  .el-card__body {
    padding: 24px;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;

  .card-icon {
    font-size: 20px;
    color: #3b82f6;
  }

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
  }
}

/* 订单状态总览 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .status-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
  }

  .status-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;

    &.amount {
      color: #dc2626;
      font-size: 16px;
    }
  }

  .el-tag {
    align-self: flex-start;
  }
}

/* 商品信息 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;

  .product-image {
    flex-shrink: 0;

    img {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      object-fit: cover;
      border: 1px solid #e5e7eb;
    }
  }

  .product-details {
    flex: 1;

    .product-name {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
  }
}

.product-specs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

.spec-item {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .spec-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
  }

  .spec-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;

    &.amount {
      color: #16a34a;
    }
  }
}

/* 出行人信息 */
.travelers-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.traveler-item {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.traveler-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.traveler-field {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .field-icon {
    font-size: 14px;
    color: #3b82f6;
    margin-right: 4px;
  }

  .field-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  .field-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
  }
}

/* 物流信息 */
.logistics-item {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.logistics-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.logistics-field {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .field-icon {
    font-size: 14px;
    color: #3b82f6;
    margin-right: 4px;
  }

  .field-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  .field-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
  }
}

/* 联系人信息 */
.contact-item {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.contact-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.contact-field {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .field-icon {
    font-size: 14px;
    color: #3b82f6;
    margin-right: 4px;
  }

  .field-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  .field-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
  }
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 24px;
  margin-top: 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .el-button {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
