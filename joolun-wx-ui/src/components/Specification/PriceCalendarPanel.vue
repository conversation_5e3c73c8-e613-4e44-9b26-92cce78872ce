<template>
  <div class="calendar-container">
    <!-- 日历导航 -->
    <div class="calendar-header">
      <el-button icon="el-icon-arrow-left" circle @click="previousWeek" />
      <span class="week-range">{{ weekStartDate }} - {{ weekEndDate }}</span>
      <el-button icon="el-icon-arrow-right" circle @click="nextWeek" />
    </div>

    <!-- 价格日历表格 -->
    <div class="calendar-body">
      <table class="price-calendar-table">
        <thead>
          <tr>
            <template v-if="modelType === 'multiple'">
              <th v-for="(category, index) in filteredCategories" :key="'header-'+index">
                {{ category.name }}
              </th>
            </template>
            <template v-else>
              <th>默认规格</th>
            </template>
            <th v-for="day in weekDays" :key="day.date">
              {{ formatDayHeader(day) }}
            </th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="!hasSelectedSpecs && modelType === 'multiple'">
            <td :colspan="filteredCategories.length + weekDays.length + 1" class="no-data">请先选择规格值</td>
          </tr>
          <template v-else>
            <tr v-for="(row, rowIndex) in tableRows" :key="rowIndex">
              <template v-if="modelType === 'multiple'">
                <template v-for="(value, valueIndex) in row.values">
                  <td
                    v-if="!shouldMergeCell(rowIndex, valueIndex)"
                    :key="'cell-'+valueIndex"
                    :rowspan="getRowspan(rowIndex, valueIndex)"
                  >
                    {{ value.name }}
                  </td>
                </template>
              </template>
              <template v-else>
                <td>默认规格</td>
              </template>
              <td
                v-for="day in weekDays"
                :key="day.date"
                :class="['price-cell', {
                  'is-disabled': day.isDisabled,
                  'is-today': day.isToday
                }]"
                @click="handlePriceCellClick(day, modelType === 'unified' ? [] : row.values)"
              >
                <div v-if="getPriceInfo(day, modelType === 'unified' ? [] : row.values)" class="price-info">
                  <div class="price">¥{{ getPriceInfo(day, modelType === 'unified' ? [] : row.values).adultPrice }}</div>
                  <div class="stock">库存:{{ getPriceInfo(day, modelType === 'unified' ? [] : row.values).stock }}</div>
                </div>
                <div v-else class="price-placeholder">
                  <i class="el-icon-edit edit-icon" />
                </div>
              </td>
              <td>
                <el-button type="text" @click="handleBatchSet(modelType === 'unified' ? [] : row.values)">批量设置</el-button>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <!-- 批量设置对话框 -->
    <el-dialog
      title="批量设置"
      :visible.sync="dialogBatchSet"
      width="500px"
      modal-append-to-body
      append-to-body
    >
      <el-form :model="batchSetForm" label-width="100px">
        <el-form-item label="日期" required>
          <el-date-picker
            v-model="batchSetForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="适用" required>
          <div class="weekday-select">
            <el-checkbox v-model="batchSetForm.weekdays" :label="1">周一</el-checkbox>
            <el-checkbox v-model="batchSetForm.weekdays" :label="2">周二</el-checkbox>
            <el-checkbox v-model="batchSetForm.weekdays" :label="3">周三</el-checkbox>
            <el-checkbox v-model="batchSetForm.weekdays" :label="4">周四</el-checkbox>
            <el-checkbox v-model="batchSetForm.weekdays" :label="5">周五</el-checkbox>
            <el-checkbox v-model="batchSetForm.weekdays" :label="6">周六</el-checkbox>
            <el-checkbox v-model="batchSetForm.weekdays" :label="0">周日</el-checkbox>
          </div>
          <div class="quick-select">
            <el-button type="text" @click="selectAllWeekdays">全选</el-button>
            <el-button type="text" @click="selectWorkdays">工作日</el-button>
            <el-button type="text" @click="selectWeekends">周末</el-button>
          </div>
        </el-form-item>
        <el-form-item label="状态" required>
          <el-radio-group v-model="batchSetForm.status">
            <el-radio label="1">在售</el-radio>
            <el-radio label="0">停售</el-radio>
            <el-radio label="2">保持现状</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="成人价格">
          <el-input-number
            v-model="batchSetForm.adultPrice"
            :min="0"
            :precision="2"
            placeholder="成人价格"
          />
          <span class="unit">元</span>
        </el-form-item>
        <el-form-item label="儿童价格">
          <el-input-number
            v-model="batchSetForm.childPrice"
            :min="0"
            :precision="2"
            placeholder="儿童价格"
          />
          <span class="unit">元</span>
        </el-form-item>
        <el-form-item label="佣金比例">
          <el-input-number
            v-model="batchSetForm.commission"
            :min="0"
            :max="100"
            :precision="2"
            placeholder="佣金比例"
          />
          <span class="unit">%</span>
        </el-form-item>
        <el-form-item label="库存">
          <el-input-number
            v-model="batchSetForm.stock"
            :min="0"
            placeholder="库存数量"
          />
          <span class="unit">件</span>
        </el-form-item>
        <el-form-item label="套餐名称">
          <el-input
            v-model="batchSetForm.skuName"
            placeholder="请输入套餐名称"
            maxlength="50"
            show-word-limit
          />
          <div class="hint">用于小程序端显示套餐名称</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogBatchSet = false">取 消</el-button>
        <el-button type="primary" @click="saveBatchSet">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 价格设置对话框 -->
    <el-dialog
      :title="'设置价格 - ' + (selectedSpecInfo ? selectedSpecInfo.category1 + (selectedSpecInfo.values ? ' - ' + selectedSpecInfo.values.map(v => v.value).join(' - ') : '') : '')"
      :visible.sync="priceDialogVisible"
      width="500px"
      :append-to-body="true"
      custom-class="price-dialog"
    >
      <el-form :model="priceForm" label-width="100px">
        <el-form-item label="日期">
          <span>{{ selectedDate }}</span>
        </el-form-item>
        <el-form-item label="成人价格">
          <el-input-number v-model="priceForm.adultPrice" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="儿童价格">
          <el-input-number v-model="priceForm.childPrice" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="库存">
          <el-input-number v-model="priceForm.stock" :min="0" />
        </el-form-item>
        <el-form-item label="佣金比例">
          <el-input-number v-model="priceForm.commission" :min="0" :max="100" :precision="2" />
        </el-form-item>
        <el-form-item label="套餐名称">
          <el-input
            v-model="priceForm.skuName"
            placeholder="请输入套餐名称"
            maxlength="50"
            show-word-limit
            @blur="validateSkuName"
          />
          <div class="hint">用于小程序端显示套餐名称</div>
          <div v-if="skuNameError" class="error-text">{{ skuNameError }}</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="priceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="savePriceInfo">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PriceCalendarPanel',
  props: {
    modelType: {
      type: String,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    userSelectSpecValues: {
      type: Object,
      default: () => ({})
    },
    selectedSpecOrder: {
      type: Array,
      default: () => []
    },
    localPriceData: {
      type: Object,
      default: () => ({})
    },
    calendarData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 日历相关数据
      currentDate: new Date(),
      weekDays: [],
      weekStartDate: '',
      weekEndDate: '',
      selectedDate: '',
      // 批量设置相关数据
      dialogBatchSet: false,
      batchSetForm: {
        dateRange: [],
        weekdays: [],
        status: 1,
        adultPrice: 0,
        childPrice: 0,
        commission: 0,
        stock: 0,
        skuName: ''
      },
      // 价格设置相关数据
      priceDialogVisible: false,
      priceForm: {
        adultPrice: 0,
        childPrice: 0,
        stock: 0,
        commission: 0,
        skuName: ''
      },
      selectedSpecInfo: null,
      skuNameError: '',
      // 临时存储数据
      tempPriceData: {},
      tempLocalPriceData: {}
    }
  },
  computed: {
    hasSelectedSpecs() {
      return Object.values(this.userSelectSpecValues).some(values => values && values.length > 0)
    },
    filteredCategories() {
      return this.selectedSpecOrder
        .filter(name => {
          const values = this.userSelectSpecValues[name]
          return values && values.length > 0
        })
        .map(name => {
          return {
            name: name,
            values: this.userSelectSpecValues[name]
          }
        })
        .filter(Boolean)
    },
    tableRows() {
      if (this.modelType === 'unified') {
        return [{
          id: 0,
          values: [{
            category: {
              name: '默认规格'
            },
            value: {
              id: 'default_spec',
              name: '默认规格'
            },
            name: '默认规格'
          }]
        }]
      }
      if (!this.hasSelectedSpecs) return []
      const rows = []
      const categories = this.filteredCategories
      if (categories.length === 0) return []
      const generateCombinations = (arrays) => {
        if (arrays.length === 0) return [[]]
        const result = []
        const restCombinations = generateCombinations(arrays.slice(1))
        arrays[0].forEach(item => {
          restCombinations.forEach(combination => {
            result.push([item, ...combination])
          })
        })
        return result
      }
      const specArrays = categories.map(category => {
        return category.values.map(value => ({
          category: category,
          value: value,
          name: value.name,
          id: value.id
        }))
      })
      const combinations = generateCombinations(specArrays)
      combinations.forEach((combination, comboIndex) => {
        rows.push({
          id: comboIndex,
          values: combination
        })
      })
      return rows
    }
  },
  watch: {
    localPriceData: {
      handler(newVal) {
        this.tempLocalPriceData = { ...newVal }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.initCalendar()
  },
  methods: {
    // 初始化日历
    initCalendar() {
      this.updateWeekDays()
    },

    // 更新周天数
    updateWeekDays() {
      const current = new Date(this.currentDate)
      const weekDay = current.getDay() || 7
      const diff = current.getDate() - weekDay + 1

      const weekStart = new Date(current.setDate(diff))
      const weekEnd = new Date(current.setDate(diff + 6))

      this.weekStartDate = this.formatDate(weekStart)
      this.weekEndDate = this.formatDate(weekEnd)

      this.weekDays = []
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      for (let i = 0; i < 7; i++) {
        const date = new Date(weekStart)
        date.setDate(date.getDate() + i)
        date.setHours(0, 0, 0, 0)

        this.weekDays.push({
          date: this.formatDate(date),
          weekDay: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i],
          day: date.getDate(),
          isToday: this.isToday(date),
          isSelected: false,
          isDisabled: date < today
        })
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 判断是否为今天
    isToday(date) {
      const today = new Date()
      return date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    },

    // 上一周
    previousWeek() {
      this.currentDate.setDate(this.currentDate.getDate() - 7)
      this.updateWeekDays()
    },

    // 下一周
    nextWeek() {
      this.currentDate.setDate(this.currentDate.getDate() + 7)
      this.updateWeekDays()
    },

    // 格式化日期头部
    formatDayHeader(day) {
      const date = new Date(day.date)
      const month = date.getMonth() + 1
      const dayOfMonth = date.getDate()
      return `${month}-${dayOfMonth} ${day.weekDay}`
    },

    // 获取价格信息
    getPriceInfo(day, values) {
      const priceKey = this.modelType === 'unified'
        ? `unified_${day.date}`
        : values.length > 0
          ? values.map(v => v.id).sort().join('-') + '_' + day.date
          : `unified_${day.date}`

      const priceInfo = this.tempLocalPriceData[priceKey] || this.localPriceData[priceKey]
      // 添加调试日志
      if (day.date === this.formatDate(new Date())) {
        console.log('获取价格信息:', JSON.stringify({
          priceKey,
          priceInfo,
          tempLocalPriceData: this.tempLocalPriceData,
          localPriceData: this.localPriceData,
          values: values.map(v => ({ id: v.id, name: v.name }))
        }, null, 2))
      }
      return priceInfo
    },

    // 处理价格单元格点击
    handlePriceCellClick(day, values) {
      if (day.isDisabled) {
        this.$message.warning('过去的日期不可编辑')
        return
      }
      this.selectedDate = day.date
      const existingPrice = this.getPriceInfo(day, values)

      // 获取当前SKU的名称
      const currentSkuName = this.getCurrentSkuName(values)
      this.priceForm = {
        adultPrice: existingPrice ? existingPrice.adultPrice : 0,
        childPrice: existingPrice ? existingPrice.childPrice : 0,
        stock: existingPrice ? existingPrice.stock : 0,
        commission: existingPrice ? existingPrice.commission : 0,
        skuName: currentSkuName || this.generateDefaultSkuName(),
        ...(this.modelType === 'unified' ? {} : values.reduce((acc, value) => {
          acc[value.category.name] = value.name
          return acc
        }, {}))
      }
      this.selectedSpecInfo = {
        category1: this.modelType === 'unified' ? '统一规格' : values[0].category.name,
        values: this.modelType === 'unified' ? [] : values.map(value => ({
          id: value.value.id,
          category: value.category.name,
          value: value.name
        }))
      }
      this.skuNameError = ''
      this.priceDialogVisible = true
    },

    // 保存价格信息
    savePriceInfo() {
      if (!this.selectedDate) {
        this.$message.error('请选择日期')
        return
      }

      const priceKey = this.modelType === 'unified'
        ? `unified_${this.selectedDate}`
        : this.selectedSpecInfo.values.length > 0
          ? this.selectedSpecInfo.values.map(v => v.id).sort().join('-') + '_' + this.selectedDate
          : `unified_${this.selectedDate}`

      // 调试日志：检查SKU ID获取情况
      const currentSkuId = this.getCurrentSkuId()
      console.log('保存价格信息 - SKU ID获取情况:', {
        modelType: this.modelType,
        currentSkuId: currentSkuId,
        calendarData: this.calendarData,
        form: this.form,
        priceKey: priceKey
      })

      // 将价格信息存储到临时对象中
      this.tempPriceData[priceKey] = {
        calendarDate: this.selectedDate,
        adultPrice: this.priceForm.adultPrice,
        childPrice: this.priceForm.childPrice,
        stock: this.priceForm.stock,
        commission: this.priceForm.commission,
        status: 1,
        saleNum: 0,
        specValueIds: this.modelType === 'multiple' ? this.selectedSpecInfo.values.map(v => v.id).sort() : [],
        skuId: this.getCurrentSkuId(),
        skuName: this.priceForm.skuName
      }

      // 更新本地显示数据
      this.tempLocalPriceData[priceKey] = {
        adultPrice: this.priceForm.adultPrice,
        childPrice: this.priceForm.childPrice,
        stock: this.priceForm.stock,
        commission: this.priceForm.commission,
        date: this.selectedDate,
        status: 1,
        skuId: this.getCurrentSkuId()
      }

      // 发送价格变更事件
      this.$emit('price-change', {
        tempPriceData: this.tempPriceData,
        localPriceData: this.tempLocalPriceData
      })

      this.priceDialogVisible = false
      this.$emit('unsaved-changes', true)
      this.$message.success('价格已更新，请点击保存按钮提交更改')
    },

    // 处理批量设置
    handleBatchSet(values) {
      // 获取当前SKU的名称
      const currentSkuName = this.getCurrentSkuName(values)
      this.batchSetForm = {
        dateRange: [this.weekStartDate, this.weekEndDate],
        weekdays: [],
        status: 1,
        adultPrice: 0,
        childPrice: 0,
        commission: 0,
        stock: 0,
        skuName: currentSkuName || this.generateDefaultSkuName()
      }
      this.selectedSpecInfo = {
        category1: this.modelType === 'unified' ? '统一规格' : values[0].category.name,
        values: this.modelType === 'unified' ? [] : values.map(value => ({
          id: value.value.id,
          category: value.category.name,
          value: value.name
        }))
      }

      this.dialogBatchSet = true
    },

    // 保存批量设置
    saveBatchSet() {
      if (!this.batchSetForm.dateRange || this.batchSetForm.dateRange.length !== 2) {
        this.$message.error('请选择日期范围')
        return
      }

      const [startDate, endDate] = this.batchSetForm.dateRange
      const start = new Date(startDate)
      const end = new Date(endDate)

      // 重构循环以避免linter错误
      const startTime = start.getTime()
      const endTime = end.getTime()
      const oneDay = 24 * 60 * 60 * 1000

      for (let time = startTime; time <= endTime; time += oneDay) {
        const currentDate = new Date(time)
        const dateStr = this.formatDate(currentDate)
        const dayOfWeek = currentDate.getDay()

        if (this.batchSetForm.weekdays.includes(dayOfWeek)) {
          const priceKey = this.modelType === 'unified'
            ? `unified_${dateStr}`
            : this.selectedSpecInfo.values.length > 0
              ? this.selectedSpecInfo.values.map(v => v.id).sort().join('-') + '_' + dateStr
              : `unified_${dateStr}`

          // 将批量设置的价格信息存储到临时对象中
          this.tempPriceData[priceKey] = {
            calendarDate: dateStr,
            adultPrice: this.batchSetForm.adultPrice,
            childPrice: this.batchSetForm.childPrice,
            stock: this.batchSetForm.stock,
            commission: this.batchSetForm.commission,
            status: this.batchSetForm.status,
            saleNum: 0,
            specValueIds: this.modelType === 'multiple' ? this.selectedSpecInfo.values.map(v => v.id).sort() : [],
            skuId: this.getCurrentSkuId(),
            skuName: this.batchSetForm.skuName
          }

          // 更新本地显示数据
          this.tempLocalPriceData[priceKey] = {
            adultPrice: this.batchSetForm.adultPrice,
            childPrice: this.batchSetForm.childPrice,
            stock: this.batchSetForm.stock,
            commission: this.batchSetForm.commission,
            date: dateStr,
            status: this.batchSetForm.status,
            skuId: this.getCurrentSkuId()
          }
        }
      }

      // 发送价格变更事件
      this.$emit('price-change', {
        tempPriceData: this.tempPriceData,
        localPriceData: this.tempLocalPriceData
      })

      this.dialogBatchSet = false
      this.$emit('unsaved-changes', true)
      this.$message.success('批量设置已更新，请点击保存按钮提交更改')
    },

    // 选择所有工作日
    selectAllWeekdays() {
      const allDays = [1, 2, 3, 4, 5, 6, 0]
      const isAllSelected = allDays.every(day => this.batchSetForm.weekdays.includes(day))
      this.$set(this.batchSetForm, 'weekdays', isAllSelected ? [] : allDays)
    },

    // 选择工作日
    selectWorkdays() {
      const workdays = [1, 2, 3, 4, 5]
      const isWorkdaysSelected = workdays.every(day => this.batchSetForm.weekdays.includes(day))
      const weekends = this.batchSetForm.weekdays.filter(day => [0, 6].includes(day))
      this.$set(this.batchSetForm, 'weekdays', isWorkdaysSelected
        ? weekends
        : [...new Set([...weekends, ...workdays])])
    },

    // 选择周末
    selectWeekends() {
      const weekends = [0, 6]
      const isWeekendsSelected = weekends.every(day => this.batchSetForm.weekdays.includes(day))
      const workdays = this.batchSetForm.weekdays.filter(day => ![0, 6].includes(day))
      this.$set(this.batchSetForm, 'weekdays', isWeekendsSelected
        ? workdays
        : [...new Set([...workdays, ...weekends])])
    },

    // 判断是否应该合并单元格
    shouldMergeCell(rowIndex, valueIndex) {
      if (this.modelType === 'unified') return false
      if (rowIndex === 0) return false
      const currentRow = this.tableRows[rowIndex]
      const prevRow = this.tableRows[rowIndex - 1]
      if (!prevRow) return false
      return currentRow.values[valueIndex].name === prevRow.values[valueIndex].name
    },

    // 获取行跨度
    getRowspan(rowIndex, valueIndex) {
      if (this.modelType === 'unified') return 1
      if (rowIndex > 0 && this.shouldMergeCell(rowIndex, valueIndex)) {
        return 0
      }
      let rowspan = 1
      let currentIndex = rowIndex + 1
      while (currentIndex < this.tableRows.length && this.tableRows[currentIndex].values[valueIndex].name === this.tableRows[rowIndex].values[valueIndex].name) {
        rowspan++
        currentIndex++
      }
      return rowspan
    },

    // 验证SKU名称
    validateSkuName() {
      this.skuNameError = ''
      const skuName = this.priceForm.skuName
      if (!skuName || skuName.trim() === '') {
        return true // 允许为空
      }

      // 调用后端接口验证SKU名称唯一性
      // 这里需要传入商品ID和当前SKU ID
      if (this.form && this.form.id && this.selectedSpecInfo) {
        const currentSkuId = this.getCurrentSkuId()
        this.checkSkuNameUnique(this.form.id, skuName.trim(), currentSkuId)
      }
    },

    // 获取当前SKU ID
    getCurrentSkuId() {
      if (this.modelType === 'unified') {
        // 统一规格：从calendarData中获取统一规格的SKU ID
        if (this.calendarData && this.calendarData.attrInfo) {
          // 对于统一规格，attrInfo的key通常是SKU ID本身或者特定的标识
          const unifiedKeys = Object.keys(this.calendarData.attrInfo).filter(key => {
            const item = this.calendarData.attrInfo[key]
            return item && item.id
          })
          if (unifiedKeys.length > 0) {
            return this.calendarData.attrInfo[unifiedKeys[0]].id
          }
        }
        // 如果calendarData中没有，尝试从form中获取
        if (this.form && this.form.skuId) {
          return this.form.skuId
        }
        return null
      } else if (this.selectedSpecInfo && this.selectedSpecInfo.values) {
        const specKey = this.selectedSpecInfo.values.map(v => v.id).sort().join('-')
        return this.calendarData.attrInfo && this.calendarData.attrInfo[specKey]
          ? this.calendarData.attrInfo[specKey].id
          : null
      }
      return null
    },

    // 检查SKU名称唯一性
    async checkSkuNameUnique(goodsId, skuName, excludeId) {
      try {
        // 这里应该调用后端API检查唯一性
        // const response = await this.$api.checkSkuNameUnique({ goodsId, skuName, excludeId })
        // if (!response.data) {
        //   this.skuNameError = 'SKU名称已存在，请修改'
        // }

        // 临时的前端验证逻辑
        console.log('检查SKU名称唯一性:', { goodsId, skuName, excludeId })
      } catch (error) {
        console.error('检查SKU名称唯一性失败:', error)
      }
    },

    // 生成默认SKU名称
    generateDefaultSkuName() {
      if (this.modelType === 'unified') {
        return '默认规格'
      } else if (this.selectedSpecInfo && this.selectedSpecInfo.values) {
        return this.selectedSpecInfo.values.map(v => v.value).join('-')
      }
      return ''
    },

    // 获取当前SKU名称（从已保存的数据中获取）
    getCurrentSkuName(values) {
      console.log('getCurrentSkuName调用:', {
        modelType: this.modelType,
        values: values,
        calendarData: this.calendarData
      })

      if (this.modelType === 'unified') {
        // 统一规格：从calendarData.attrInfo中获取SKU名称
        if (this.calendarData && this.calendarData.attrInfo) {
          // 对于统一规格，attrInfo的key通常是SKU ID
          const attrInfoKeys = Object.keys(this.calendarData.attrInfo)
          console.log('统一规格 - attrInfoKeys:', attrInfoKeys)
          if (attrInfoKeys.length > 0) {
            const firstKey = attrInfoKeys[0]
            const attrInfoItem = this.calendarData.attrInfo[firstKey]
            console.log('统一规格 - attrInfoItem:', attrInfoItem)
            if (attrInfoItem && attrInfoItem.skuName) {
              console.log('统一规格 - 返回skuName:', attrInfoItem.skuName)
              return attrInfoItem.skuName
            }
          }
        }
        console.log('统一规格 - 返回默认值')
        return '默认规格'
      } else if (values && values.length > 0) {
        // 多规格：根据规格值组合获取对应SKU的名称
        const specKey = values.map(value => value.value.id).sort().join('-')
        console.log('多规格 - specKey:', specKey)
        if (this.calendarData && this.calendarData.attrInfo && this.calendarData.attrInfo[specKey]) {
          const attrInfoItem = this.calendarData.attrInfo[specKey]
          console.log('多规格 - attrInfoItem:', attrInfoItem)
          if (attrInfoItem && attrInfoItem.skuName) {
            console.log('多规格 - 返回skuName:', attrInfoItem.skuName)
            return attrInfoItem.skuName
          }
        }
        const defaultName = values.map(v => v.name).join('-')
        console.log('多规格 - 返回默认值:', defaultName)
        return defaultName
      }
      console.log('返回空字符串')
      return ''
    }
  }
}
</script>

<style scoped>
.calendar-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.week-range {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.calendar-body {
  width: 100%;
  overflow-x: auto;
}

.price-calendar-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.price-calendar-table th,
.price-calendar-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #EBEEF5;
  min-width: 100px;
  vertical-align: middle;
}

.price-calendar-table th:first-child,
.price-calendar-table td:first-child {
  min-width: 120px;
  text-align: center;
}

.price-calendar-table th {
  background-color: #F5F7FA;
  color: #606266;
  font-weight: 500;
}

.price-cell {
  cursor: pointer;
  transition: all 0.3s;
  height: 80px;
  vertical-align: middle;
  text-align: center;
}

.price-cell:hover {
  background-color: #F5F7FA;
}

.price-cell.is-disabled {
  background-color: #F5F7FA;
  cursor: not-allowed;
  opacity: 0.7;
}

.price-cell.is-today {
  background-color: #ECF5FF;
}

.price-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.price {
  color: #F56C6C;
  font-weight: bold;
  text-align: center;
}

.stock {
  color: #909399;
  font-size: 12px;
  text-align: center;
}

.price-placeholder {
  color: #C0C4CC;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  opacity: 0;
  transition: opacity 0.3s;
}

.price-cell:hover .edit-icon {
  opacity: 1;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.weekday-select {
  margin-bottom: 10px;
}

.weekday-select .el-checkbox {
  margin-right: 15px;
  margin-bottom: 10px;
}

.quick-select {
  display: flex;
  gap: 15px;
}

.quick-select .el-button {
  padding: 0;
}

.unit {
  margin-left: 10px;
  color: #606266;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.price-dialog {
  z-index: 2010 !important;
}

.hint {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}
</style>
