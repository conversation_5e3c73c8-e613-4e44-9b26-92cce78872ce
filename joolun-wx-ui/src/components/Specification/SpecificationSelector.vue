<template>
  <div class="model-type-container">
    <div class="model-type-header">
      <div class="header-indicator"></div>
      <span class="header-title">规格配置</span>
    </div>

    <div class="spec-type-section">
      <div class="spec-type-label">
        <span class="required-star">*</span>
        <span class="label-text">选择规格类型</span>
      </div>

      <el-radio-group v-model="currentModelType" @change="handleModelTypeChange" class="radio-group">
        <el-radio label="unified" class="radio-item">统一规格</el-radio>
        <el-radio label="multiple" class="radio-item">多级规格</el-radio>
      </el-radio-group>
    </div>

    <!-- 多级规格内容 -->
    <div v-if="currentModelType === 'multiple'" class="model-categories">
      <!-- 规格名列表 -->
      <div class="spec-list">
        <div class="spec-tabs">
          <draggable v-model="modelCategories" :options="{animation:200,handle:'.spec-tab'}" @end="handleCategorySortEnd">
            <transition-group tag="div" class="spec-tabs-inner">
              <div v-for="(category, index) in modelCategories" :key="category.id || index"
                :class="['spec-tab', { 'is-active': activeSpec && activeSpec.id === category.id }]">
                <span class="drag-icon el-icon-rank"></span>
                <el-input v-if="!category.name" v-model="category.name" size="small" placeholder="请输入规格名称"
                  class="spec-input" @blur="handleSpecNameBlur(category)" />
                <span @click="handleSpecClick(index)" v-else class="spec-name-text">{{ category.name }}</span>
              </div>
            </transition-group>
          </draggable>
        </div>
        <el-button type="primary" size="mini" class="save-sort-btn" :disabled="!hasSortChanges" @click="saveSort">保存排序</el-button>
      </div>

      <!-- 已选规格值展示区域 -->
      <div v-if="hasSelectedSpecs" class="selected-specs">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div
            v-for="(category, index) in selectedSpecOrder"
            :key="'selected-'+index"
            class="spec-category-section"
          >
            <template v-if="userSelectSpecValues[category] && userSelectSpecValues[category].length > 0">
              <div class="spec-category-header">
                <span class="category-name">{{ category }}</span>
                <el-button
                  type="text"
                  size="mini"
                  class="remove-category-btn"
                  @click="removeCategory({name: category})"
                >
                  <i class="el-icon-delete" />
                  删除
                </el-button>
              </div>

              <div class="spec-values-grid">
                <draggable v-model="userSelectSpecValues[category]" :options="{animation:200}"
                  @end="handleValueSortEnd(category)">
                  <transition-group tag="div" class="values-inner" :key="'vals-'+category">
                    <div
                      v-for="value in userSelectSpecValues[category]"
                      :key="value.id"
                      :class="[
                      'spec-value-item',
                      value.isCustom ? 'spec-value-custom' : 'spec-value-normal'
                      ]"
                    >
                      <span class="drag-icon el-icon-rank"></span>
                      <span class="spec-value-name">{{ value.name }}</span>
                      <el-button
                        type="text"
                        size="mini"
                        class="remove-value-btn"
                        @click="removeSpecValue({name: category, values: userSelectSpecValues[category]}, value)"
                      >
                        <i class="el-icon-close" />
                      </el-button>
                    </div>
                  </transition-group>
                </draggable>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 规格值选择弹出框 -->
      <el-dialog
        :title="activeSpec ? '选择' + activeSpec.name + '规格值' : '选择规格值'"
        :visible.sync="specValueDialogVisible"
        width="600px"
        :close-on-click-modal="false"
        :append-to-body="true"
        custom-class="spec-value-dialog"
      >
        <div class="spec-selector">
          <div class="search-box">
            <el-input v-model="searchQuery" prefix-icon="el-icon-search" placeholder="搜索规格值" clearable>
              <el-button slot="append" :disabled="!searchQuery" @click="addCustomSpecValue">
                添加
              </el-button>
            </el-input>
          </div>
          <div class="spec-options">
            <el-checkbox
              v-for="(value, index) in filteredSpecValues"
              :key="index"
              v-model="value.selected"
              class="spec-option"
              @change="handleSpecValueChange"
            >
              {{ value.name }}
              <el-tag v-if="value.isCustom" size="mini" type="warning">自定义</el-tag>
            </el-checkbox>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-checkbox v-model="isAllSelected" @change="handleSelectAll">全选</el-checkbox>
          <div class="dialog-buttons">
            <el-button @click="specValueDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleConfirmSelection">
              确定({{ selectedCount }})
            </el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { allAttrList } from '@/api/mall/goodsspu'
import { updateSort } from '@/api/mall/attr'
import draggable from 'vuedraggable'

export default {
  name: 'SpecificationSelector',
  components: { draggable },
  props: {
    modelType: {
      type: String,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    userSelectSpecValues: {
      type: Object,
      default: () => ({})
    },
    selectedSpecOrder: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      modelCategories: [{
        name: '',
        values: []
      }],
      specValueDialogVisible: false,
      searchQuery: '',
      activeSpec: null,
      specValues: {},
      tempSpecData: {
        addedSpecs: [],
        removedSpecs: [],
        addedValues: [],
        removedValues: [],
        specValueChanges: []
      },
      currentModelType: this.modelType,
      hasSortChanges: false,
      sortPayload: []
    }
  },
  computed: {
    filteredSpecValues() {
      if (!this.activeSpec) return []
      const values = this.specValues[this.activeSpec.name] || []
      if (!this.searchQuery) return values
      return values.filter(v =>
        v.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      )
    },
    isAllSelected: {
      get() {
        if (!this.filteredSpecValues || this.filteredSpecValues.length === 0) return false
        return this.filteredSpecValues.every(v => v.selected)
      },
      set(value) {
        if (!this.filteredSpecValues) return
        this.filteredSpecValues.forEach(v => {
          this.$set(v, 'selected', value)
        })
      }
    },
    selectedCount() {
      if (!this.activeSpec || !this.specValues[this.activeSpec.name]) return 0
      return this.specValues[this.activeSpec.name].filter(v => v.selected).length
    },
    hasSelectedSpecs() {
      return Object.values(this.userSelectSpecValues).some(values => values && values.length > 0)
    }
  },
  watch: {
    modelType: {
      handler(newVal) {
        this.currentModelType = newVal
      },
      immediate: true
    },
    currentModelType: {
      handler(newVal, oldVal) {
        console.log('SpecificationSelector watch currentModelType:', JSON.stringify({
          oldVal,
          newVal
        }, null, 2))

        if (newVal === 'multiple') {
          // 切换到多级规格，加载规格数据
          this.loadSpecificationData()
        } else if (newVal === 'unified') {
          // 切换到统一规格，清空多级规格相关数据
          this.modelCategories = [{
            name: '',
            values: []
          }]
          this.specValues = {}
          this.activeSpec = null
          this.specValueDialogVisible = false
          this.searchQuery = ''
        }
      },
      immediate: true
    },
    userSelectSpecValues: {
      handler(newVal) {
        // 当父组件传递的规格值发生变化时，更新本地的选中状态
        if (newVal && Object.keys(newVal).length > 0) {
          Object.keys(newVal).forEach(categoryName => {
            const selectedValues = newVal[categoryName]
            if (this.specValues[categoryName]) {
              this.specValues[categoryName].forEach(value => {
                value.selected = selectedValues.some(v => v.id === value.id)
              })
            }
          })
          this.$forceUpdate()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 处理规格类型变更
    handleModelTypeChange(value) {
      console.log('SpecificationSelector 接收到规格类型切换:', JSON.stringify({
        oldModelType: this.currentModelType,
        newModelType: value
      }, null, 2))

      // 清空当前组件的数据
      this.modelCategories = [{
        name: '',
        values: []
      }]
      this.specValues = {}
      this.activeSpec = null
      this.specValueDialogVisible = false
      this.searchQuery = ''
      this.tempSpecData = {
        addedSpecs: [],
        removedSpecs: [],
        addedValues: [],
        removedValues: [],
        specValueChanges: []
      }

      // 发送切换事件到父组件
      this.$emit('model-type-change', value)
    },

    // 加载规格数据
    loadSpecificationData() {
      allAttrList(this.form.goodsId)
        .then(res => {
          if (res.code === 200 && res.data && res.data.length > 0) {
            // 按 sortOrder ASC 排序分类
            const sortedAttr = [...res.data].sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999))

            this.modelCategories = sortedAttr.map(item => {
              const values = (item.goodsSpecificationValueList || [])
                .sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999))
              return {
                id: item.id,
                name: item.attrName,
                values: values.map(spec => ({
                  id: spec.id,
                  name: spec.name,
                  attrId: item.id
                }))
              }
            })

            // 初始化specValues
            this.specValues = {}
            this.modelCategories.forEach(category => {
              this.specValues[category.name] = (category.values || []).map(v => ({
                id: v.id,
                name: v.name,
                selected: false
              }))
            })

            // 更新已选规格值的选中状态
            Object.keys(this.userSelectSpecValues).forEach(categoryName => {
              const selectedValues = this.userSelectSpecValues[categoryName]
              if (this.specValues[categoryName]) {
                this.specValues[categoryName].forEach(value => {
                  value.selected = selectedValues.some(v => v.id === value.id)
                })
              }
            })
          }
        })
        .catch(err => {
          console.error('加载规格数据失败:', err)
        })
    },

    // 处理规格点击
    handleSpecClick(index) {
      this.activeSpec = this.modelCategories[index]
      this.searchQuery = ''

      // 记录规格选择顺序
      if (this.activeSpec && !this.selectedSpecOrder.includes(this.activeSpec.name)) {
        const newOrder = [...this.selectedSpecOrder, this.activeSpec.name]
        this.$emit('spec-values-change', {
          userSelectSpecValues: this.userSelectSpecValues,
          selectedSpecOrder: newOrder
        })
      }

      // 初始化选中状态
      if (this.activeSpec) {
        if (!this.specValues[this.activeSpec.name]) {
          this.specValues[this.activeSpec.name] = (this.activeSpec.values || []).map(v => ({
            id: v.id,
            name: v.name,
            selected: false,
            isCustom: v.id.toString().startsWith('custom_')
          }))
        } else {
          this.specValues[this.activeSpec.name].forEach(v => {
            this.$set(v, 'selected', false)
          })
        }
      }
      this.specValueDialogVisible = true
    },

    // 处理规格名称失焦
    handleSpecNameBlur(category) {
      if (!category.name) return

      const duplicateName = this.modelCategories.find(c =>
        c !== category && c.name === category.name
      )
      if (duplicateName) {
        this.$message.warning('规格名称不能重复')
        category.name = ''
        return
      }

      if (!this.specValues[category.name]) {
        this.$set(this.specValues, category.name, [])
      }
    },

    // 处理全选
    handleSelectAll(val) {
      if (!this.filteredSpecValues) return
      this.filteredSpecValues.forEach(v => {
        this.$set(v, 'selected', val)
      })
      this.$forceUpdate()
    },

    // 处理规格值变更
    handleSpecValueChange() {
      this.$forceUpdate()
      if (this.filteredSpecValues && this.filteredSpecValues.length > 0) {
        const allSelected = this.filteredSpecValues.every(v => v.selected)
        if (allSelected !== this.isAllSelected) {
          this.$set(this, 'isAllSelected', allSelected)
        }
      }
    },

    // 确认选择
    handleConfirmSelection() {
      if (!this.activeSpec) return

      const selectedValues = []
      this.filteredSpecValues.forEach(v => {
        if (v.selected) {
          selectedValues.push({
            id: v.id,
            name: v.name,
            attrId: this.activeSpec.id,
            isCustom: v.isCustom
          })
        }
      })

      const categoryName = this.activeSpec.name
      if (!categoryName) {
        this.$message.warning('请先输入规格名称')
        return
      }

      // 获取原有的规格值
      const oldValues = this.userSelectSpecValues[categoryName] || []
      // 找出新增和删除的规格值
      const addedValues = selectedValues.filter(newVal =>
        !oldValues.some(oldVal => oldVal.id === newVal.id)
      )
      const removedValues = oldValues.filter(oldVal =>
        !selectedValues.some(newVal => newVal.id === oldVal.id)
      )

      // 更新临时存储
      if (addedValues.length > 0 || removedValues.length > 0) {
        this.tempSpecData.specValueChanges.push({
          categoryName,
          categoryId: this.activeSpec.id,
          addedValues: addedValues.map(v => ({
            ...v,
            categoryName
          })),
          removedValues: removedValues.map(v => ({
            ...v,
            categoryName
          }))
        })
      }

      // 更新用户选择的规格值
      const newUserSelectSpecValues = { ...this.userSelectSpecValues }
      this.$set(newUserSelectSpecValues, categoryName, selectedValues)

      // 发送变更事件
      this.$emit('spec-values-change', {
        userSelectSpecValues: newUserSelectSpecValues,
        selectedSpecOrder: this.selectedSpecOrder,
        tempSpecData: this.tempSpecData
      })

      this.specValueDialogVisible = false
      this.$message.success('规格值已更新，请点击保存按钮提交更改')
    },

    // 添加自定义规格值
    addCustomSpecValue() {
      if (!this.searchQuery) return

      if (!this.activeSpec) {
        this.$message.warning('请先选择规格分类')
        return
      }

      const existingValue = this.specValues[this.activeSpec.name].find(v =>
        v.name === this.searchQuery
      )

      if (existingValue) {
        this.$message.warning('该规格值已存在')
        return
      }

      const newValue = {
        id: 'custom_' + Date.now(),
        name: this.searchQuery,
        selected: true,
        isCustom: true
      }

      this.specValues[this.activeSpec.name].push(newValue)

      const currentCategory = this.modelCategories.find(c => c.id === this.activeSpec.id)
      if (currentCategory) {
        currentCategory.values.push({
          id: newValue.id,
          name: newValue.name,
          attrId: this.activeSpec.id,
          isCustom: true,
          selected: true
        })
      }

      this.searchQuery = ''
      this.$message.success('添加成功')
    },

    // 删除规格值
    removeSpecValue(category, value) {
      const index = category.values.findIndex(v => v.id === value.id)
      if (index > -1) {
        category.values.splice(index, 1)
      }

      if (this.specValues[category.name]) {
        const specValue = this.specValues[category.name].find(v => v.id === value.id)
        if (specValue) {
          this.$set(specValue, 'selected', false)
        }
      }

      if (value.isCustom && this.specValues[category.name]) {
        const specIndex = this.specValues[category.name].findIndex(v => v.id === value.id)
        if (specIndex > -1) {
          this.specValues[category.name].splice(specIndex, 1)
        }
      }

      this.tempSpecData.specValueChanges.push({
        categoryName: category.name,
        categoryId: value.attrId,
        addedValues: [],
        removedValues: [{
          ...value,
          categoryName: category.name
        }]
      })

      // 更新用户选择的规格值
      const newUserSelectSpecValues = { ...this.userSelectSpecValues }
      if (newUserSelectSpecValues[category.name]) {
        const valueIndex = newUserSelectSpecValues[category.name].findIndex(v => v.id === value.id)
        if (valueIndex > -1) {
          newUserSelectSpecValues[category.name].splice(valueIndex, 1)
        }
      }

      this.$emit('spec-values-change', {
        userSelectSpecValues: newUserSelectSpecValues,
        selectedSpecOrder: this.selectedSpecOrder,
        tempSpecData: this.tempSpecData
      })

      if (!this.hasSelectedSpecs) {
        this.$forceUpdate()
      }
    },

    // 删除规格分类
    removeCategory(category) {
      const hasValues = this.userSelectSpecValues[category.name] && this.userSelectSpecValues[category.name].length > 0
      const confirmMessage = hasValues
        ? '该规格已有选择的规格值，确定要删除该规格及其选中的规格值吗?'
        : '确定要删除该规格吗?'

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tempSpecData.removedSpecs.push({
          id: category.id,
          name: category.name
        })

        if (this.specValues[category.name]) {
          this.specValues[category.name].forEach(value => {
            this.$set(value, 'selected', false)
          })
        }

        const newUserSelectSpecValues = { ...this.userSelectSpecValues }
        if (newUserSelectSpecValues[category.name]) {
          this.$delete(newUserSelectSpecValues, category.name)
        }

        const newSelectedSpecOrder = this.selectedSpecOrder.filter(name => name !== category.name)

        this.$emit('spec-values-change', {
          userSelectSpecValues: newUserSelectSpecValues,
          selectedSpecOrder: newSelectedSpecOrder,
          tempSpecData: this.tempSpecData
        })
      }).catch(() => { })
    },

    // 拖拽规格分类结束
    handleCategorySortEnd() {
      const newOrder = this.modelCategories.map(c => c.name).filter(Boolean)
      this.sortPayload = this.computeSortPayload()
      this.hasSortChanges = this.sortPayload.length > 0
      this.$emit('spec-values-change', {
        userSelectSpecValues: this.userSelectSpecValues,
        selectedSpecOrder: newOrder,
        tempSpecData: this.tempSpecData
      })
    },

    // 拖拽规格值结束
    handleValueSortEnd(categoryName) {
      if (!this.userSelectSpecValues[categoryName]) return
      const newUserSelect = { ...this.userSelectSpecValues }
      newUserSelect[categoryName] = [...newUserSelect[categoryName]]
      this.sortPayload = this.computeSortPayload()
      this.hasSortChanges = this.sortPayload.length > 0
      this.$emit('spec-values-change', {
        userSelectSpecValues: newUserSelect,
        selectedSpecOrder: this.selectedSpecOrder,
        tempSpecData: this.tempSpecData
      })
    },

    // 计算排序 payload
    computeSortPayload() {
      const payload = []
      // 分类排序
      this.modelCategories.forEach((c, idx) => {
        if (c.id && typeof c.id === 'string' && !c.id.startsWith('custom_')) {
          payload.push({ id: c.id, type: 'CATEGORY', sortOrder: idx + 1 })
        }
      })
      // 规格值排序
      Object.keys(this.userSelectSpecValues).forEach(cat => {
        const arr = this.userSelectSpecValues[cat]
        arr.forEach((v, idx) => {
          if (v.id && typeof v.id === 'string' && !v.id.startsWith('custom_')) {
            payload.push({ id: v.id, type: 'VALUE', sortOrder: idx + 1 })
          }
        })
      })
      return payload
    },

    // 保存排序
    saveSort() {
      if (!this.hasSortChanges) return
      updateSort(this.sortPayload)
        .then(res => {
          if (res.code === 200) {
            this.$message.success('排序已保存')
            this.hasSortChanges = false
          } else {
            this.$message.error(res.msg || '保存失败')
          }
        })
        .catch(err => {
          console.error('保存排序失败', err)
          this.$message.error('保存失败')
        })
    }
  }
}
</script>

<style scoped>
.model-type-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e8e9eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
}

.model-type-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 12px;
}

.header-indicator {
  width: 4px;
  height: 16px;
  background-color: #F5222D;
  border-radius: 2px;
}

.header-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.spec-type-section {
  margin-bottom: 20px;
}

.spec-type-label {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 12px;
}

.required-star {
  color: #F5222D;
  font-size: 14px;
}

.label-text {
  font-size: 14px;
  color: #606266;
}

.radio-group {
  display: flex;
  gap: 12px;
}

.radio-item {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 8px 16px;
  margin-right: 0 !important;
  transition: all 0.2s ease;
}

.radio-item:hover {
  border-color: #F5222D;
}

.radio-item.is-checked {
  background-color: #fff2f0;
  border-color: #F5222D;
  color: #F5222D;
}

.model-categories {
  margin-top: 24px;
}

.spec-list {
  width: 100%;
  padding: 16px 0;
}

.spec-tabs {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.spec-tab {
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #f7f8fa;
  border: 1px solid #e4e7ed;
  color: #606266;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 40px;
}

.spec-tab:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
}

.spec-tab.is-active {
  background-color: #fff2f0;
  border-color: #F5222D;
  color: #F5222D;
  font-weight: 500;
}

.spec-input {
  width: 140px;
}

.spec-name-text {
  font-size: 14px;
}

.selected-specs {
  margin: 24px 0;
  padding: 0;
  background-color: transparent;
  border: none;
  border-radius: 0;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-6 {
  gap: 24px;
}

.spec-category-section {
  padding: 16px;
  background-color: #fafbfc;
  border: 1px solid #e8e9eb;
  border-radius: 8px;
}

.spec-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.category-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.remove-category-btn {
  padding: 4px 8px !important;
  font-size: 12px !important;
  color: #F5222D !important;
  border: 1px solid #F5222D !important;
  border-radius: 4px !important;
  background-color: transparent !important;
  transition: all 0.2s ease;
}

.remove-category-btn:hover {
  background-color: #F5222D !important;
  color: #fff !important;
}

.spec-values-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-value-item {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  min-width: 80px;
}

.spec-value-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spec-value-normal {
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  color: #1e40af;
}

.spec-value-normal:hover {
  border-color: #3b82f6;
}

.spec-value-custom {
  background-color: #fffbeb;
  border: 1px solid #fed7aa;
  color: #ea580c;
  border-style: dashed;
}

.spec-value-custom:hover {
  border-color: #fb923c;
}

.spec-value-name {
  font-size: 13px;
  font-weight: 500;
}

.remove-value-btn {
  padding: 2px 4px !important;
  font-size: 10px !important;
  color: #6b7280 !important;
  border: none !important;
  border-radius: 50% !important;
  background-color: transparent !important;
  transition: all 0.2s ease;
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
}

.remove-value-btn:hover {
  background-color: #ef4444 !important;
  color: #fff !important;
}

.spec-selector {
  padding: 0 4px;
}

.search-box {
  margin-bottom: 20px;
}

.spec-options {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px 0;
  border: 1px solid #e8e9eb;
  border-radius: 6px;
  background-color: #fafbfc;
}

.spec-option {
  display: block;
  margin-bottom: 0 !important;
  padding: 8px 16px;
  transition: background-color 0.2s ease;
}

.spec-option:hover {
  background-color: #f0f0f0;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #e8e9eb;
  background-color: #fafbfc;
}

.dialog-buttons {
  display: flex;
  gap: 12px;
}

.spec-value-dialog {
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-type-container {
    padding: 16px;
  }

  .model-type-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .radio-group {
    width: 100%;
  }

  .radio-item {
    flex: 1;
    text-align: center;
  }

  .spec-tabs {
    flex-direction: column;
  }

  .selected-specs {
    padding: 16px;
  }

  .spec-category-section {
    margin-bottom: 16px;
  }

  .spec-category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .spec-values-grid {
    gap: 6px;
  }

  .spec-value-item {
    font-size: 12px;
    padding: 4px 8px;
  }
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.drag-icon {
  cursor: move;
  font-size: 14px;
  color: #c0c4cc;
}

.values-inner > div {
  display: flex;
  align-items: center;
}

.spec-tabs-inner {
  display: flex;
  flex-wrap: wrap;   /* 一行放不下时自动换行，可按需保留 */
  gap: 12px;         /* 与原来保持一致的间距 */
}

.save-sort-btn {
  margin-top: 12px;
}
</style>