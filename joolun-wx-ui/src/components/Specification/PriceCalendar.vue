<template>
  <div class="specification-container">
    <div style="overflow-y: auto;height: 80vh; padding-right: 10px;">
      <!-- <el-divider content-position="left">生成产品</el-divider> -->

      <!-- 规格选择组件 -->
      <SpecificationSelector
        :model-type="modelType"
        :form="form"
        :user-select-spec-values="userSelectSpecValues"
        :selected-spec-order="selectedSpecOrder"
        @model-type-change="handleModelTypeChange"
        @spec-values-change="handleSpecValuesChange"
        @unsaved-changes="handleUnsavedChanges"
      />

      <!-- 价格日历组件 -->
      <PriceCalendarPanel
        :model-type="modelType"
        :form="form"
        :user-select-spec-values="userSelectSpecValues"
        :selected-spec-order="selectedSpecOrder"
        :local-price-data="localPriceData"
        :calendar-data="calendarData"
        @price-change="handlePriceChange"
        @unsaved-changes="handleUnsavedChanges"
      />
    </div>

    <!-- 保存按钮 -->
    <el-card class="save-button-card" shadow="hover">
      <el-button type="primary" size="large" :disabled="!hasUnsavedChanges" @click="saveAllChanges">
        <i class="el-icon-check" /> 保存所有更改
      </el-button>
    </el-card>
  </div>
</template>

<script>
import SpecificationSelector from './SpecificationSelector.vue'
import PriceCalendarPanel from './PriceCalendarPanel.vue'
import {
  getByGoodsId,
  queryCalendarPrices,
  batchSaveCalendarPrices,
  allAttrList
} from '@/api/mall/goodsspu'

export default {
  name: 'PriceCalendar',
  components: {
    SpecificationSelector,
    PriceCalendarPanel
  },
  props: {
    form: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 型号相关数据
      modelType: 'unified', // 先设置默认值，在created中根据form数据更新
      userSelectSpecValues: {},
      selectedSpecOrder: [],
      // 日历数据
      calendarData: {
        attrList: [],
        attrInfo: {},
        calendarInfo: {
          startDate: 0,
          endDate: 0
        }
      },
      localPriceData: {},
      // 临时存储数据
      tempPriceData: {},
      tempSpecData: {
        addedSpecs: [],
        removedSpecs: [],
        addedValues: [],
        removedValues: [],
        specValueChanges: []
      },
      hasUnsavedChanges: false
    }
  },
  computed: {
    hasSelectedSpecs() {
      return Object.values(this.userSelectSpecValues).some(values => values && values.length > 0)
    }
  },
  created() {
    this.initializeData()
  },
  methods: {
    // 初始化数据
    initializeData() {
      // 调试日志：检查form的完整内容
      console.log('PriceCalendar initializeData - 完整form对象:', JSON.stringify(this.form, null, 2))
      console.log('PriceCalendar initializeData - specificationType值:', this.form.specificationType)
      console.log('PriceCalendar initializeData - specificationType类型:', typeof this.form.specificationType)

      // 根据form数据设置modelType
      this.modelType = this.form.specificationType === '0' ? 'unified' : 'multiple'

      console.log('初始化数据:', JSON.stringify({
        formSpecificationType: this.form.specificationType,
        modelType: this.modelType,
        goodsId: this.form.goodsId
      }, null, 2))
      if (this.form.specificationType === '0') {
        // 统一规格
        this.userSelectSpecValues = {
          '默认规格': [{
            id: 'default_spec',
            name: '默认规格',
            selected: true
          }]
        }
        this.selectedSpecOrder = ['默认规格']
      } else {
        // 多级规格，需要加载规格数据
        this.loadSpecificationData()
      }
      this.loadCalendarData()
    },

    // 加载规格数据
    loadSpecificationData() {
      Promise.all([
        allAttrList(this.form.goodsId),
        getByGoodsId(this.form.goodsId)
      ]).then(([attrResponse, goodsResponse]) => {
        console.log('API响应数据:', JSON.stringify({ attrResponse, goodsResponse }, null, 2))

        if (attrResponse.code === 200 && attrResponse.data) {
          // 清空现有数据
          this.userSelectSpecValues = {}
          this.selectedSpecOrder = []

          const sortedAttr = [...attrResponse.data].sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999))
          // 处理规格数据 - 优先使用goodsResponse，如果为空则使用attrResponse
          if (goodsResponse.data && goodsResponse.data.attrList) {
            const attrList = goodsResponse.data.attrList
            console.log('使用goodsResponse.data.attrList')
            if (attrList && attrList.length > 0) {
              // 对 attrList 排序，若有 sortOrder 字段
              const sortedList = [...attrList].sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999))
              sortedList.forEach(attr => {
                if (attr.attrName && attr.attrValues) {
                  // 按值排序
                  const sortedValues = [...attr.attrValues].sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999))
                  this.$set(this.userSelectSpecValues, attr.attrName,
                    sortedValues.map(value => ({
                      id: value.id,
                      name: value.name,
                      selected: true,
                      attrId: value.attrId
                    }))
                  )
                }
              })
              this.selectedSpecOrder = sortedList.map(attr => attr.attrName)
            }
          } else {
            // 使用规格库信息
            sortedAttr.forEach(attr => {
              const sortedValues = (attr.goodsSpecificationValueList || []).sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999))
              this.$set(this.userSelectSpecValues, attr.attrName,
                sortedValues.map(value => ({
                  id: value.id,
                  name: value.name,
                  selected: false,
                  attrId: value.attrId
                }))
              )
            })
            this.selectedSpecOrder = sortedAttr.map(a => a.attrName)
          }

          // 强制更新视图以确保子组件接收到新数据
          this.$forceUpdate()

          console.log('加载的规格数据:', JSON.stringify({
            userSelectSpecValues: this.userSelectSpecValues,
            selectedSpecOrder: this.selectedSpecOrder,
            hasGoodsResponse: !!(goodsResponse.data && goodsResponse.data.attrList)
          }, null, 2))
        } else {
          console.log('attrResponse 响应异常:', JSON.stringify(attrResponse, null, 2))
        }
      }).catch(err => {
        console.error('加载规格数据失败:', JSON.stringify(err, null, 2))
      })
    },

    // 加载日历数据
    loadCalendarData() {
      const specType = this.modelType === 'unified' ? '0' : '1'
      queryCalendarPrices(this.form.goodsId, specType)
        .then(res => {
          if (res.code === 200 && res.data && res.data.length > 0) {
            this.calendarData = res.data[0]
            this.updatePriceData()
          }
        })
        .catch(err => {
          console.error('加载日历数据失败:', err)
          this.$message.error('加载日历数据失败')
        })
    },

    // 更新价格数据
    updatePriceData() {
      if (this.calendarData.attrInfo) {
        this.localPriceData = {}
        Object.entries(this.calendarData.attrInfo).forEach(([key, info]) => {
          if (info.calendarPrice) {
            info.calendarPrice.forEach(price => {
              const priceKey = this.modelType === 'unified' ? `unified_${price.date}` : `${key}_${price.date}`
              const priceInfo = {
                adultPrice: price.prices[0].price,
                childPrice: price.prices[0].childPrice,
                stock: price.stock,
                date: price.date,
                status: price.status,
                skuId: info.id,
                commission: price.prices[0].commission || 0
              }
              this.$set(this.localPriceData, priceKey, priceInfo)
            })
          }
        })
        console.log('更新价格数据:', JSON.stringify({
          modelType: this.modelType,
          calendarDataKeys: Object.keys(this.calendarData.attrInfo),
          localPriceDataKeys: Object.keys(this.localPriceData),
          samplePriceData: Object.entries(this.localPriceData).slice(0, 3)
        }, null, 2))
        this.$forceUpdate()
      }
    },

    // 处理规格类型变更
    handleModelTypeChange(value) {
      console.log('规格类型切换:', JSON.stringify({
        oldModelType: this.modelType,
        newModelType: value
      }, null, 2))

      this.modelType = value

      // 清空临时数据和未保存状态
      this.tempPriceData = {}
      this.tempSpecData = {
        addedSpecs: [],
        removedSpecs: [],
        addedValues: [],
        removedValues: [],
        specValueChanges: []
      }
      this.hasUnsavedChanges = false

      // 清空价格数据
      this.localPriceData = {}
      this.calendarData = {
        attrList: [],
        attrInfo: {},
        calendarInfo: {
          startDate: 0,
          endDate: 0
        }
      }

      if (value === 'unified') {
        // 切换到统一规格
        this.userSelectSpecValues = {
          '默认规格': [{
            id: 'default_spec',
            name: '默认规格',
            selected: true
          }]
        }
        this.selectedSpecOrder = ['默认规格']
        console.log('切换到统一规格，清空多规格数据')
      } else {
        // 切换到多级规格
        this.userSelectSpecValues = {}
        this.selectedSpecOrder = []
        console.log('切换到多级规格，重新加载规格数据')
        // 重新加载规格数据
        this.loadSpecificationData()
      }

      // 重新加载价格日历数据
      this.loadCalendarData()

      // 强制更新视图
      this.$forceUpdate()
    },

    // 处理规格值变更
    handleSpecValuesChange(data) {
      this.userSelectSpecValues = data.userSelectSpecValues
      this.selectedSpecOrder = data.selectedSpecOrder
      if (data.tempSpecData) {
        // 直接替换临时规格数据，而不是累积添加
        this.tempSpecData = {
          addedSpecs: [...data.tempSpecData.addedSpecs],
          removedSpecs: [...data.tempSpecData.removedSpecs],
          addedValues: [...data.tempSpecData.addedValues],
          removedValues: [...data.tempSpecData.removedValues],
          specValueChanges: [...data.tempSpecData.specValueChanges]
        }
      }
    },

    // 处理价格变更
    handlePriceChange(data) {
      // 合并价格数据
      Object.assign(this.tempPriceData, data.tempPriceData)
      Object.assign(this.localPriceData, data.localPriceData)
    },

    // 处理未保存更改标志
    handleUnsavedChanges(hasChanges) {
      this.hasUnsavedChanges = hasChanges
    },

    // 去重 specValueChanges 数据
    deduplicateSpecValueChanges(specValueChanges) {
      if (!specValueChanges || specValueChanges.length === 0) return []

      // 按照 categoryName + categoryId 进行分组
      const grouped = {}
      specValueChanges.forEach(change => {
        const key = `${change.categoryName}_${change.categoryId}`
        if (!grouped[key]) {
          grouped[key] = {
            categoryName: change.categoryName,
            categoryId: change.categoryId,
            addedValues: new Map(),
            removedValues: new Map()
          }
        }

        // 合并新增的规格值（去重）
        change.addedValues.forEach(value => {
          grouped[key].addedValues.set(value.id, value)
        })

        // 合并删除的规格值（去重）
        change.removedValues.forEach(value => {
          grouped[key].removedValues.set(value.id, value)
        })
      })

      // 转换回数组格式，只保留有实际变更的记录
      return Object.values(grouped)
        .map(group => ({
          categoryName: group.categoryName,
          categoryId: group.categoryId,
          addedValues: Array.from(group.addedValues.values()),
          removedValues: Array.from(group.removedValues.values())
        }))
        .filter(change => change.addedValues.length > 0 || change.removedValues.length > 0)
    },

    // 保存所有更改
    saveAllChanges() {
      // 调试日志：显示当前 tempPriceData 的内容
      console.log('=== saveAllChanges 开始 ===')
      console.log('tempPriceData 内容:', JSON.stringify(this.tempPriceData, null, 2))
      console.log('tempPriceData 的 keys:', Object.keys(this.tempPriceData))
      // 1. 生成所有SKU组合
      let allSkuCombinations = []
      if (this.modelType === 'unified') {
        allSkuCombinations = [[{ category: { name: '默认规格' }, value: { id: 'default_spec', name: '默认规格' }, name: '默认规格' }]]
      } else {
        // 生成所有规格值组合
        const categories = this.selectedSpecOrder
          .filter(name => {
            const values = this.userSelectSpecValues[name]
            return values && values.length > 0
          })
          .map(name => {
            return {
              name: name,
              values: this.userSelectSpecValues[name]
            }
          })
          .filter(Boolean)
        if (categories.length === 0) {
          this.$message.warning('请先选择规格值')
          return
        }
        // 递归生成所有组合
        const generateCombinations = (arrays) => {
          if (arrays.length === 0) return [[]]
          const result = []
          const restCombinations = generateCombinations(arrays.slice(1))
          arrays[0].forEach(item => {
            restCombinations.forEach(combination => {
              result.push([item, ...combination])
            })
          })
          return result
        }
        const specArrays = categories.map(category => {
          return category.values.map(value => ({
            category: category,
            value: value,
            name: value.name,
            id: value.id
          }))
        })
        allSkuCombinations = generateCombinations(specArrays)
      }

      // 2. 生成所有SKU+日期的 priceList
      // 先收集所有已设置价格的key
      const tempPriceData = this.tempPriceData || {}
      const priceList = []
      // 统一规格只需处理已设置的日期
      if (this.modelType === 'unified') {
        // 找到所有已设置的日期
        const allDates = new Set()
        Object.keys(tempPriceData).forEach(key => {
          if (key.startsWith('unified_')) {
            allDates.add(key.replace('unified_', ''))
          }
        })
        // 只要有一个日期就可以
        if (allDates.size === 0) {
          this.$message.warning('请设置日历价格')
          return
        }
        allDates.forEach(date => {
          // 统一规格只有一个SKU
          const priceKey = `unified_${date}`
          const priceObj = tempPriceData[priceKey] || {
            calendarDate: date,
            adultPrice: 0,
            childPrice: 0,
            stock: 0,
            commission: 0,
            status: 1,
            saleNum: 0,
            specValueIds: [],
            skuId: 'default_spec',
            skuName: '默认规格'
          }
          priceList.push({ ...priceObj })
        })
      } else {
        // 多规格：所有组合+所有已设置的日期
        // 先收集所有已设置的日期
        const allDates = new Set()
        Object.keys(tempPriceData).forEach(key => {
          const arr = key.split('_')
          if (arr.length > 1) {
            allDates.add(arr.slice(-1)[0])
          }
        })
        if (allDates.size === 0) {
          this.$message.warning('请设置日历价格')
          return
        }
        // 遍历所有组合和所有日期
        allSkuCombinations.forEach(skuCombo => {
          const specIds = skuCombo.map(v => v.id)
          const skuName = skuCombo.map(v => v.name).join('-')
          // 确保 SKU ID 的生成与 PriceCalendarPanel 中的方式一致（使用 sort()）
          const skuId = specIds.sort().join('-')
          allDates.forEach(date => {
            const priceKey = `${skuId}_${date}`
            const priceObj = tempPriceData[priceKey]
            // 调试日志：显示 key 匹配情况
            if (priceObj) {
              console.log(`✅ 找到价格数据 - priceKey: ${priceKey}, 价格: ${priceObj.adultPrice}`)
            } else {
              console.log(`❌ 未找到价格数据 - priceKey: ${priceKey}, 使用默认值 0`)
            }
            const finalPriceObj = priceObj || {
              calendarDate: date,
              adultPrice: 0,
              childPrice: 0,
              stock: 0,
              commission: 0,
              status: 1,
              saleNum: 0,
              specValueIds: specIds.sort(), // 这里也要排序
              skuId: skuId,
              skuName: skuName
            }
            priceList.push({ ...finalPriceObj })
          })
        })
      }

      // 获取SKU名称
      let skuName = ''
      if (this.modelType === 'unified') {
        if (priceList.length > 0 && priceList[0].skuName) {
          skuName = priceList[0].skuName
        }
        priceList.forEach(item => {
          delete item.skuName
        })
      }
      // 对 specValueChanges 进行去重处理
      const uniqueSpecValueChanges = this.deduplicateSpecValueChanges(this.tempSpecData.specValueChanges)
      const dto = {
        specificationType: this.modelType === 'unified' ? '0' : '1',
        goodsId: this.form.goodsId,
        skuName: this.modelType === 'unified' ? skuName : undefined,
        priceList: priceList,
        specChanges: {
          addedSpecs: this.tempSpecData.addedSpecs,
          removedSpecs: this.tempSpecData.removedSpecs,
          specValueChanges: uniqueSpecValueChanges
        }
      }
      batchSaveCalendarPrices(dto)
        .then(res => {
          if (res.code === 200) {
            this.$message.success('保存成功')
            // 清空临时数据
            this.tempPriceData = {}
            this.tempSpecData = {
              addedSpecs: [],
              removedSpecs: [],
              addedValues: [],
              removedValues: [],
              specValueChanges: []
            }
            this.hasUnsavedChanges = false
            this.loadCalendarData()
            this.$emit('refresh')
          } else {
            this.$message.error(res.msg || '保存失败')
          }
        })
        .catch(err => {
          console.error('保存失败:', err)
          this.$message.error('保存失败')
        })
    }
  }
}
</script>

<style scoped>
.specification-container {
  position: relative;
  min-height: 100%;
}

.save-button-card {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.save-button-card .el-button {
  width: 100%;
}
</style>
