<template>
  <div class="goods-spu-container">
    <el-form ref="form" :rules="rules" :model="localForm" label-position="top" class="form-container">
      <!-- 基本信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">基本信息</h3>
        </div>
        <div class="card-content">
          <div class="form-grid">
            <div class="form-item-wrapper">
              <el-form-item label="景区名称" prop="spotName" class="form-item">
                <el-input v-model="localForm.spotName" placeholder="请输入景区名称" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="商品名称" prop="name" class="form-item">
                <el-input v-model="localForm.name" placeholder="请输入商品名称" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>

            <div class="form-item-wrapper">
              <el-form-item label="商品分类" prop="categoryFirst" class="form-item">
                <el-select v-model="localForm.categoryFirst" placeholder="请选择商品分类" class="form-select" @change="updateForm">
                  <el-option
                    v-for="item in categoryList"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="出发地" prop="startingPoint" class="form-item">
                <el-cascader
                  v-model="localForm.startingPoint"
                  :options="startingPointOptions"
                  :props="{ value: 'id', label: 'name', emitPath: true }"
                  placeholder="请选择出发地"
                  class="form-cascader"
                  @change="handleStartingPointChange"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="目的地" prop="dest" class="form-item">
                <el-cascader
                  v-model="localForm.dest"
                  :value="localForm.startingPoint"
                  :options="startingPointOptions"
                  :props="{ value: 'id', label: 'name' }"
                  placeholder="请选择目的地"
                  class="form-cascader"
                  @change="updateForm"
                />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="乘车点" class="form-item">
                <el-select v-model="localForm.boardingPoint" placeholder="请选择乘车点" class="form-select" @change="updateForm">
                  <el-option v-for="item in meetingPointList" :key="item.id" :label="item.address" :value="item.id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="联系电话" prop="phone" class="form-item">
                <el-input v-model="localForm.phone" placeholder="请输入联系电话" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="卖点" class="form-item">
                <el-input v-model="localForm.sellPoint" placeholder="请输入产品卖点，多个卖点用逗号分隔" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="上下架设置:" class="form-item shelf-form-item">
                <el-radio-group
                  v-model="localForm.shelf"
                  class="shelf-radio-group"
                  @change="updateForm"
                >
                  <el-radio label="1">上架售卖</el-radio>
                  <el-radio label="0">暂不上架</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="小程序显示设置:" class="form-item shelf-form-item">
                <el-radio-group
                  v-model="localForm.isShow"
                  class="shelf-radio-group"
                  @change="updateForm"
                >
                  <el-radio label="1">显示</el-radio>
                  <el-radio label="0">隐藏</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品图片卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">线路图片</h3>
        </div>
        <div class="card-content">
          <!-- 封面图 -->
          <el-form-item prop="coverImage" label="封面图（限1张）" class="upload-form-item">
            <el-upload
              :http-request="(item) => httpRequest(item, '0')"
              action=""
              list-type="picture-card"
              :limit="1"
              :file-list="localForm.coverImage"
              :props="{
                thumbUrlKey: 'fileUrl',
                urlKey: 'fileUrl'
              }"
              :on-preview="handlePictureCardPreview"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'coverImage')"
              class="upload-container"
            >
              <i class="el-icon-plus upload-icon" />
            </el-upload>
          </el-form-item>

          <!-- 轮播图 -->
          <el-form-item prop="carouselImages" label="线路详情轮播图" class="upload-form-item">
            <el-upload
              :http-request="(item) => httpRequest(item, '1')"
              action=""
              list-type="picture-card"
              :limit="9"
              :file-list="localForm.carouselImages"
              :props="{
                thumbUrlKey: 'fileUrl',
                urlKey: 'fileUrl'
              }"
              :on-preview="handlePictureCardPreview"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'carouselImages')"
              class="upload-container"
            >
              <i class="el-icon-plus upload-icon" />
            </el-upload>
          </el-form-item>

          <el-dialog
            :modal="false"
            :visible.sync="dialogVisible"
            width="auto"
            top="5vh"
            class="simple-preview-dialog"
          >
            <div class="image-container">
              <img :src="dialogImageUrl" class="preview-image" @click="dialogVisible = false">
            </div>
          </el-dialog>
        </div>
      </div>

      <!-- 内容详情卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">线路介绍</h3>
        </div>
        <div class="card-content">
          <div class="editor-wrapper">
            <el-form-item label="线路文字介绍" prop="description" class="editor-form-item">
              <el-input v-model="localForm.description" type="textarea" :rows="3" placeholder="请输入线路文字介绍" class="form-input" @input="updateForm" />
            </el-form-item>
          </div>

          <!-- 线路介绍图 -->
          <el-form-item prop="itineraryImages" label="线路介绍图" class="upload-form-item">
            <el-upload
              :http-request="(item) => httpRequest(item, '2')"
              action=""
              list-type="picture-card"
              :limit="9"
              :file-list="localForm.itineraryImages"
              :props="{
                thumbUrlKey: 'fileUrl',
                urlKey: 'fileUrl'
              }"
              :on-preview="handlePictureCardPreview"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'itineraryImages')"
              class="upload-container"
            >
              <i class="el-icon-plus upload-icon" />
            </el-upload>
          </el-form-item>
        </div>
      </div>

      <!-- 服务详情卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">费用详情</h3>
        </div>
        <div class="card-content flex justify-between">
          <div class="card-content-section">
            <div class="card-content-section-title">成人</div>
            <div class="form-item-wrapper">
              <el-form-item label="交通" class="form-item">
                <el-input v-model="adultCostDetail.transportDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入交通情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="住宿" class="form-item">
                <el-input v-model="adultCostDetail.stayDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入住宿情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="用餐" class="form-item">
                <el-input v-model="adultCostDetail.mealsDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入用餐情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="门票" class="form-item">
                <el-input v-model="adultCostDetail.ticketsDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入门票情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="导服" class="form-item">
                <el-input v-model="adultCostDetail.guideDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入导服情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
          </div>
          <div class="card-content-section">
            <div class="card-content-section-title">儿童</div>
            <div class="form-item-wrapper">
              <el-form-item label="交通" class="form-item">
                <el-input v-model="childCostDetail.transportDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入交通情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="住宿" class="form-item">
                <el-input v-model="childCostDetail.stayDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入住宿情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="用餐" class="form-item">
                <el-input v-model="childCostDetail.mealsDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入用餐情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="门票" class="form-item">
                <el-input v-model="childCostDetail.ticketsDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入门票情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
            <div class="form-item-wrapper">
              <el-form-item label="导服" class="form-item">
                <el-input v-model="childCostDetail.guideDesc" type="textarea" :rows="3" maxlength="300" show-word-limit placeholder="请输入导服情况" class="form-input" @input="updateForm" />
              </el-form-item>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" size="medium" class="save-button" @click="submitForm('form')">
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  getCategoryFirst,
  getPointList,
  getMeetingPoint,
  addObj,
  uploadFile,
  updateImageObjectId,
  deleteImages
} from '@/api/mall/goodsspu'

export default {
  name: 'GoodsSpuInfo',
  props: {
    /* 编辑器的内容 */
    form: {
      type: Object,
      default: () => ({})
    },
    closeMethod: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      localForm: {
        // 基本信息
        id: '', // 商品ID
        spotName: '',
        name: '',
        categoryFirst: '',
        startingPoint: [],
        dest: [],
        boardingPoint: '',
        phone: '',
        sellPoint: '',
        shelf: '1',
        isShow: '1',
        // 图片分类
        coverImage: [], // 封面图
        carouselImages: [], // 轮播图
        itineraryImages: [], // 线路介绍图
        // 文字介绍
        description: '', // 线路文字介绍
        mealsInfo: '', // 保留兼容性
        // 费用详情
        costDetails: []
      },
      // 费用详情的响应式数据
      adultCostDetail: {
        passengerType: 'adult',
        transportDesc: '',
        stayDesc: '',
        mealsDesc: '',
        ticketsDesc: '',
        guideDesc: ''
      },
      childCostDetail: {
        passengerType: 'child',
        transportDesc: '',
        stayDesc: '',
        mealsDesc: '',
        ticketsDesc: '',
        guideDesc: ''
      },
      dialogImageUrl: '',
      dialogVisible: false,
      categoryList: [],
      startingPointOptions: [],
      meetingPointList: [],
      rules: {
        name: [{
          required: true,
          message: '请输入商品名称',
          trigger: 'blur'
        }],
        categoryFirst: [{
          required: true,
          message: '请选择商品分类',
          trigger: 'change'
        }],
        phone: [{
          required: true,
          message: '请输入联系方式',
          trigger: 'blur'
        }],
        startingPoint: [{
          required: true,
          message: '请选择出发地',
          trigger: ['change', 'blur'],
          validator: (rule, value, callback) => {
            if (!value || (Array.isArray(value) && value.length === 0)) {
              callback(new Error('请选择出发地'))
            } else {
              callback()
            }
          }
        }],
        dest: [{
          required: true,
          message: '请选择目的地',
          trigger: 'change'
        }],
        coverImage: [{
          required: true,
          message: '请选择封面图',
          trigger: 'change'
        }]
      }
    }
  },
  watch: {
    form: {
      handler(newVal) {
        if (newVal) {
          this.initLocalForm()
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.loadBasicData()
  },
  mounted() {
    // 延迟重置抽屉滚动位置，等待组件完全加载
    setTimeout(() => {
      this.resetScrollPosition()
    }, 300)
  },
  methods: {
    /**
     * 初始化本地表单数据
     */
    initLocalForm() {
      if (this.form) {
        // 复制基本数据
        Object.keys(this.localForm).forEach(key => {
          if (this.form[key] !== undefined) {
            this.localForm[key] = this.form[key]
          }
        })

        // 兼容性处理：如果没有description但有mealsInfo，使用mealsInfo
        if (!this.localForm.description && this.form.mealsInfo) {
          this.localForm.description = this.form.mealsInfo
        }

        // 处理费用详情数据
        this.initializeCostDetails()

        // 处理已有图片数据，按类型分类
        this.categorizeImages()
      }
    },

    /**
     * 初始化费用详情数据
     */
    initializeCostDetails() {
      // 重置费用详情数据
      this.adultCostDetail = {
        passengerType: 'adult',
        transportDesc: '',
        stayDesc: '',
        mealsDesc: '',
        ticketsDesc: '',
        guideDesc: ''
      }
      this.childCostDetail = {
        passengerType: 'child',
        transportDesc: '',
        stayDesc: '',
        mealsDesc: '',
        ticketsDesc: '',
        guideDesc: ''
      }

      // 如果有费用详情数据，填充到对应字段
      if (this.form.costDetails && Array.isArray(this.form.costDetails)) {
        this.form.costDetails.forEach(detail => {
          if (detail.passengerType === 'adult') {
            this.adultCostDetail = { ...detail }
          } else if (detail.passengerType === 'child') {
            this.childCostDetail = { ...detail }
          }
        })
      }

      console.log('费用详情初始化完成:', {
        成人: this.adultCostDetail,
        儿童: this.childCostDetail
      })
    },

    /**
     * 将现有图片按类型分类
     */
    categorizeImages() {
      if (this.form.picUrls && Array.isArray(this.form.picUrls)) {
        console.log('开始分类图片，总数量:', this.form.picUrls.length)
        this.localForm.coverImage = []
        this.localForm.carouselImages = []
        this.localForm.itineraryImages = []

        this.form.picUrls.forEach(item => {
          const imageData = {
            ...item,
            url: item.fileUrl,
            thumbUrl: item.fileUrl
          }

          // 根据objectType分类，默认视为封面图
          const objectType = item.objectType || '0'
          console.log('图片分类:', {
            fileName: item.fileName,
            objectType: objectType,
            fileUrl: item.fileUrl
          })
          switch (objectType) {
            case '0':
              this.localForm.coverImage.push(imageData)
              break
            case '1':
              this.localForm.carouselImages.push(imageData)
              break
            case '2':
              this.localForm.itineraryImages.push(imageData)
              break
            default:
              console.warn('未知图片类型，归类为封面图:', objectType)
              this.localForm.coverImage.push(imageData)
          }
        })
        console.log('图片分类结果:', {
          封面图: this.localForm.coverImage.length,
          轮播图: this.localForm.carouselImages.length,
          线路介绍图: this.localForm.itineraryImages.length
        })
      } else {
        console.log('没有找到图片数据或数据格式不正确')
      }
    },

    /**
     * 加载基础数据
     */
    loadBasicData() {
      getCategoryFirst().then(data => {
        this.categoryList = data.data
      })
      getPointList().then(data => {
        this.startingPointOptions = data.data
      })
      getMeetingPoint().then(data => {
        this.meetingPointList = data.rows
      })
    },

    /**
     * 更新表单数据到父组件
     */
    updateForm() {
      this.$nextTick(() => {
        // 组装费用详情数据
        const costDetails = []

        // 添加成人费用详情（只有在有内容时才添加）
        if (this.adultCostDetail && (
          this.adultCostDetail.transportDesc ||
          this.adultCostDetail.stayDesc ||
          this.adultCostDetail.mealsDesc ||
          this.adultCostDetail.ticketsDesc ||
          this.adultCostDetail.guideDesc
        )) {
          costDetails.push({ ...this.adultCostDetail })
        }

        // 添加儿童费用详情（只有在有内容时才添加）
        if (this.childCostDetail && (
          this.childCostDetail.transportDesc ||
          this.childCostDetail.stayDesc ||
          this.childCostDetail.mealsDesc ||
          this.childCostDetail.ticketsDesc ||
          this.childCostDetail.guideDesc
        )) {
          costDetails.push({ ...this.childCostDetail })
        }

        // 更新localForm的costDetails
        this.localForm.costDetails = costDetails

        // 传递本地表单数据
        const updatedForm = {
          ...this.localForm
        }

        this.$emit('update:form', updatedForm)
      })
    },

    /**
     * 提交表单
     */
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 准备提交数据，不再传递picUrls，图片数据通过分类字段传递
          const submitData = {
            ...this.localForm
          }

          // 移除图片数组字段，这些是前端UI用的，不需要传给后端
          delete submitData.coverImage
          delete submitData.carouselImages
          delete submitData.itineraryImages

          console.log('提交数据:', submitData)

          addObj(submitData).then(data => {
            console.log('商品保存成功，返回数据:', data)

            // 保存成功后，处理图片关联和删除操作
            this.handlePostSaveImageOperations(data)
          }).catch(error => {
            this.$message.error('保存失败：' + error.message)
          })
        } else {
          this.$message.error('请完善必填信息')
          return false
        }
      })
    },

    /**
     * 处理保存后的图片操作（关联）
     */
    async handlePostSaveImageOperations(saveResult) {
      try {
        // 处理新增商品的图片关联
        if (saveResult.data && saveResult.data.id && !this.localForm.id) {
          await this.handleNewProductImageAssociation(saveResult.data.id)
        }

        this.$message.success('保存成功')
        this.closeMethod()
      } catch (error) {
        console.error('后续操作失败:', error)
        this.$message.warning('商品保存成功，但后续操作有问题：' + error.message)
        this.closeMethod()
      }
    },

    /**
     * 处理新增商品的图片关联
     */
    async handleNewProductImageAssociation(goodsId) {
      const allImageIds = []

      // 收集所有已上传的图片ID
      this.localForm.coverImage.forEach(img => {
        if (img.id) allImageIds.push(img.id)
      })
      this.localForm.carouselImages.forEach(img => {
        if (img.id) allImageIds.push(img.id)
      })
      this.localForm.itineraryImages.forEach(img => {
        if (img.id) allImageIds.push(img.id)
      })

      if (allImageIds.length > 0) {
        console.log('开始批量更新图片关联，图片数量:', allImageIds.length, '商品ID:', goodsId)
        const updateResult = await updateImageObjectId(allImageIds.join(','), goodsId)
        console.log('图片关联更新成功:', updateResult)
      }
    },
    /**
     * 图片上传请求
     */
    httpRequest(item, objectType) {
      const data = {
        fileName: '',
        fileUrl: '',
        id: '',
        thumbUrl: '',
        url: '',
        name: '',
        status: 'success',
        objectType: objectType
      }
      data.fileName = item.file.name
      data.name = item.file.name

      const formData = new FormData()
      formData.append('file', item.file)
      formData.append('fileName', item.file.name)
      formData.append('objectType', objectType)
      if (this.localForm.id) {
        formData.append('objectId', this.localForm.id)
      }

      uploadFile(formData)
        .then(res => {
          const fileUrl = res.data.fileUrl
          data.fileUrl = fileUrl
          data.thumbUrl = fileUrl
          data.url = fileUrl
          data.id = res.data.id

          // 设置文件的 url 和 thumbUrl 属性
          item.file.url = fileUrl
          item.file.thumbUrl = fileUrl
          item.file.status = 'success'

          // 根据图片类型添加到对应数组
          switch (objectType) {
            case '0':
              this.localForm.coverImage.push(data)
              break
            case '1':
              this.localForm.carouselImages.push(data)
              break
            case '2':
              this.localForm.itineraryImages.push(data)
              break
          }

          this.updateForm()
        })
        .catch((error) => {
          this.$message.error('上传失败：' + error.message)
        })
    },

    /**
     * 删除图片（立即删除）
     */
    async handleRemove(file, fileList, imageType) {
      console.log('删除图片:', file)

      // 如果删除的是已保存的图片（有ID），立即执行删除操作
      if (file.id) {
        try {
          console.log('立即删除图片，ID:', file.id)
          await deleteImages(file.id)
          console.log('图片删除成功:', file.id)
          this.$message.success('图片删除成功')
        } catch (error) {
          console.error('删除图片失败:', error)
          this.$message.error('删除图片失败：' + error.message)
          return // 删除失败时不更新本地显示
        }
      }

      // 更新本地显示
      this.localForm[imageType] = fileList
      this.updateForm()
    },

    /**
     * 预览图片
     */
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.fileUrl
      this.dialogVisible = true
      // 重置页面滚动位置到顶部
      this.$nextTick(() => {
        window.scrollTo(0, 0)
        document.documentElement.scrollTop = 0
        document.body.scrollTop = 0
      })
    },

    /**
     * 出发地变化处理
     */
    handleStartingPointChange(value) {
      console.log('出发地选择变化:', value)
      this.updateForm()
    },

    /**
     * 重置滚动位置
     */
    resetScrollPosition() {
      // 查找当前组件所在的抽屉容器并重置滚动位置
      const currentElement = this.$el
      if (currentElement) {
        let parent = currentElement.parentElement
        while (parent) {
          if (parent.classList.contains('el-drawer__body') ||
            parent.classList.contains('el-drawer__container') ||
            parent.classList.contains('el-drawer')) {
            parent.scrollTop = 0
            break
          }
          parent = parent.parentElement
        }
      }

      // 备用方案：直接查找抽屉容器
      const drawerBody = document.querySelector('.el-drawer__body')
      if (drawerBody) {
        drawerBody.scrollTop = 0
      }
    }
  }
}
</script>

<style scoped>
.goods-spu-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding: 24px;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
  overflow: hidden;
}

.card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.card-content {
  padding: 24px;
  gap: 20px;
}

.card-content-section {
  flex: 1;
}

.card-content-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
}

.form-item-wrapper {
  margin-bottom: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.form-grid:last-child {
  margin-bottom: 0;
}

.form-item-wrapper {
  width: 100%;
}

.form-item-full {
  margin-bottom: 24px;
}

.form-item-full:last-child {
  margin-bottom: 0;
}

.form-item {
  margin-bottom: 0;
}

.form-item >>> .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  line-height: 20px;
  padding-bottom: 8px;
}

.form-input,
.form-select,
.form-cascader {
  width: 100%;
}

.form-input >>> .el-input__inner,
.form-select >>> .el-input__inner,
.form-cascader >>> .el-input__inner {
  height: 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input >>> .el-input__inner:focus,
.form-select >>> .el-input__inner:focus,
.form-cascader >>> .el-input__inner:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.upload-form-item {
  margin-bottom: 24px;
}

.upload-container >>> .el-upload--picture-card {
  width: 128px;
  height: 128px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  transition: border-color 0.2s ease;
}

.upload-container >>> .el-upload--picture-card:hover {
  border-color: #9ca3af;
}

.upload-container >>> .el-upload-list--picture-card .el-upload-list__item {
  width: 128px;
  height: 128px;
  border-radius: 8px;
}

.upload-icon {
  font-size: 32px;
  color: #9ca3af;
}

.editor-wrapper {
  margin-bottom: 24px;
}

.editor-wrapper:last-child {
  margin-bottom: 0;
}

.editor-form-item {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding-top: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.save-button {
  padding: 12px 32px;
  font-size: 14px;
  font-weight: 500;
  background-color: #ef4444;
  border-color: #ef4444;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.save-button:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .goods-spu-container {
    padding: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .card-content {
    padding: 16px;
  }
}

/* 图片预览对话框样式 */
.simple-preview-dialog >>> .el-dialog {
  max-width: 90%;
  background: transparent;
  box-shadow: none;
}

.simple-preview-dialog >>> .el-dialog__header {
  display: none;
}

.simple-preview-dialog >>> .el-dialog__body {
  padding: 0;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
}

.preview-image {
  max-width: 100%;
  max-height: 85vh;
  object-fit: contain;
  cursor: zoom-out;
  border-radius: 4px;
}

.shelf-form-item {
  display: flex;
  align-items: center;
}

.shelf-form-item >>> .el-form-item__label {
  width: auto;
  margin-right: 24px;
  line-height: 32px;
}

.shelf-form-item >>> .el-form-item__content {
  flex: 1;
}

.shelf-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shelf-radio-group >>> .el-radio {
  margin-right: 0;
  font-size: 14px;
  color: #606266;
}

.shelf-radio-group >>> .el-radio__input.is-checked .el-radio__inner {
  background-color: #409EFF;
  border-color: #409EFF;
}

.shelf-radio-group >>> .el-radio__label {
  font-size: 14px;
  color: #606266;
}

.shelf-radio-group >>> .el-radio.is-checked .el-radio__label {
  color: #409EFF;
}
</style>
