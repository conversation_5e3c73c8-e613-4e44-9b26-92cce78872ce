<template>
  <div class="jl-search-form">
    <!-- 按钮组固定在右上角 -->
    <div class="button-group-fixed">
      <el-button type="primary" size="small" @click="onSearch">搜索</el-button>
      <el-button size="small" @click="onReset">重置</el-button>
      <el-button type="text" size="small" @click="toggleExpand" class="expand-btn">
        {{ isExpand ? '收起' : '展开' }}
        <i :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
      </el-button>
    </div>

    <el-form
      :model="formData"
      :inline="false"
      label-position="left"
      class="jl-search-form-el"
      @keyup.enter.native="onSearch"
    >
      <el-row :gutter="12">
        <el-col v-for="item in visibleFields" :key="item.prop" :span="item.span || 8">
          <el-form-item :label="item.label" :prop="item.prop" class="search-form-item">
            <template v-if="item.type === 'daterange'">
              <el-date-picker
                v-model="formData[item.prop]"
                v-bind="item.attrs"
                :placeholder="item.placeholder"
                style="width: 100%"
              />
            </template>
            <template v-else>
              <component
                :is="item.type === 'select' ? 'el-select' : 'el-input'"
                v-model="formData[item.prop]"
                v-bind="item.attrs"
                :placeholder="item.placeholder"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="opt in item.options"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                />
              </component>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'JlSearchForm',
  props: {
    fields: { // [{label, prop, type, options, placeholder, span, attrs}]
      type: Array,
      required: true
    },
    value: {
      type: Object,
      default: () => ({})
    },
    labelWidth: {
      type: String,
      default: 'auto'
    },
    isExpand: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: this.initFormData(this.value, this.fields)
    }
  },
  computed: {
    visibleFields() {
      return this.fields.filter(item => !item.expand || this.isExpand)
    }
  },
  watch: {
    value: {
      handler(val) {
        this.formData = this.initFormData(val, this.fields)
      },
      deep: true
    },
    fields: {
      handler() {
        this.formData = this.initFormData(this.value, this.fields)
      },
      deep: true
    }
  },
  mounted() {
    // 移除 ElementUI 可能设置的内联 margin-left 样式
    this.$nextTick(() => {
      this.removeInlineMarginLeft()
    })
  },
  updated() {
    // 在组件更新后也移除内联样式
    this.$nextTick(() => {
      this.removeInlineMarginLeft()
    })
  },
  methods: {
    removeInlineMarginLeft() {
      const contentElements = this.$el.querySelectorAll('.el-form-item__content')
      contentElements.forEach(el => {
        el.style.marginLeft = ''
      })
    },
    initFormData(val, fields) {
      const data = { ...val }
      fields.forEach(f => {
        if (f.type === 'daterange' && f.startProp && f.endProp) {
          data[f.prop] = val[f.startProp] && val[f.endProp] ? [val[f.startProp], val[f.endProp]] : []
        }
      })
      return data
    },
    onSearch() {
      // 拆分daterange
      const result = { ...this.formData }
      this.fields.forEach(f => {
        if (f.type === 'daterange' && f.startProp && f.endProp) {
          const range = this.formData[f.prop] || []
          result[f.startProp] = range[0] || ''
          result[f.endProp] = range[1] || ''
          delete result[f.prop]
        }
      })
      this.$emit('search', result)
    },
    onReset() {
      const resetObj = {}
      this.fields.forEach(f => {
        if (f.type === 'daterange' && f.startProp && f.endProp) {
          resetObj[f.prop] = []
          resetObj[f.startProp] = ''
          resetObj[f.endProp] = ''
        } else {
          resetObj[f.prop] = ''
        }
      })
      this.formData = resetObj
      // 拆分daterange
      const result = { ...resetObj }
      this.fields.forEach(f => {
        if (f.type === 'daterange' && f.startProp && f.endProp) {
          result[f.startProp] = ''
          result[f.endProp] = ''
          delete result[f.prop]
        }
      })
      this.$emit('reset', result)
    },
    toggleExpand() {
      this.$emit('toggle-expand')
    }
  }
}
</script>

<style scoped>
/* 搜索表单容器，避免全局样式干扰 */
.jl-search-form {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
  padding: 20px 16px 8px 16px;
  margin-bottom: 16px;
  position: relative;
  /* 重要：覆盖全局样式 */
  box-sizing: border-box !important;
}

/* 固定按钮组在右上角 - 优化紧凑布局 */
.button-group-fixed {
  position: absolute;
  top: 20px;
  right: 16px;
  display: flex;
  align-items: center;
  z-index: 10;
  gap: 8px; /* 使用gap替代margin-left，更现代化 */
}

.button-group-fixed .el-button {
  height: 32px !important;
  line-height: 30px !important;
  padding: 0 15px !important;
  margin: 0 !important; /* 清除默认margin，使用gap控制间距 */
}

.button-group-fixed .el-button--primary {
  background-color: #F5222D !important;
  border-color: #F5222D !important;
}

.button-group-fixed .el-button--primary:hover {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

/* 展开/收起按钮样式优化 - 更紧凑 */
.button-group-fixed .expand-btn {
  color: #666 !important;
  height: 32px !important;
  line-height: 30px !important;
  padding: 0 8px !important; /* 减少左右padding */
  margin-left: 4px !important; /* 与前面按钮更近 */
  font-size: 13px !important; /* 稍微缩小字体 */
}

.button-group-fixed .expand-btn:hover {
  color: #F5222D !important;
}

.button-group-fixed .expand-btn i {
  margin-left: 2px !important;
  font-size: 12px !important;
}

/* 表单样式重置，避免全局冲突 */
.jl-search-form .jl-search-form-el {
  /* 重置全局 el-form 样式 */
  margin: 0 !important;
  padding: 0 !important;
  /* 为按钮组留出空间，减少右边距 */
  padding-right: 240px !important;
}

/* 表单项样式 - 优化为flex布局，标签宽度自适应 */
.jl-search-form .jl-search-form-el .el-form-item {
  margin-bottom: 18px !important;
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  flex-wrap: nowrap !important;
}

/* 完全重置标签包装器 */
.jl-search-form .jl-search-form-el .el-form-item__label-wrap {
  margin: 0 !important;
  width: auto !important;
  flex-shrink: 0 !important;
}

/* 强制覆盖 ElementUI 可能设置的 margin-left */
.jl-search-form .jl-search-form-el .el-form-item--small .el-form-item__label,
.jl-search-form .jl-search-form-el .el-form-item--medium .el-form-item__label,
.jl-search-form .jl-search-form-el .el-form-item__label {
  margin-left: 0 !important;
}

.jl-search-form .jl-search-form-el .el-form-item--small .el-form-item__content,
.jl-search-form .jl-search-form-el .el-form-item--medium .el-form-item__content,
.jl-search-form .jl-search-form-el .el-form-item__content {
  margin-left: 0 !important;
}

/* 表单标签样式 - 自适应宽度，不换行 */
.jl-search-form .jl-search-form-el ::v-deep .el-form-item__label {
  font-weight: 500 !important;
  color: #333 !important;
  text-align: left !important;
  padding: 0 8px 0 0 !important; /* 增加右边距 */
  margin: 0 !important;
  line-height: 32px !important;
  white-space: nowrap !important; /* 强制不换行 */
  width: auto !important; /* 自适应宽度 */
  min-width: auto !important; /* 移除最小宽度限制 */
  max-width: none !important; /* 移除最大宽度限制 */
  flex-shrink: 0 !important; /* 不允许收缩 */
  word-break: keep-all !important;
  overflow: visible !important; /* 允许内容显示 */
  display: inline-block !important; /* 确保宽度自适应 */
}

/* 表单控件容器 - 占据剩余空间 */
.jl-search-form .jl-search-form-el ::v-deep .el-form-item__content {
  flex: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  text-align: left !important;
  min-width: 0 !important;
  width: auto !important;
}

/* 输入框和选择器统一样式 */
.jl-search-form .jl-search-form-el .el-input,
.jl-search-form .jl-search-form-el .el-select {
  width: 100% !important;
}

.jl-search-form .jl-search-form-el .el-input__inner {
  width: 100% !important;
  height: 32px !important;
  line-height: 32px !important;
  border-radius: 4px !important;
  border: 1px solid #dcdfe6 !important;
}

.jl-search-form .jl-search-form-el .el-input__inner:focus {
  border-color: #F5222D !important;
}

/* 选择器样式 */
.jl-search-form .jl-search-form-el .el-select .el-input__inner {
  cursor: pointer;
}

/* 日期选择器样式 */
.jl-search-form .jl-search-form-el .el-date-editor {
  width: 100% !important;
}

.jl-search-form .jl-search-form-el .el-date-editor .el-input__inner {
  width: 100% !important;
}

/* 行间距优化 - 减少左右间距 */
.jl-search-form .el-row {
  margin-left: -6px !important;
  margin-right: -6px !important;
}

.jl-search-form .el-col {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .jl-search-form {
    padding: 16px 12px 8px 12px;
  }

  .button-group-fixed {
    top: 16px;
    right: 12px;
  }

  .jl-search-form .jl-search-form-el {
    padding-right: 220px !important;
  }
}

@media (max-width: 992px) {
  /* 中等屏幕时每行显示2个 */
  .jl-search-form .el-col {
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }
}

@media (max-width: 768px) {
  .jl-search-form {
    padding: 12px 8px 8px 8px;
  }

  /* 小屏幕时按钮组移到下方 */
  .button-group-fixed {
    position: static;
    margin-top: 12px;
    justify-content: center;
  }

  .jl-search-form .jl-search-form-el {
    padding-right: 0 !important;
  }

  .jl-search-form .jl-search-form-el .el-form-item__label {
    padding: 0 4px 0 0 !important;
  }

  .jl-search-form .jl-search-form-el .el-form-item__content {
    min-width: 120px !important;
  }

  /* 小屏幕时每行显示1个 */
  .jl-search-form .el-col {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
}
</style>
