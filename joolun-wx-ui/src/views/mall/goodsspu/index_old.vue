<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，项目使用请保留此说明
-->
<template>
  <div class="app-container">
    <avue-crud ref="crud" :page="page" :data="tableData" :permission="permissionList" :table-loading="tableLoading"
      :option="tableOption" :before-open="beforeOpen" v-model="form" :searchShow="true" :searchShowBtn="false"
      @on-load="getPage" @refresh-change="refreshChange" @row-update="handleUpdate" @row-save="handleSave"
      @sort-change="sortChange" @search-change="searchChange" @selection-change="selectionChange">
      <template slot="detailListForm" slot-scope="scope">
        <custom-dynamic :dataFroms="scope.row.detailList" :goodsSpecs="scope.row.goodsSpecs" :label="scope.label"></custom-dynamic>
      </template>
      <template slot="goodsSpecsForm" slot-scope="scope">
        <GoodsSpec :dataFroms="scope.row.goodsSpecs" ></GoodsSpec>
      </template>

      <template slot="menuLeft">

      </template>
      <template slot="picUrls" slot-scope="scope">
        <el-image style="width: 100px; height: 100px" :src="scope.row.picUrls[0]" :preview-src-list="scope.row.picUrls">
        </el-image>
      </template>
      <template slot="rqCode" slot-scope="scope">
        <el-image style="width: 100px; height: 100px" :src="scope.row.rqCode">
        </el-image>
      </template>

      <template slot="salesPrice" slot-scope="scope">
        <div style="color: red">￥{{scope.row.salesPrice}}</div>
      </template>
      <template slot="shelf" slot-scope="scope">
        <el-switch active-value="1" inactive-value="0" v-model="scope.row.shelf" active-color="#13ce66"
          inactive-color="#ff4949" @change="changeShelf(scope.row)">
        </el-switch>
      </template>
      <template slot="isShow" slot-scope="scope">
        <el-switch active-value="1" inactive-value="0" v-model="scope.row.isShow" active-color="#1E90FF"
          inactive-color="#778899" @change="changeShow(scope.row)">
        </el-switch>
      </template>
      <template slot="descriptionForm" slot-scope="scope">
        <BaseEditor v-model="scope.row.description" />
      </template>
      <template slot="costIntroForm" slot-scope="scope">
        <BaseEditor v-model="scope.row.costIntro" />
      </template>
      <template slot="remindFrom" slot-scope="scope">
        <BaseEditor v-model="scope.row.remind" />
      </template>
      <template slot="picUrls" slot-scope="scope">
        <el-image style="width: 100px; height: 100px" :src="scope.row.picUrls?scope.row.picUrls[0]:''"
          :preview-src-list="scope.row.picUrls">
        </el-image>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button icon="el-icon-edit" size="small" type="text" @click="showXcxMa(scope.row,scope.index)">小程序码
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="小程序码" append-to-body :visible.sync="xcxMaShow" width="500px">
      <p>
        <img style="width:400px;margin-right:10px" :src="xcxMa" @click="openPreview(index)">
      </p>
    </el-dialog>
  </div>


</template>

<script>
  import {
    checkPermi,
    checkRole
  } from "@/utils/permission"
  import {
    getPage,
    getObj,
    addObj,
    putObj,
    delObj,
    putObjShelf,
    getTreeNotZero,
    putObjIsShow,
    getRQCode
  } from '@/api/mall/goodsspu'
  import {
    tableOption
  } from '@/const/crud/mall/goodsspu'
  import {
    yry
  } from '@/const/crud/line/yry'
  import {
    mapGetters
  } from 'vuex'
  import BaseEditor from '@/components/Editor/index.vue'
  import CustomDynamic from '@/components/CustomDynamic/CustomDynamic.vue'
  import GoodsSpec from '@/components/GoodsSpec/index.vue'

  export default {
    name: 'goodsspu',
    components: {
      BaseEditor,
      CustomDynamic,
      GoodsSpec
    },
    data() {

      return {
        checkPermi: checkPermi,
        defaults: {},
        form: {
          people: 0
        },
        tableData: [],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20, // 每页显示多少条
          ascs: [], //升序字段
          descs: 'create_time' //降序字段
        },
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption,
        dialogAppraises: false,
        optionAppraises: {
          props: {
            avatar: 'nickName',
            author: 'headimgUrl',
            body: 'content'
          }
        },
        selectionData: '',
        pointsConfig: null,
        xcxMaShow: false,
        xcxMa: ""
      }
    },
    watch: {
      'form.people'(val, oldval) {
        if (val == 3) {
          console.log(111)
          console.log(val)
        }
        console.log(this.form.people)



        console.log(this.$refs.crud)

      }
    },
    created() {
      console.log("created")
    },
    mounted: function() {},
    computed: {
      permissionList() {
        return {
          addBtn: checkPermi(['mall:goodsspu:add']),
          // delBtn: checkPermi(['mall:goodsspu:del']),
          editBtn: checkPermi(['mall:goodsspu:edit']),
          viewBtn: checkPermi(['mall:goodsspu:get'])
        };
      },
    },
    methods: {


      selectionChange(list) {
        this.selectionData = list
      },
      batchShelf(shelf) {
        if (this.selectionData.length <= 0) {
          this.$message.error('请选择商品')
          return
        }
        let selectionIds = ''
        this.selectionData.forEach(item => {
          selectionIds += item.id + ','
        })
        this.putObjShelf(selectionIds, shelf)
      },
      changeShelf(row) {
        this.putObjShelf(row.id, row.shelf)
      },
      changeShow(row) {
        console.log(row)
        this.putObjIsShow(row.id, row.isShow)
      },
      putObjShelf(ids, shelf) {
        putObjShelf({
          ids: ids,
          shelf: shelf
        }).then(data => {
          this.getPage(this.page)
        })
      },
      putObjIsShow(ids, isShow) {
        console.log(isShow)
        // console.log(isShow == '0')
        // if(isShow == '0'){
        //   isShow="1"
        // }else{
        //   isShow="0"
        // }
        putObjIsShow({
          ids: ids,
          isShow: isShow
        }).then(data => {
          this.getPage(this.page)
        })
      },
      beforeOpen(done, type) {
        console.log(type)
        if (type == 'add') {
          done()
        } else if (type == 'edit') {
          this.tableLoading = true
          getObj(this.form.id).then(response => {

            console.log(response)
            // response.data.dest=response.data.dest.split(',');
            // response.data.startingPoint="北京市,东城区,东城"
            // this.$set(this.form,'dest', response.data.dest)
            this.$set(this.form, 'description', response.data.description)
            // this.tableOption.group[0].column[3].rules[0].message="aaa"
            // console.log(this.tableOption.group[0].column[3].rules[0])
            this.tableLoading = false
            done()
          })
        }
      },
      searchChange(params, done) {
        params = this.filterForm(params)
        this.paramsSearch = params
        this.page.currentPage = 1
        this.getPage(this.page, params)
        done()
      },
      sortChange(val) {
        let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : '';
        if (val.order == 'ascending') {
          this.page.descs = []
          this.page.ascs = prop
        } else if (val.order == 'descending') {
          this.page.ascs = []
          this.page.descs = prop
        } else {
          this.page.ascs = []
          this.page.descs = []
        }
        this.getPage(this.page)
      },
      getPage(page, params) {
        this.tableLoading = true
        // if (this.paramsSearch.categoryId) {
        //     this.paramsSearch.categoryFirst = this.paramsSearch.categoryId[0]
        //     this.paramsSearch.categorySecond = this.paramsSearch.categoryId[1]
        // }
        getPage(Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
        }, params, this.paramsSearch)).then(response => {
          let tableData = response.data.records
          tableData.forEach(function(item, index) {
            let point = []
            item.dest.split(',').forEach((data) => {
              point.push(parseInt(data))
            })
            item.dest = point;
            point = []
            item.startingPoint.split(',').forEach((data) => {
              point.push(parseInt(data))
            })
            item.startingPoint = point;
          })

          this.tableData = tableData
          this.page.total = response.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      },
      /**
       * @title 数据删除
       * @param row 为当前的数据
       * @param index 为当前删除数据的行数
       *
       **/
      handleDel: function(row, index) {
        var _this = this
        this.$confirm('是否确认删除此数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          return delObj(row.id)
        }).then(data => {
          _this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          })
          this.getPage(this.page)
        }).catch(function(err) {})
      },
      /**
       * @title 数据更新
       * @param row 为当前的数据
       * @param index 为当前更新数据的行数
       * @param done 为表单关闭函数
       *
       **/
      handleUpdate: function(row, index, done, loading) {
        // row.categoryFirst = row.categoryId[0]
        // row.categorySecond = row.categoryId[1]
        console.log(row);
        row.destProvince = row.dest[0]
        row.destCity = row.dest[1]
        row.destRegion = row.dest[2]
        row.dest = row.dest.join(",");
        row.startProvince = row.startingPoint[0]
        row.startCity = row.startingPoint[1]
        row.startRegion = row.startingPoint[2]
        row.startingPoint = row.startingPoint.join(",");

        putObj(row).then(data => {
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          })
          done()
          this.getPage(this.page)
        }).catch(() => {
          loading()
        })
      },
      /**
       * @title 数据添加
       * @param row 为当前的数据
       * @param done 为表单关闭函数
       *
       **/
      handleSave: function(row, done, loading) {
        // row.categoryFirst = row.categoryId[0]
        // row.categorySecond = row.categoryId[1]
        console.log(row);
        row.destProvince = row.dest[0]
        row.destCity = row.dest[1]
        row.destRegion = row.dest[2]
        row.dest = row.dest.join(",");
        row.startProvince = row.startingPoint[0]
        row.startCity = row.startingPoint[1]
        row.startRegion = row.startingPoint[2]
        row.startingPoint = row.startingPoint.join(",");
        row.issue = 1; // 默认展示
        addObj(row).then(data => {
          this.$message({
            showClose: true,
            message: '添加成功',
            type: 'success'
          })
          done()
          this.getPage(this.page)
        }).catch(() => {
          loading()
        })
      },
      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPage(this.page)
      },
      /**
       * 生成小程序码
       */
      showXcxMa(row, index) {
        let rqCode = getRQCode(row.id);
        this.xcxMaShow = true;
        this.xcxMa = rqCode;
        this.getPage(this.page)
      }

    }
  }
</script>
