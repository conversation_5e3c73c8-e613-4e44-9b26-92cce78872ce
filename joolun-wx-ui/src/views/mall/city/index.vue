<template>
  <div class="app-container">
    <el-card class="box-card">
      <JlSearchForm
        v-show="showSearch"
        :fields="searchFields"
        :value="queryParams"
        :is-expand="searchExpanded"
        @search="handleQuery"
        @reset="resetQuery"
        @toggle-expand="searchExpanded = !searchExpanded"
      />

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:city:add']"
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:city:edit']"
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:city:remove']"
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:city:export']"
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
          >导出</el-button>
        </el-col>
      </el-row>

      <JlVxeTable
        table-id="city-table"
        :data="cityList"
        :columns="columns"
        :loading="loading"
        :pager="pager"
        :show-toolbar="false"
        :show-pager="true"
        :checkbox-config="{ highlight: true }"
        @checkbox-change="handleSelectionChange"
        @checkbox-all="handleSelectionChange"
        @page-change="handlePageChange"
      >
        <template #operation="{ row }">
          <el-button
            v-hasPermi="['system:city:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['system:city:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >删除</el-button>
        </template>
      </JlVxeTable>
    </el-card>
    <!-- 添加或修改city对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="行政区划代码" prop="code">
          <el-input v-model="form.code" placeholder="请输入行政区划代码" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="上级id" prop="pid">
          <el-input v-model="form.pid" placeholder="请输入上级id" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="热门城市" prop="isHot">
          <el-select v-model="form.isHot" placeholder="请选择是否热门">
            <el-option label="否" value="0" />
            <el-option label="是" value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCity, getCity, delCity, addCity, updateCity, exportCity } from '@/api/mall/city'
import JlSearchForm from '@/components/Form/JlSearchForm.vue'
import JlVxeTable from '@/components/Table/JlVxeTable.vue'

export default {
  name: 'City',
  components: {
    JlSearchForm,
    JlVxeTable
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 搜索展开状态
      searchExpanded: false,
      // 总条数
      total: 0,
      // city表格数据
      cityList: [],
      // pager配置
      pager: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 表格列配置
      columns: [
        { type: 'checkbox', width: 55, align: 'center' },
        { field: 'id', title: 'id', align: 'center' },
        { field: 'code', title: '行政区划代码', align: 'center' },
        { field: 'name', title: '名称', align: 'center' },
        { field: 'pid', title: '上级id', align: 'center' },
        { field: 'type', title: '类型', align: 'center' },
        {
          field: 'isHot',
          title: '热门城市',
          align: 'center',
          formatter: ({ cellValue }) => {
            return cellValue === '1' ? '是' : '否'
          }
        },
        {
          field: 'operation',
          title: '操作',
          align: 'center',
          slots: { default: 'operation' },
          fixed: 'right',
          width: 150
        }
      ],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: '',
        name: '',
        pid: '',
        type: '',
        isHot: ''
      },
      // 搜索字段配置
      searchFields: [
        {
          label: '行政区划代码',
          prop: 'code',
          type: 'input',
          placeholder: '请输入行政区划代码',
          span: 8
        },
        {
          label: '名称',
          prop: 'name',
          type: 'input',
          placeholder: '请输入名称',
          span: 8
        },
        {
          label: '上级id',
          prop: 'pid',
          type: 'input',
          placeholder: '请输入上级id',
          span: 8
        },
        {
          label: '类型',
          prop: 'type',
          type: 'select',
          placeholder: '请选择类型',
          span: 8,
          options: [
            { label: '请选择字典生成', value: '' }
          ]
        },
        {
          label: '热门城市',
          prop: 'isHot',
          type: 'select',
          placeholder: '请选择是否热门',
          span: 8,
          options: [
            { label: '全部', value: '' },
            { label: '是', value: '1' },
            { label: '否', value: '0' }
          ]
        }
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  watch: {
    total(val) {
      this.pager.total = val
    },
    'queryParams.pageNum'(val) {
      this.pager.currentPage = val
    },
    'queryParams.pageSize'(val) {
      this.pager.pageSize = val
    },
    'pager.currentPage'(val) {
      this.queryParams.pageNum = val
    },
    'pager.pageSize'(val) {
      this.queryParams.pageSize = val
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询city列表 */
    getList() {
      this.loading = true
      listCity(this.queryParams).then(response => {
        this.cityList = response.rows
        this.total = response.total
        this.pager.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: '',
        name: '',
        pid: '',
        type: '',
        isHot: '0'
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery(searchParams) {
      if (searchParams) {
        // 从JlSearchForm传来的搜索参数
        Object.assign(this.queryParams, searchParams)
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery(resetParams) {
      if (resetParams) {
        // 从JlSearchForm传来的重置参数
        Object.assign(this.queryParams, resetParams)
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange({ records }) {
      this.ids = records.map(item => item.id)
      this.single = records.length !== 1
      this.multiple = !records.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCity(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCity(this.form).then(response => {
              this.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addCity(this.form).then(response => {
              this.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除city编号为"' + ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delCity(ids)
      }).then(() => {
        this.getList()
        this.msgSuccess('删除成功')
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有city数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return exportCity(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    },
    /** 分页变化 */
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pageNum = currentPage
      this.queryParams.pageSize = pageSize
      this.getList()
    }
  }
}
</script>
