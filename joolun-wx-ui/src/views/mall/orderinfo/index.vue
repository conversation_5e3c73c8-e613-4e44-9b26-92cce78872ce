<template>
  <div class="app-container">
    <el-tabs v-model="status" type="card" @tab-click="handleClickStatus">
      <el-tab-pane name="-1">
        <span slot="label"><i class="el-icon-s-order" /> 全部订单</span>
      </el-tab-pane>
      <el-tab-pane name="0">
        <span slot="label"><i class="el-icon-bank-card" /> 待付款</span>
      </el-tab-pane>
      <el-tab-pane name="2">
        <span slot="label"><i class="el-icon-truck" /> 待使用</span>
      </el-tab-pane>
      <el-tab-pane name="3">
        <span slot="label"><i class="el-icon-document" /> 已完成</span>
      </el-tab-pane>
      <el-tab-pane name="4,6,7,8">
        <span slot="label"><i class="el-icon-warning" /> 退款/售后</span>
      </el-tab-pane>
      <el-tab-pane name="5">
        <span slot="label"><i class="el-icon-circle-close" /> 已取消</span>
      </el-tab-pane>
      <!-- <el-tab-pane name="6">
        <span slot="label"><i class="el-icon-circle-close" /> 已退款</span>
      </el-tab-pane>
      <el-tab-pane name="7">
        <span slot="label"><i class="el-icon-check" /> 同意退款</span>
      </el-tab-pane>
      <el-tab-pane name="8">
        <span slot="label"><i class="el-icon-close" /> 拒绝退款</span>
      </el-tab-pane> -->
    </el-tabs>

    <!-- 搜索区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" @submit.prevent="handleSearch">
        <el-form-item label="订单编号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单编号"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select v-model="searchForm.isPay" placeholder="请选择支付状态" clearable>
            <el-option label="已支付" value="1"/>
            <el-option label="未支付" value="0"/>
          </el-select>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="dateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
          <el-button icon="el-icon-download" @click="exportToExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      border
      style="width: 100%"
      height="calc(100vh - 300px)"
    >
      <el-table-column
        label="商品图片"
        align="center"
      >
        <template slot-scope="scope">
          <el-row v-for="(item, index) in scope.row.listOrderItem" :key="index">
            <img :src="item.picUrl" width="80" height="80" style="object-fit: cover;">
          </el-row>
        </template>
      </el-table-column>
      <el-table-column
        label="商品信息"
        align="center"
      >
        <template slot-scope="scope">
          <el-row
            v-for="(item, index) in scope.row.listOrderItem"
            :key="index"
            style="border:1px solid #eaeaea;padding: 5px;margin-bottom: 5px"
          >
            <el-col :span="16" style="text-align: left">
              <div class="spu-name">{{ item.spuName }}</div>
              <div class="spec-info">{{ item.specInfo }}</div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content" style="color:red;">￥{{ item.paymentPrice }}</div>
              <div class="grid-content">×{{ item.quantity }}人</div>
              <div v-if="item.status == '4'" class="grid-content"><el-tag type="warning">退款中</el-tag></div>
              <div v-if="item.status == '7'" class="grid-content"><el-tag type="success">同意退款</el-tag></div>
              <div v-if="item.status == '8'" class="grid-content"><el-tag type="danger">拒绝退款</el-tag></div>
              <div v-if="item.isRefund == '1'" class="grid-content" style="color:red;">已退款</div>
              <div v-if="item.status == '2' && scope.row.isPay == '1'" class="grid-content">
                <el-tag type="danger" @click="doOrderRefunds(item)">申请退款</el-tag>
              </div>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderNo"
        label="订单编号"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.orderNo }} </span>
          <el-button
            type="text"
            icon="el-icon-copy-document"
            @click="copyOrderNo(scope.row.orderNo)"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="状态"
        align="center"
      >
        <template slot-scope="scope">
          <div style="text-align: left">
            <div class="grid-content">订单状态：
              <el-tag
                :type="getStatusTagType(scope.row.status)"
                effect="dark"
                size="mini"
              >
                {{ scope.row.statusDesc }}
              </el-tag>
            </div>
            <div class="grid-content">支付状态：
              <el-tag
                :type="scope.row.isPay=='1' ? 'success' : 'danger'"
                effect="dark"
                size="mini"
              >
                {{ scope.row.isPay == '1' ? '已支付' : '未支付' }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="paymentTime"
        label="付款时间"
        align="center"
      />
      <el-table-column
        prop="salesPrice"
        label="订单金额"
        align="center"
      >
        <template slot-scope="scope">
          <div style="color: red">支付金额：￥{{ scope.row.paymentPrice }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="订单时间"
        align="center"
      >
        <template slot-scope="scope">
          <div>创建时间：{{ scope.row.createTime }}</div>
          <div v-if="scope.row.paymentTime">付款时间：{{ scope.row.paymentTime }}</div>
          <!-- <div v-if="scope.row.deliveryTime">发货时间：{{ scope.row.deliveryTime }}</div>
          <div v-if="scope.row.receiverTime">收货时间：{{ scope.row.receiverTime }}</div>
          <div v-if="scope.row.closingTime">成交时间：{{ scope.row.closingTime }}</div> -->
        </template>
      </el-table-column>
      <el-table-column
        prop="transactionId"
        label="支付流水号2"
        align="center"
      />
      <!-- <el-table-column
        prop="userMessage"
        label="买家留言"
        align="center"
      /> -->
      <el-table-column
        prop="remark"
        label="备注/拒绝退款原因"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.listOrderItem[0].remark }} </span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="checkPermi(['mall:orderinfo:get'])"
            icon="el-icon-edit"
            size="small"
            type="text"
            @click="openView(scope.row,scope.$index)"
          >
            订单详情
          </el-button>
          <!-- 发货功能暂时隐藏，因为STATUS_1("待发货")状态暂时不用 -->
          <!-- <el-button
            v-if="checkPermi(['mall:orderinfo:edit']) && scope.row.status == '1'"
            icon="el-icon-position"
            size="small"
            type="text"
            @click="showDialogLogistics(scope.row,scope.$index)"
          >
            发货
          </el-button> -->
          <el-button
            v-if="checkPermi(['mall:orderinfo:edit']) && scope.row.isPay == '0' && !scope.row.status"
            icon="el-icon-delete"
            size="small"
            type="text"
            @click="orderCancel(scope.row,scope.$index)"
          >
            取消
          </el-button>
          <!-- 退款管理操作：为退款中状态的订单项提供同意/拒绝操作 -->
          <template v-if="checkPermi(['mall:orderinfo:edit']) && hasRefundingItems(scope.row)">
            <el-button
              icon="el-icon-check"
              size="small"
              type="text"
              style="color: #67C23A;"
              @click="handleRefundApproval(scope.row, '7')"
            >
              同意退款
            </el-button>
            <el-button
              icon="el-icon-close"
              size="small"
              type="text"
              style="color: #F56C6C;"
              @click="handleRefundApproval(scope.row, '8')"
            >
              拒绝退款
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        background
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 订单详情弹窗 -->
    <OrderDetail
      :visible.sync="dialogView"
      :order-data="currentRow"
      @close="dialogView = false"
      @print="handlePrintOrder"
      @export="handleExportOrder"
      @contact="handleContactService"
    />

    <!-- 拒绝退款原因弹窗 -->
    <el-dialog title="拒绝退款" :visible.sync="dialogRefundReject" width="40%" @close="resetRefundRejectForm">
      <el-form ref="refundRejectForm" :model="refundRejectForm" :rules="refundRejectRules" label-width="100px">
        <el-form-item label="拒绝原因" prop="rejectReason">
          <el-input
            v-model="refundRejectForm.rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝退款的原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelRefundReject">取 消</el-button>
        <el-button type="primary" @click="confirmRefundReject">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 发货弹窗 -->
    <el-dialog title="发货" :visible.sync="dialogLogistics" width="30%">
      <el-form ref="logisticsForm" :model="logisticsForm" :rules="logisticsRules" label-width="100px">
        <el-form-item label="收货人名字" prop="userName">
          <el-input v-model="logisticsForm.userName" readonly/>
        </el-form-item>
        <el-form-item label="电话号码" prop="telNum">
          <el-input v-model="logisticsForm.telNum" readonly/>
        </el-form-item>
        <el-form-item label="收货地址" prop="address">
          <el-input v-model="logisticsForm.address" type="textarea" readonly/>
        </el-form-item>
        <el-form-item label="快递公司" prop="logistics">
          <el-select v-model="logisticsForm.logistics" placeholder="请选择快递公司">
            <el-option
              v-for="item in logisticsOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" prop="logisticsNo">
          <el-input v-model="logisticsForm.logisticsNo"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogLogistics = false">取 消</el-button>
        <el-button type="primary" @click="submitLogistics">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import {
  getPage,
  getObj,
  putObj,
  orderCancel,
  doOrderRefunds,
  exportOrderInfo
} from '@/api/mall/orderinfo'
import OrderDetail from '@/components/OrderDetail/OrderDetail.vue'

export default {
  name: 'OrderInfo',
  components: {
    OrderDetail
  },
  data() {
    return {
      checkPermi: checkPermi,
      date: [],
      status: '-1',
      tableData: [],
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 20,
        ascs: [],
        descs: 'create_time'
      },
      searchForm: {
        orderNo: '',
        isPay: ''
      },
      paramsSearch: {},
      tableLoading: false,
      dialogLogistics: false,
      logisticsForm: {
        row: {},
        logistics: null,
        logisticsNo: null,
        address: null,
        userName: null,
        telNum: null
      },
      logisticsRules: {
        logistics: [
          { required: true, message: '请选择快递公司', trigger: 'change' }
        ],
        logisticsNo: [
          { required: true, message: '请输入快递单号', trigger: 'blur' }
        ]
      },
      logisticsOptions: [],
      dialogView: false,
      currentRow: {},
      // 拒绝退款相关
      dialogRefundReject: false,
      refundRejectForm: {
        rejectReason: '',
        currentRow: null,
        newStatus: ''
      },
      refundRejectRules: {
        rejectReason: [
          { required: true, message: '请输入拒绝退款的原因', trigger: 'blur' },
          { min: 5, message: '拒绝原因至少需要5个字', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getPage(this.page)
  },
  methods: {
    // 搜索方法
    handleSearch() {
      this.paramsSearch = this.searchForm
      this.page.currentPage = 1
      this.getPage(this.page)
    },
    resetSearch() {
      this.searchForm = {
        orderNo: '',
        isPay: ''
      }
      this.date = []
      this.paramsSearch = {}
      this.getPage(this.page)
    },
    // 分页方法
    handleSizeChange(val) {
      this.page.pageSize = val
      this.getPage(this.page)
    },
    handleCurrentChange(val) {
      this.page.currentPage = val
      this.getPage(this.page)
    },
    // 导出方法
    exportToExcel() {
      const queryParams = this.paramsSearch
      this.$confirm('是否导出当前查询结果?', '提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return exportOrderInfo(queryParams)
      }).then(response => {
        this.download(response.msg)
      })
    },
    // 其他方法保持不变
    handleClickStatus(tab) {
      this.status = tab.name
      this.page.currentPage = 1
      this.getPage(this.page)
    },
    openView(row, index) {
      this.tableLoading = true
      getObj(row.id).then(response => {
        this.currentRow = response.data
        // 确保userInfo数据存在
        if (!this.currentRow.userInfo) {
          this.currentRow.userInfo = {}
        }
        this.dialogView = true
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    orderCancel(row, index) {
      this.$confirm('是否确认取消此订单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return orderCancel(row.id)
      }).then(() => {
        this.$message({
          showClose: true,
          message: '取消成功',
          type: 'success'
        })
        this.getPage(this.page)
      })
    },
    showDialogLogistics(row, index) {
      this.logisticsForm.row = row
      this.logisticsForm.address = row.orderLogistics.address
      this.logisticsForm.userName = row.orderLogistics.userName
      this.logisticsForm.telNum = row.orderLogistics.telNum
      this.dialogLogistics = true
    },
    submitLogistics() {
      this.$refs.logisticsForm.validate((valid) => {
        if (valid) {
          const row = this.logisticsForm.row
          row.status = '2'
          row.logistics = this.logisticsForm.logistics
          row.logisticsNo = this.logisticsForm.logisticsNo
          putObj(row).then(() => {
            this.$message({
              showClose: true,
              message: '发货成功',
              type: 'success'
            })
            this.dialogLogistics = false
            this.getPage(this.page)
          })
        }
      })
    },
    dateChange(date) {
      if (date) {
        this.date = date
      } else {
        this.date = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      this.tableLoading = true
      const queryParams = {
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
        status: this.status !== '-1' ? this.status : null,
        beginTime: this.date[0],
        endTime: this.date[1]
      }
      // 合并搜索参数，过滤掉空值
      Object.assign(queryParams, params, this.paramsSearch)
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === '' || queryParams[key] === null || queryParams[key] === undefined) {
          delete queryParams[key]
        }
      })
      getPage(queryParams).then(response => {
        this.tableData = response.data.records
        this.page.total = response.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    doOrderRefunds(item) {
      // Implementation of doOrderRefunds method
    },
    copyOrderNo(orderNo) {
      const input = document.createElement('input')
      input.value = orderNo
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$message({
        message: '订单号已复制到剪贴板',
        type: 'success',
        duration: 1500
      })
    },
    getStatusTagType(status) {
      // 根据订单状态返回对应的标签类型
      switch (status) {
        case '0': // 待付款
          return 'info'
        case '2': // 待使用
        case '3': // 已完成
          return 'success'
        case '4': // 退款中
          return 'warning'
        case '5': // 已取消
        case '8': // 拒绝退款
          return 'danger'
        case '6': // 已退款
        case '7': // 同意退款
          return 'success'
        default:
          return 'info'
      }
    },
    hasRefundingItems(row) {
      // 检查订单是否有退款中的订单项
      return row.listOrderItem && row.listOrderItem.some(item => item.status === '4')
    },
    handleRefundApproval(row, newStatus) {
      // 处理退款审批
      const statusText = newStatus === '7' ? '同意退款' : '拒绝退款'

      if (newStatus === '8') {
        // 拒绝退款需要填写原因
        this.refundRejectForm.currentRow = row
        this.refundRejectForm.newStatus = newStatus
        this.refundRejectForm.rejectReason = ''
        this.dialogRefundReject = true
      } else {
        // 同意退款直接确认
        this.$confirm(`确认${statusText}？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.executeRefundApproval(row, newStatus, '')
        }).catch(() => {
          // 用户取消操作
        })
      }
    },
    // 确认拒绝退款
    confirmRefundReject() {
      this.$refs.refundRejectForm.validate((valid) => {
        if (valid) {
          this.executeRefundApproval(
            this.refundRejectForm.currentRow,
            this.refundRejectForm.newStatus,
            this.refundRejectForm.rejectReason
          )
          this.resetRefundRejectForm()
        }
      })
    },
    // 取消拒绝退款
    cancelRefundReject() {
      this.resetRefundRejectForm()
    },
    // 重置拒绝退款表单
    resetRefundRejectForm() {
      this.dialogRefundReject = false
      this.refundRejectForm.rejectReason = ''
      this.refundRejectForm.currentRow = null
      this.refundRejectForm.newStatus = ''
      // 清除表单验证
      if (this.$refs.refundRejectForm) {
        this.$refs.refundRejectForm.clearValidate()
      }
    },
    // 执行退款审批
    executeRefundApproval(row, newStatus, rejectReason = '') {
      const statusText = newStatus === '7' ? '同意退款' : '拒绝退款'
      // 找到退款中的订单项并更新状态
      const refundingItems = row.listOrderItem.filter(item => item.status === '4')

      // 这里需要调用后端API来处理退款审批
      const promises = refundingItems.map(item => {
        const updateData = {
          id: item.id,
          status: newStatus,
          remark: rejectReason
        }
        return doOrderRefunds(updateData) // 需要确保这个API方法存在并正确实现
      })

      Promise.all(promises).then(() => {
        this.$message({
          showClose: true,
          message: `${statusText}成功`,
          type: 'success'
        })
        this.getPage(this.page)
      }).catch(error => {
        this.$message({
          showClose: true,
          message: `${statusText}失败: ${error.message || '未知错误'}`,
          type: 'error'
        })
      })
    },
    // OrderDetail组件事件处理方法
    handlePrintOrder(orderData) {
      // 打印订单逻辑
      console.log('打印订单:', orderData)
      this.$message({
        message: '打印功能待实现',
        type: 'info'
      })
    },
    handleExportOrder(orderData) {
      // 导出订单详情逻辑
      console.log('导出订单详情:', orderData)
      this.$message({
        message: '导出功能待实现',
        type: 'info'
      })
    },
    handleContactService(orderData) {
      // 联系客服逻辑
      console.log('联系客服:', orderData)
      this.$message({
        message: '客服功能待实现',
        type: 'info'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
.pagination-container {
  padding: 32px 16px;
}
.spu-name {
  font-size: 13px;
}
.spec-info {
  margin-top: 10px;
  font-size: 12px;
  color: #7b7b7b;
}
.app-container {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.reject-reason {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
  line-height: 1.2;
  background-color: #FEF0F0;
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #F56C6C;

  .reason-text {
    cursor: pointer;
    color: #F56C6C;
    font-weight: 500;
    &:hover {
      color: #E6A23C;
    }
  }
}
</style>
