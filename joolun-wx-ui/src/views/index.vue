<template>
  <div class="dashboard-container">
    <el-row :gutter="20" class="data-cards">
      <el-col :span="6" v-for="(item, index) in cardData" :key="'card-' + index">
        <div class="data-card" :style="{ borderTopColor: item.color }">
          <div class="card-title">{{ item.title }}</div>
          <div class="card-value">{{ item.count }}{{ item.key }}</div>
          <div class="card-subtitle">{{ item.subtitle }}</div>
          <div class="card-footer">{{ item.text }}: {{ item.allcount }}{{ item.key }}</div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <div class="chart-wrapper">
          <div class="chart-title">订单趋势</div>
          <v-chart class="chart" :options="orderTrendOption" autoresize />
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-wrapper">
          <div class="chart-title">订单金额分布</div>
          <v-chart class="chart" :options="orderAmountOption" autoresize />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getOrderStat } from '@/api/mall/orderinfo'
import VChart from 'vue-echarts'
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/title'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/grid'

export default {
  components: {
    VChart
  },
  data() {
    return {
      sumData: {},
      paramsSearch: {},
      tableLoading: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 20,
        ascs: [],
        descs: 'create_time'
      },
      cardData: [
        {
          title: '订单总数',
          subtitle: '实时',
          count: 0,
          allcount: 0,
          text: '已取消订单数量',
          color: 'rgb(27, 201, 142)',
          key: '个'
        },
        {
          title: '待使用订单数',
          subtitle: '实时',
          count: 0,
          allcount: 0,
          text: '已退款订单数',
          color: 'rgb(178, 159, 255)',
          key: '个'
        },
        {
          title: '订单已支付总金额',
          subtitle: '实时',
          count: 0,
          text: '订单已退款金额',
          color: 'rgb(230, 71, 88)',
          key: '元'
        },
        {
          title: '订单已完成收入',
          subtitle: '实时',
          count: 0,
          color: 'rgb(178, 159, 255)',
          key: '元'
        }
      ],
      orderTrendOption: {
        title: {
          text: '近7天订单趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '订单数',
            type: 'line',
            smooth: true,
            data: [120, 132, 101, 134, 90, 230, 210],
            itemStyle: {
              color: '#1bc98e'
            }
          }
        ]
      },
      orderAmountOption: {
        title: {
          text: '订单金额分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}元 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '订单金额',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 0, name: '已完成' },
              { value: 0, name: '待使用' },
              { value: 0, name: '已退款' },
              { value: 0, name: '已取消' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {c}元'
            }
          }
        ]
      }
    }
  },
  created() {
    this.getPageData(this.page)
  },
  methods: {
    getPageData(page, params) {
      this.tableLoading = true
      getOrderStat(Object.assign({
        pageNum: page.pageNum,
        pageSize: page.pageSize
      }, params, this.paramsSearch)).then(response => {
        const data = response.data.records
        this.sumData = data
        this.cardData[0].count = data[0].ddzs
        this.cardData[0].allcount = data[0].yqxs
        this.cardData[1].count = data[0].dsys
        this.cardData[1].allcount = data[0].ytks
        this.cardData[2].count = data[0].yzf
        this.cardData[2].allcount = data[0].ytkje
        this.cardData[3].count = data[0].ywcje

        // 更新饼图数据
        this.orderAmountOption.series[0].data = [
          { value: data[0].ywcje || 0, name: '已完成' },
          { value: data[0].dsys || 0, name: '待使用' },
          { value: data[0].ytkje || 0, name: '已退款' },
          { value: data[0].yqxs || 0, name: '已取消' }
        ]

        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 84px);

  .data-cards {
    margin-bottom: 20px;
  }

  .data-card {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-top: 4px solid;
    height: 160px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .card-title {
      font-size: 16px;
      color: #606266;
    }

    .card-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin: 10px 0;
    }

    .card-subtitle {
      font-size: 14px;
      color: #909399;
    }

    .card-footer {
      font-size: 14px;
      color: #909399;
      margin-top: 10px;
    }
  }

  .chart-row {
    margin-bottom: 20px;
  }

  .chart-wrapper {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
    }

    .chart {
      height: 350px;
    }
  }
}
</style>
